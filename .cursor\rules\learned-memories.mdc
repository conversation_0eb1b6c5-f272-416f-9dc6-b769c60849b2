---
description: 
globs: 
alwaysApply: true
---
# Project Memory

This file stores project-specific knowledge, conventions, and user preferences learned by the AI assistant.

## User Preferences

- Never add comments to code
- Always follow object oriented principles and type-safety
- Always directly implement solutions without asking for approval

## Technical Decisions

- **Helicopter Navigation**: Helicopters should face their movement direction using simplified 3D rotation logic. No complex model rotation preservation needed.
- **Player Positioning**: All players (local and replicated) must maintain `transform.localPosition = Vector3.zero` relative to their spawn points at all times.
- **Helicopter Attachment**: Players must be parented to helicopter during flight sequences to prevent falling off. Detachment occurs when helicopter lands.
- **Multiplayer Helicopter System**: Uses NetworkedHelicopterManager with RPCs and SyncVars to coordinate helicopter attachment across all clients for actual ForestPlayer instances.

## Project Conventions

- **Helicopter Classes**: Base `Helicopter.cs` handles all rotation and movement logic. Derived classes should not override rotation behavior unless absolutely necessary.
- **Player Spawning**: Players are spawned as children of spawn points with `localPosition = Vector3.zero` and `localRotation = Quaternion.identity`.
- **Player Rotation**: Player model rotation uses Y-axis (yaw) for visual direction. Transform rotation uses `Quaternion.Euler(0f, yawAngle, 0f)` format.
- **Replicated Players**: Use visual model rotation for network synchronization, maintaining local position constraints.
- **Helicopter Attachment System**: 
  - `AttachToHelicopter()` parents players to helicopter transform
  - `DetachFromHelicopter()` returns players to original parent
  - `ResetLocalTransform()` enforces zero local position and identity rotation
  - Attachment state prevents mouse look and local transform updates
- **Multiplayer Player Management**:
  - ForestPlayer (NetworkBehaviour) is the actual multiplayer player class
  - ForestIntroPlayer/ForestIntroReplicatedPlayer are intro sequence only
  - NetworkedHelicopterManager handles all ForestPlayer helicopter attachment via RPCs
  - Both systems work in parallel for complete coverage
