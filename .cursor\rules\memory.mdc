---
description: 
globs: 
alwaysApply: true
---
# Project Memory

This file stores project-specific knowledge, conventions, and user preferences learned by the AI assistant.

## User Preferences
# Project Memory

This file stores project-specific knowledge, conventions, and user preferences learned by the AI assistant.

## User Preferences

- Never add comments to code
- Always follow object oriented principles and type-safety
- Always directly implement solutions without asking for approval

## AI Working Rules

- **Context First Principle**: When working with players or specific game systems, ALWAYS check higher level manager classes first for context before making changes:
  - For player-related work: Check ForestPlayerManager, MyNetworkManager, and related manager classes
  - For game systems: Check the relevant SystemManager or GameManager classes
  - For UI/Camera systems: Check the relevant UI/Camera manager classes
  - Understanding existing setup and references prevents duplicate work and ensures proper integration
  - Context is key - never assume setup, always investigate existing architecture first

## Technical Decisions

- **Helicopter Navigation**: Helicopters should face their movement direction using simplified 3D rotation logic. No complex model rotation preservation needed.
- **Player Positioning**: ALL players (local, replicated, and networked) must maintain `transform.localPosition = Vector3.zero` relative to their spawn points at ALL times. This must be enforced every frame. Never change player scale.
- **Player Rotation System**: ALL players spawn with `localRotation = Quaternion.Euler(0f, 0f, 0f)` to align with spawn points. Player model rotation uses Y-axis for visual direction via mouse look. Transform enforcer maintains position/rotation constraints.
- **Multiplayer Camera System**: ForestMultiplayerCameraManager creates a CamHolder GameObject at spawn point with Y offset 2.665, then parents Camera.main to this CamHolder. CamHolder handles vertical rotation (X-axis), player transform handles horizontal rotation (Y-axis).
- **Helicopter Attachment**: Players must be parented to helicopter during flight sequences to prevent falling off. Detachment occurs when helicopter lands.
- **Multiplayer Helicopter System**: Uses NetworkedHelicopterManager with RPCs and SyncVars to coordinate helicopter attachment across all clients for actual ForestPlayer instances.
- **NetworkedHelicopterManager Setup**: Must be server-only NetworkIdentity. GameObject gets disabled on clients but this is normal Mirror behavior for server-only objects.

## Project Conventions

- **Helicopter Classes**: Base `Helicopter.cs` handles all rotation and movement logic. Derived classes should not override rotation behavior unless absolutely necessary.
- **Player Spawning**: ALL players spawn with zero local position and rotation, then align with spawn points. No rotation offsets applied at spawn.
- **Player Rotation**: Player model rotation uses Y-axis (yaw) for visual direction. All ResetLocalTransform() methods enforce `Quaternion.Euler(0f, 0f, 0f)`. ForestPlayer syncs Y rotation via SyncVar for multiplayer.
- **Camera Management**: ForestMultiplayerCameraManager creates CamHolder at spawn point Y offset 2.665, Camera.main parents to CamHolder. ForestPlayer.AssignMainCamera() finds CamHolder and assigns it for mouse look controls. ProcessPlayerLook() uses camHolder for vertical rotation and player transform for horizontal rotation.
- **Replicated Players**: Use visual model rotation for network synchronization, maintaining local position/rotation constraints.
- **Helicopter Attachment System**: 
  - `AttachToHelicopter()` parents players to helicopter transform
  - `DetachFromHelicopter()` returns players to original parent
  - `ResetLocalTransform()` enforces zero local position and identity rotation
  - Attachment state prevents mouse look and local transform updates
- **Multiplayer Player Management**:
  - ForestPlayer (NetworkBehaviour) is the actual multiplayer player class
  - ForestIntroPlayer/ForestIntroReplicatedPlayer are intro sequence only
  - NetworkedHelicopterManager handles all ForestPlayer helicopter attachment via RPCs
  - ForestMultiplayerCameraManager handles CamHolder creation and Camera.main positioning
  - MyNetworkManager assigns PlayerSlotId based on connection order
  - Both systems work in parallel for complete coverage

## Critical Multiplayer Setup Issues Resolved

- **Missing NetworkManager**: Scene must contain a GameObject with MyNetworkManager component, LobbyPlayerList component, and a Transport (KcpTransport)
- **Player Prefab Assignment**: NetworkManager.playerPrefab must be assigned (use ClientPlayerPrefab.prefab)
- **Spawn Prefabs**: NetworkManager.spawnPrefabs must include ForestIntroHelicopter
- **Helicopter Deactivation Issue**: Was caused by NetworkedHelicopterManager singleton logic destroying duplicates. Fixed by ensuring only one instance exists and proper component references.
- **Player Parenting Issue**: SOLVED by creating standalone spawn points and modifying attachment system:
  1. Created dedicated spawn points: SpawnPoint_LocalPlayer, SpawnPoint_ReplicatedPlayer_0/1/2
  2. Updated ForestPlayerManager to reference these new spawn points  
  3. Modified player attachment scripts to not change parenting during helicopter attachment
  4. Players now remain children of their designated spawn points with localPosition = Vector3.zero always
  5. Local player parenting works correctly; replicated players need scene restart to take effect

## Solution Summary: Player Spawn Point Parenting

**Problem**: Players were being parented to helicopter's internal spawn points instead of dedicated scene spawn points.

**Root Cause**: ForestPlayerManager was referencing helicopter's internal SpawnPoint transforms instead of standalone spawn points.

**Solution**:
1. Created standalone spawn point GameObjects in scene at specific world positions
2. Updated ForestPlayerManager.localPlayerSpawnPoint and replicatedPlayerSpawnPoints to reference new spawn points  
3. Modified player attachment scripts to not change parenting during helicopter attachment
4. Result: Players remain children of their designated spawn points with localPosition = Vector3.zero always

**Status**: Local player ✅ working correctly. Replicated players need fresh scene start to take effect due to runtime reference caching.

## Transform Constraint System

**Critical Requirements**:
- ALL players must have `transform.localPosition = Vector3.zero` at ALL times
- ALL players must have `transform.localRotation = Quaternion.Euler(0f, 0f, zRotation)` (only Z rotation allowed)
- Scale must NEVER be modified
- These constraints must be enforced every frame via `ResetLocalTransform()`
- Mouse look and player facing direction uses only Z-axis rotation

## Issue Fixes

- **Helicopter Deactivation Issue**: Caused by missing NetworkedHelicopterManager setup. Fixed by adding NetworkedHelicopterManager GameObject with NetworkIdentity, adding NetworkIdentity to ForestIntroHelicopter, configuring proper references in ForestGameManager, and adding both to NetworkManager spawn prefabs.

## Critical NetworkedHelicopterManager Setup

**CRITICAL**: NetworkedHelicopterManager must be configured as a scene object, NOT a spawnable prefab:

1. **NetworkIdentity Configuration**: Must have `serverOnly: true` checked in Inspector
2. **Spawn Prefabs**: Do NOT add NetworkedHelicopterManager to NetworkManager.spawnPrefabs list
3. **Scene Placement**: Must exist directly in scene at game start (not spawned during runtime)
4. **Authority**: Gets server authority automatically when server starts because it's a scene object
5. **Common Error**: Adding NetworkIdentity with serverOnly=false causes GameObject to get disabled when game runs

**Why This Works**: Scene objects with NetworkIdentity get automatic server authority when server starts. Spawnable prefabs require explicit spawning and authority assignment.

## Critical Issues Discovered

- **Helicopter Deactivation Bug**: ForestIntroHelicopter GameObject gets deactivated (`activeSelf = false`) during runtime after Awake() but before Start(). This causes:
  - Start() method never executes
  - StartIntroSequence() never gets called
  - All attached players become inactive as children
  - Helicopter appears "turned off" in game
  - NetworkedHelicopterManager shows `isServer: false, isClient: false` indicating network authority issues
- **Root Cause**: Likely related to NetworkIdentity/Mirror networking system deactivating objects that don't have proper server authority
- **Investigation Method**: Use Unity MCP server to check GameObject properties during runtime: `activeInHierarchy`, `activeSelf`, `didStart`, `didAwake` properties
