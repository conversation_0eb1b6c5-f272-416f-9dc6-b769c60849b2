%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!29 &1
OcclusionCullingSettings:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_OcclusionBakeSettings:
    smallestOccluder: 5
    smallestHole: 0.25
    backfaceThreshold: 100
  m_SceneGUID: 00000000000000000000000000000000
  m_OcclusionCullingData: {fileID: 0}
--- !u!104 &2
RenderSettings:
  m_ObjectHideFlags: 0
  serializedVersion: 10
  m_Fog: 0
  m_FogColor: {r: 0.5, g: 0.5, b: 0.5, a: 1}
  m_FogMode: 3
  m_FogDensity: 0.01
  m_LinearFogStart: 0
  m_LinearFogEnd: 300
  m_AmbientSkyColor: {r: 0.212, g: 0.227, b: 0.259, a: 1}
  m_AmbientEquatorColor: {r: 0.114, g: 0.125, b: 0.133, a: 1}
  m_AmbientGroundColor: {r: 0.047, g: 0.043, b: 0.035, a: 1}
  m_AmbientIntensity: 1
  m_AmbientMode: 0
  m_SubtractiveShadowColor: {r: 0.42, g: 0.478, b: 0.627, a: 1}
  m_SkyboxMaterial: {fileID: 0}
  m_HaloStrength: 0.5
  m_FlareStrength: 1
  m_FlareFadeSpeed: 3
  m_HaloTexture: {fileID: 0}
  m_SpotCookie: {fileID: 10001, guid: 0000000000000000e000000000000000, type: 0}
  m_DefaultReflectionMode: 0
  m_DefaultReflectionResolution: 128
  m_ReflectionBounces: 1
  m_ReflectionIntensity: 1
  m_CustomReflection: {fileID: 0}
  m_Sun: {fileID: 0}
  m_UseRadianceAmbientProbe: 0
--- !u!157 &3
LightmapSettings:
  m_ObjectHideFlags: 0
  serializedVersion: 13
  m_BakeOnSceneLoad: 0
  m_GISettings:
    serializedVersion: 2
    m_BounceScale: 1
    m_IndirectOutputScale: 1
    m_AlbedoBoost: 1
    m_EnvironmentLightingMode: 0
    m_EnableBakedLightmaps: 1
    m_EnableRealtimeLightmaps: 0
  m_LightmapEditorSettings:
    serializedVersion: 12
    m_Resolution: 2
    m_BakeResolution: 40
    m_AtlasSize: 1024
    m_AO: 0
    m_AOMaxDistance: 1
    m_CompAOExponent: 1
    m_CompAOExponentDirect: 0
    m_ExtractAmbientOcclusion: 0
    m_Padding: 2
    m_LightmapParameters: {fileID: 0}
    m_LightmapsBakeMode: 1
    m_TextureCompression: 1
    m_ReflectionCompression: 2
    m_MixedBakeMode: 2
    m_BakeBackend: 1
    m_PVRSampling: 1
    m_PVRDirectSampleCount: 32
    m_PVRSampleCount: 512
    m_PVRBounces: 2
    m_PVREnvironmentSampleCount: 256
    m_PVREnvironmentReferencePointCount: 2048
    m_PVRFilteringMode: 1
    m_PVRDenoiserTypeDirect: 1
    m_PVRDenoiserTypeIndirect: 1
    m_PVRDenoiserTypeAO: 1
    m_PVRFilterTypeDirect: 0
    m_PVRFilterTypeIndirect: 0
    m_PVRFilterTypeAO: 0
    m_PVREnvironmentMIS: 1
    m_PVRCulling: 1
    m_PVRFilteringGaussRadiusDirect: 1
    m_PVRFilteringGaussRadiusIndirect: 5
    m_PVRFilteringGaussRadiusAO: 2
    m_PVRFilteringAtrousPositionSigmaDirect: 0.5
    m_PVRFilteringAtrousPositionSigmaIndirect: 2
    m_PVRFilteringAtrousPositionSigmaAO: 1
    m_ExportTrainingData: 0
    m_TrainingDataDestination: TrainingData
    m_LightProbeSampleCountMultiplier: 4
  m_LightingDataAsset: {fileID: 0}
  m_LightingSettings: {fileID: 0}
--- !u!196 &4
NavMeshSettings:
  serializedVersion: 2
  m_ObjectHideFlags: 0
  m_BuildSettings:
    serializedVersion: 3
    agentTypeID: 0
    agentRadius: 0.5
    agentHeight: 2
    agentSlope: 45
    agentClimb: 0.4
    ledgeDropHeight: 0
    maxJumpAcrossDistance: 0
    minRegionArea: 2
    manualCellSize: 0
    cellSize: 0.16666667
    manualTileSize: 0
    tileSize: 256
    buildHeightMesh: 0
    maxJobWorkers: 0
    preserveTilesOutsideBounds: 0
    debug:
      m_Flags: 0
  m_NavMeshData: {fileID: 0}
--- !u!1 &47753183
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 47753184}
  m_Layer: 0
  m_Name: HelicopterSittingPlayerPrefab_2
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &47753184
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 47753183}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: 0.7071067, z: 0.7071068, w: 0}
  m_LocalPosition: {x: 0.022516789, y: -0.013273006, z: -0.01522}
  m_LocalScale: {x: 0.010000002, y: 0.0100000035, z: 0.010000002}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 141025193}
  m_Father: {fileID: 1238138302}
  m_LocalEulerAnglesHint: {x: 0, y: 180, z: 0}
--- !u!1 &90960997
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 90960998}
  - component: {fileID: 90961001}
  - component: {fileID: 90961000}
  - component: {fileID: 90960999}
  - component: {fileID: 90961002}
  m_Layer: 0
  m_Name: HelicopterFloorCollider
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &90960998
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 90960997}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0.0013769533, y: 0.00021515026, z: -0.014956994}
  m_LocalScale: {x: 0.14129893, y: 0.04464409, z: 0.00027566476}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 666247744}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!65 &90960999
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 90960997}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 3
  m_Size: {x: 1, y: 1, z: 1}
  m_Center: {x: 0, y: 0, z: 0}
--- !u!23 &90961000
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 90960997}
  m_Enabled: 0
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 73c176f402d2c2f4d929aa5da7585d17, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!33 &90961001
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 90960997}
  m_Mesh: {fileID: 10202, guid: 0000000000000000e000000000000000, type: 0}
--- !u!114 &90961002
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 90960997}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 7a5ac11cc976e418e8d13136b07e1f52, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_SerializedVersion: 0
  m_AgentTypeID: 0
  m_CollectObjects: 0
  m_Size: {x: 10, y: 10, z: 10}
  m_Center: {x: 0, y: 2, z: 0}
  m_LayerMask:
    serializedVersion: 2
    m_Bits: 4294967295
  m_UseGeometry: 0
  m_DefaultArea: 0
  m_GenerateLinks: 0
  m_IgnoreNavMeshAgent: 1
  m_IgnoreNavMeshObstacle: 1
  m_OverrideTileSize: 0
  m_TileSize: 256
  m_OverrideVoxelSize: 0
  m_VoxelSize: 0.16666667
  m_MinRegionArea: 2
  m_NavMeshData: {fileID: 0}
  m_BuildHeightMesh: 0
--- !u!1 &94558006
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 94558008}
  - component: {fileID: 94558007}
  m_Layer: 0
  m_Name: Volume
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!114 &94558007
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 94558006}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 172515602e62fb746b5d573b38a5fe58, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_IsGlobal: 1
  priority: 0
  blendDistance: 0
  weight: 0
  sharedProfile: {fileID: 11400000, guid: 121118a5bbe391b429f6b831506c9323, type: 2}
--- !u!4 &94558008
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 94558006}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 402.58572, y: 4.9990277, z: 40.769726}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &99197032
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 99197033}
  - component: {fileID: 99197035}
  - component: {fileID: 99197034}
  m_Layer: 5
  m_Name: MasterVolumeSettingLabelMax
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &99197033
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 99197032}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 2120279515}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0.5, y: 0.5}
  m_AnchorMax: {x: 0.5, y: 0.5}
  m_AnchoredPosition: {x: 148.3, y: 0}
  m_SizeDelta: {x: 100, y: 100}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &99197034
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 99197032}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f4688fdb7df04437aeb418b961361dc5, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_text: MAX
  m_isRightToLeft: 0
  m_fontAsset: {fileID: 11400000, guid: 8f586378b4e144a9851e7b34d9b748ee, type: 2}
  m_sharedMaterial: {fileID: 2180264, guid: 8f586378b4e144a9851e7b34d9b748ee, type: 2}
  m_fontSharedMaterials: []
  m_fontMaterial: {fileID: 0}
  m_fontMaterials: []
  m_fontColor32:
    serializedVersion: 2
    rgba: 4294967295
  m_fontColor: {r: 1, g: 1, b: 1, a: 1}
  m_enableVertexGradient: 0
  m_colorMode: 3
  m_fontColorGradient:
    topLeft: {r: 1, g: 1, b: 1, a: 1}
    topRight: {r: 1, g: 1, b: 1, a: 1}
    bottomLeft: {r: 1, g: 1, b: 1, a: 1}
    bottomRight: {r: 1, g: 1, b: 1, a: 1}
  m_fontColorGradientPreset: {fileID: 0}
  m_spriteAsset: {fileID: 0}
  m_tintAllSprites: 0
  m_StyleSheet: {fileID: 0}
  m_TextStyleHashCode: -1183493901
  m_overrideHtmlColors: 0
  m_faceColor:
    serializedVersion: 2
    rgba: 4294967295
  m_fontSize: 20
  m_fontSizeBase: 20
  m_fontWeight: 400
  m_enableAutoSizing: 0
  m_fontSizeMin: 18
  m_fontSizeMax: 72
  m_fontStyle: 0
  m_HorizontalAlignment: 2
  m_VerticalAlignment: 512
  m_textAlignment: 65535
  m_characterSpacing: 0
  m_wordSpacing: 0
  m_lineSpacing: 0
  m_lineSpacingMax: 0
  m_paragraphSpacing: 0
  m_charWidthMaxAdj: 0
  m_TextWrappingMode: 0
  m_wordWrappingRatios: 0.4
  m_overflowMode: 0
  m_linkedTextComponent: {fileID: 0}
  parentLinkedComponent: {fileID: 0}
  m_enableKerning: 0
  m_ActiveFontFeatures: 6e72656b
  m_enableExtraPadding: 0
  checkPaddingRequired: 0
  m_isRichText: 1
  m_EmojiFallbackSupport: 1
  m_parseCtrlCharacters: 1
  m_isOrthographic: 1
  m_isCullingEnabled: 0
  m_horizontalMapping: 0
  m_verticalMapping: 0
  m_uvLineOffset: 0
  m_geometrySortingOrder: 0
  m_IsTextObjectScaleStatic: 0
  m_VertexBufferAutoSizeReduction: 0
  m_useMaxVisibleDescender: 1
  m_pageToDisplay: 1
  m_margin: {x: 0, y: 0, z: 0, w: 0}
  m_isUsingLegacyAnimationComponent: 0
  m_isVolumetricText: 0
  m_hasFontAssetChanged: 0
  m_baseMaterial: {fileID: 0}
  m_maskOffset: {x: 0, y: 0, z: 0, w: 0}
--- !u!222 &99197035
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 99197032}
  m_CullTransparentMesh: 1
--- !u!1 &109180143
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 109180144}
  - component: {fileID: 109180146}
  - component: {fileID: 109180145}
  m_Layer: 5
  m_Name: AmbienceVolumeSettingLabelMin
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &109180144
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 109180143}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 954295166}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0.5, y: 0.5}
  m_AnchorMax: {x: 0.5, y: 0.5}
  m_AnchoredPosition: {x: -153.9, y: 0}
  m_SizeDelta: {x: 100, y: 100}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &109180145
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 109180143}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f4688fdb7df04437aeb418b961361dc5, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_text: MUTE
  m_isRightToLeft: 0
  m_fontAsset: {fileID: 11400000, guid: 8f586378b4e144a9851e7b34d9b748ee, type: 2}
  m_sharedMaterial: {fileID: 2180264, guid: 8f586378b4e144a9851e7b34d9b748ee, type: 2}
  m_fontSharedMaterials: []
  m_fontMaterial: {fileID: 0}
  m_fontMaterials: []
  m_fontColor32:
    serializedVersion: 2
    rgba: 4294967295
  m_fontColor: {r: 1, g: 1, b: 1, a: 1}
  m_enableVertexGradient: 0
  m_colorMode: 3
  m_fontColorGradient:
    topLeft: {r: 1, g: 1, b: 1, a: 1}
    topRight: {r: 1, g: 1, b: 1, a: 1}
    bottomLeft: {r: 1, g: 1, b: 1, a: 1}
    bottomRight: {r: 1, g: 1, b: 1, a: 1}
  m_fontColorGradientPreset: {fileID: 0}
  m_spriteAsset: {fileID: 0}
  m_tintAllSprites: 0
  m_StyleSheet: {fileID: 0}
  m_TextStyleHashCode: -1183493901
  m_overrideHtmlColors: 0
  m_faceColor:
    serializedVersion: 2
    rgba: 4294967295
  m_fontSize: 20
  m_fontSizeBase: 20
  m_fontWeight: 400
  m_enableAutoSizing: 0
  m_fontSizeMin: 18
  m_fontSizeMax: 72
  m_fontStyle: 0
  m_HorizontalAlignment: 2
  m_VerticalAlignment: 512
  m_textAlignment: 65535
  m_characterSpacing: 0
  m_wordSpacing: 0
  m_lineSpacing: 0
  m_lineSpacingMax: 0
  m_paragraphSpacing: 0
  m_charWidthMaxAdj: 0
  m_TextWrappingMode: 0
  m_wordWrappingRatios: 0.4
  m_overflowMode: 0
  m_linkedTextComponent: {fileID: 0}
  parentLinkedComponent: {fileID: 0}
  m_enableKerning: 0
  m_ActiveFontFeatures: 6e72656b
  m_enableExtraPadding: 0
  checkPaddingRequired: 0
  m_isRichText: 1
  m_EmojiFallbackSupport: 1
  m_parseCtrlCharacters: 1
  m_isOrthographic: 1
  m_isCullingEnabled: 0
  m_horizontalMapping: 0
  m_verticalMapping: 0
  m_uvLineOffset: 0
  m_geometrySortingOrder: 0
  m_IsTextObjectScaleStatic: 0
  m_VertexBufferAutoSizeReduction: 0
  m_useMaxVisibleDescender: 1
  m_pageToDisplay: 1
  m_margin: {x: 0, y: 0, z: 0, w: 0}
  m_isUsingLegacyAnimationComponent: 0
  m_isVolumetricText: 0
  m_hasFontAssetChanged: 0
  m_baseMaterial: {fileID: 0}
  m_maskOffset: {x: 0, y: 0, z: 0, w: 0}
--- !u!222 &109180146
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 109180143}
  m_CullTransparentMesh: 1
--- !u!1 &141025192
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 141025193}
  m_Layer: 0
  m_Name: SpawnPoint_2
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &141025193
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 141025192}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 47753184}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &168872573
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 168872576}
  - component: {fileID: 168872575}
  - component: {fileID: 168872574}
  m_Layer: 0
  m_Name: EventSystem
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!114 &168872574
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 168872573}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 4f231c4fb786f3946a6b90b886c48677, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_SendPointerHoverToParent: 1
  m_HorizontalAxis: Horizontal
  m_VerticalAxis: Vertical
  m_SubmitButton: Submit
  m_CancelButton: Cancel
  m_InputActionsPerSecond: 10
  m_RepeatDelay: 0.5
  m_ForceModuleActive: 0
--- !u!114 &168872575
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 168872573}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 76c392e42b5098c458856cdf6ecaaaa1, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_FirstSelected: {fileID: 0}
  m_sendNavigationEvents: 1
  m_DragThreshold: 10
--- !u!4 &168872576
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 168872573}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &173380677
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 173380679}
  - component: {fileID: 173380678}
  m_Layer: 0
  m_Name: ForestMapAudioManager
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!114 &173380678
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 173380677}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f65c529c853310942b8b28f42961973f, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  musicAudioSource: {fileID: 810982374}
  uiSfxAudioSource: {fileID: 692591512}
  ambienceAudioSource: {fileID: 225600832}
  masterVolumeSlider: {fileID: 2120279516}
  musicVolumeSlider: {fileID: 2124822544}
  soundFxVolumeSlider: {fileID: 1658130457}
  ambienceVolumeSlider: {fileID: 954295167}
  introBackgroundMusic: {fileID: 8300000, guid: 9e6b4618f034d451a83e56818e4f2b09, type: 3}
  narrator_intro_0: {fileID: 8300000, guid: ff015c379eb1bd1479bca7d18768885a, type: 3}
  HelicopterFlyingLoop: {fileID: 8300000, guid: ed7e50c433bfda94a94b5da301c5e104, type: 3}
  introStormSound: {fileID: 8300000, guid: cf2b98f02f62cca4ab8b28c877507d22, type: 3}
  narratorSpeedMultiplier: 1
--- !u!4 &173380679
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 173380677}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 450.02774, y: 34.44874, z: 27.590458}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1439646037}
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &179687900
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 179687903}
  - component: {fileID: 179687902}
  - component: {fileID: 179687901}
  - component: {fileID: 179687904}
  - component: {fileID: 179687905}
  - component: {fileID: 179687906}
  m_Layer: 0
  m_Name: MainCamera
  m_TagString: MainCamera
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!81 &179687901
AudioListener:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 179687900}
  m_Enabled: 1
--- !u!20 &179687902
Camera:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 179687900}
  m_Enabled: 1
  serializedVersion: 2
  m_ClearFlags: 1
  m_BackGroundColor: {r: 0.19215687, g: 0.3019608, b: 0.4745098, a: 0}
  m_projectionMatrixMode: 1
  m_GateFitMode: 1
  m_FOVAxisMode: 0
  m_Iso: 200
  m_ShutterSpeed: 0.005
  m_Aperture: 16
  m_FocusDistance: 10
  m_FocalLength: 50
  m_BladeCount: 5
  m_Curvature: {x: 2, y: 11}
  m_BarrelClipping: 0.25
  m_Anamorphism: 0
  m_SensorSize: {x: 36, y: 24}
  m_LensShift: {x: 0, y: 0}
  m_NormalizedViewPortRect:
    serializedVersion: 2
    x: 0
    y: 0
    width: 1
    height: 1
  near clip plane: 0.3
  far clip plane: 1000
  field of view: 60
  orthographic: 0
  orthographic size: 5
  m_Depth: 0
  m_CullingMask:
    serializedVersion: 2
    m_Bits: 4294967295
  m_RenderingPath: -1
  m_TargetTexture: {fileID: 0}
  m_TargetDisplay: 0
  m_TargetEye: 3
  m_HDR: 0
  m_AllowMSAA: 0
  m_AllowDynamicResolution: 0
  m_ForceIntoRT: 0
  m_OcclusionCulling: 1
  m_StereoConvergence: 10
  m_StereoSeparation: 0.022
--- !u!4 &179687903
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 179687900}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: 0.39171857, z: -0, w: -0.9200851}
  m_LocalPosition: {x: 903.9642, y: 55.31908, z: 467.75748}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &179687904
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 179687900}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 23c1ce4fb46143f46bc5cb5224c934f6, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  clearColorMode: 0
  backgroundColorHDR: {r: 0.025, g: 0.07, b: 0.19, a: 0}
  clearDepth: 1
  volumeLayerMask:
    serializedVersion: 2
    m_Bits: 1
  volumeAnchorOverride: {fileID: 0}
  antialiasing: 0
  SMAAQuality: 2
  dithering: 0
  stopNaNs: 0
  taaSharpenStrength: 0.5
  TAAQuality: 1
  taaSharpenMode: 0
  taaRingingReduction: 0
  taaHistorySharpening: 0.35
  taaAntiFlicker: 0.5
  taaMotionVectorRejection: 0
  taaAntiHistoryRinging: 0
  taaBaseBlendFactor: 0.875
  taaJitterScale: 1
  physicalParameters:
    m_Iso: 200
    m_ShutterSpeed: 0.005
    m_Aperture: 16
    m_FocusDistance: 10
    m_BladeCount: 5
    m_Curvature: {x: 2, y: 11}
    m_BarrelClipping: 0.25
    m_Anamorphism: 0
  flipYMode: 0
  xrRendering: 1
  fullscreenPassthrough: 0
  allowDynamicResolution: 0
  customRenderingSettings: 0
  invertFaceCulling: 0
  probeLayerMask:
    serializedVersion: 2
    m_Bits: 4294967295
  hasPersistentHistory: 0
  screenSizeOverride: {x: 0, y: 0, z: 0, w: 0}
  screenCoordScaleBias: {x: 0, y: 0, z: 0, w: 0}
  allowDeepLearningSuperSampling: 1
  deepLearningSuperSamplingUseCustomQualitySettings: 0
  deepLearningSuperSamplingQuality: 0
  deepLearningSuperSamplingUseCustomAttributes: 0
  deepLearningSuperSamplingUseOptimalSettings: 1
  deepLearningSuperSamplingSharpening: 0
  allowFidelityFX2SuperResolution: 1
  fidelityFX2SuperResolutionUseCustomQualitySettings: 0
  fidelityFX2SuperResolutionQuality: 0
  fidelityFX2SuperResolutionUseCustomAttributes: 0
  fidelityFX2SuperResolutionUseOptimalSettings: 1
  fidelityFX2SuperResolutionEnableSharpening: 0
  fidelityFX2SuperResolutionSharpening: 0
  fsrOverrideSharpness: 0
  fsrSharpness: 0.92
  exposureTarget: {fileID: 0}
  materialMipBias: 0
  m_RenderingPathCustomFrameSettings:
    bitDatas:
      data1: 5770166122053453
      data2: 12934340311651418136
    lodBias: 1
    lodBiasMode: 0
    lodBiasQualityLevel: 0
    maximumLODLevel: 0
    maximumLODLevelMode: 0
    maximumLODLevelQualityLevel: 0
    sssQualityMode: 0
    sssQualityLevel: 0
    sssCustomSampleBudget: 20
    sssCustomDownsampleSteps: 0
    msaaMode: 1
    materialQuality: 0
  renderingPathCustomFrameSettingsOverrideMask:
    mask:
      data1: 0
      data2: 0
  defaultFrameSettings: 0
  m_Version: 9
  m_ObsoleteRenderingPath: 0
  m_ObsoleteFrameSettings:
    overrides: 0
    enableShadow: 0
    enableContactShadows: 0
    enableShadowMask: 0
    enableSSR: 0
    enableSSAO: 0
    enableSubsurfaceScattering: 0
    enableTransmission: 0
    enableAtmosphericScattering: 0
    enableVolumetrics: 0
    enableReprojectionForVolumetrics: 0
    enableLightLayers: 0
    enableExposureControl: 1
    diffuseGlobalDimmer: 0
    specularGlobalDimmer: 0
    shaderLitMode: 0
    enableDepthPrepassWithDeferredRendering: 0
    enableTransparentPrepass: 0
    enableMotionVectors: 0
    enableObjectMotionVectors: 0
    enableDecals: 0
    enableRoughRefraction: 0
    enableTransparentPostpass: 0
    enableDistortion: 0
    enablePostprocess: 0
    enableOpaqueObjects: 0
    enableTransparentObjects: 0
    enableRealtimePlanarReflection: 0
    enableMSAA: 0
    enableAsyncCompute: 0
    runLightListAsync: 0
    runSSRAsync: 0
    runSSAOAsync: 0
    runContactShadowsAsync: 0
    runVolumeVoxelizationAsync: 0
    lightLoopSettings:
      overrides: 0
      enableDeferredTileAndCluster: 0
      enableComputeLightEvaluation: 0
      enableComputeLightVariants: 0
      enableComputeMaterialVariants: 0
      enableFptlForForwardOpaque: 0
      enableBigTilePrepass: 0
      isFptlEnabled: 0
--- !u!114 &179687905
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 179687900}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 8951cbe1a3055664cad4b4dac6c24343, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  syncDirection: 0
  syncMode: 0
  syncInterval: 0
  sceneMainCamera: {fileID: 179687902}
  cameraOffset: {x: 0, y: 5, z: -10}
  enableDynamicFollow: 1
  playerSpawnPoints:
  - {fileID: 641934374}
  - {fileID: 1513151534}
  - {fileID: 542679764}
  - {fileID: 141025193}
--- !u!114 &179687906
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 179687900}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 9b91ecbcc199f4492b9a91e820070131, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  sceneId: 2301166693
  _assetId: 0
  serverOnly: 0
  visibility: 0
  hasSpawned: 0
--- !u!1001 &181539674
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    serializedVersion: 3
    m_TransformParent: {fileID: 1769772629}
    m_Modifications:
    - target: {fileID: 1031072879, guid: abc00000000001332876369726052611, type: 3}
      propertyPath: m_Name
      value: SM_OfficeDesk_8
      objectReference: {fileID: 0}
    - target: {fileID: 1031072880, guid: abc00000000001332876369726052611, type: 3}
      propertyPath: m_LocalScale.x
      value: 2
      objectReference: {fileID: 0}
    - target: {fileID: 1031072880, guid: abc00000000001332876369726052611, type: 3}
      propertyPath: m_LocalScale.y
      value: 2
      objectReference: {fileID: 0}
    - target: {fileID: 1031072880, guid: abc00000000001332876369726052611, type: 3}
      propertyPath: m_LocalScale.z
      value: 2
      objectReference: {fileID: 0}
    - target: {fileID: 1031072880, guid: abc00000000001332876369726052611, type: 3}
      propertyPath: m_LocalPosition.x
      value: -0.81817627
      objectReference: {fileID: 0}
    - target: {fileID: 1031072880, guid: abc00000000001332876369726052611, type: 3}
      propertyPath: m_LocalPosition.y
      value: -1.8770001
      objectReference: {fileID: 0}
    - target: {fileID: 1031072880, guid: abc00000000001332876369726052611, type: 3}
      propertyPath: m_LocalPosition.z
      value: 0.54000854
      objectReference: {fileID: 0}
    - target: {fileID: 1031072880, guid: abc00000000001332876369726052611, type: 3}
      propertyPath: m_LocalRotation.w
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 1031072880, guid: abc00000000001332876369726052611, type: 3}
      propertyPath: m_LocalRotation.x
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 1031072880, guid: abc00000000001332876369726052611, type: 3}
      propertyPath: m_LocalRotation.y
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 1031072880, guid: abc00000000001332876369726052611, type: 3}
      propertyPath: m_LocalRotation.z
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 1031072880, guid: abc00000000001332876369726052611, type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1031072880, guid: abc00000000001332876369726052611, type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1031072880, guid: abc00000000001332876369726052611, type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1031072880, guid: abc00000000001332876369726052611, type: 3}
      propertyPath: m_ConstrainProportionsScale
      value: 1
      objectReference: {fileID: 0}
    m_RemovedComponents: []
    m_RemovedGameObjects: []
    m_AddedGameObjects: []
    m_AddedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: abc00000000001332876369726052611, type: 3}
--- !u!4 &181539675 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: 1031072880, guid: abc00000000001332876369726052611, type: 3}
  m_PrefabInstance: {fileID: 181539674}
  m_PrefabAsset: {fileID: 0}
--- !u!1 &189974503
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 189974504}
  - component: {fileID: 189974507}
  - component: {fileID: 189974506}
  - component: {fileID: 189974505}
  m_Layer: 5
  m_Name: ForestMapOverlay
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &189974504
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 189974503}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1.1, y: 1.1, z: 1.1}
  m_ConstrainProportionsScale: 1
  m_Children:
  - {fileID: 2002829529}
  m_Father: {fileID: 440133750}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0.5, y: 0.5}
  m_AnchorMax: {x: 0.5, y: 0.5}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 1920, y: 1080}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!225 &189974505
CanvasGroup:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 189974503}
  m_Enabled: 1
  m_Alpha: 1
  m_Interactable: 1
  m_BlocksRaycasts: 1
  m_IgnoreParentGroups: 0
--- !u!114 &189974506
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 189974503}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fe87c0e1cc204ed48ad3b37840f39efc, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 0, g: 0, b: 0, a: 0}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_Sprite: {fileID: 10907, guid: 0000000000000000f000000000000000, type: 0}
  m_Type: 1
  m_PreserveAspect: 0
  m_FillCenter: 1
  m_FillMethod: 4
  m_FillAmount: 1
  m_FillClockwise: 1
  m_FillOrigin: 0
  m_UseSpriteMesh: 0
  m_PixelsPerUnitMultiplier: 1
--- !u!222 &189974507
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 189974503}
  m_CullTransparentMesh: 1
--- !u!1001 &207742600
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    serializedVersion: 3
    m_TransformParent: {fileID: 1769772629}
    m_Modifications:
    - target: {fileID: 1031081025, guid: abc00000000006132371766603354423, type: 3}
      propertyPath: m_Name
      value: SM_Monitor_01a2_1
      objectReference: {fileID: 0}
    - target: {fileID: 1031081026, guid: abc00000000006132371766603354423, type: 3}
      propertyPath: m_LocalScale.x
      value: 2.5
      objectReference: {fileID: 0}
    - target: {fileID: 1031081026, guid: abc00000000006132371766603354423, type: 3}
      propertyPath: m_LocalScale.y
      value: 2.5
      objectReference: {fileID: 0}
    - target: {fileID: 1031081026, guid: abc00000000006132371766603354423, type: 3}
      propertyPath: m_LocalScale.z
      value: 2.5
      objectReference: {fileID: 0}
    - target: {fileID: 1031081026, guid: abc00000000006132371766603354423, type: 3}
      propertyPath: m_LocalPosition.x
      value: -0.8239136
      objectReference: {fileID: 0}
    - target: {fileID: 1031081026, guid: abc00000000006132371766603354423, type: 3}
      propertyPath: m_LocalPosition.y
      value: 0.4560001
      objectReference: {fileID: 0}
    - target: {fileID: 1031081026, guid: abc00000000006132371766603354423, type: 3}
      propertyPath: m_LocalPosition.z
      value: 0.69400024
      objectReference: {fileID: 0}
    - target: {fileID: 1031081026, guid: abc00000000006132371766603354423, type: 3}
      propertyPath: m_LocalRotation.w
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 1031081026, guid: abc00000000006132371766603354423, type: 3}
      propertyPath: m_LocalRotation.x
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 1031081026, guid: abc00000000006132371766603354423, type: 3}
      propertyPath: m_LocalRotation.y
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 1031081026, guid: abc00000000006132371766603354423, type: 3}
      propertyPath: m_LocalRotation.z
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 1031081026, guid: abc00000000006132371766603354423, type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1031081026, guid: abc00000000006132371766603354423, type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1031081026, guid: abc00000000006132371766603354423, type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1031081026, guid: abc00000000006132371766603354423, type: 3}
      propertyPath: m_ConstrainProportionsScale
      value: 1
      objectReference: {fileID: 0}
    m_RemovedComponents: []
    m_RemovedGameObjects: []
    m_AddedGameObjects: []
    m_AddedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: abc00000000006132371766603354423, type: 3}
--- !u!4 &207742601 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: 1031081026, guid: abc00000000006132371766603354423, type: 3}
  m_PrefabInstance: {fileID: 207742600}
  m_PrefabAsset: {fileID: 0}
--- !u!1 &214815527
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 214815528}
  - component: {fileID: 214815529}
  m_Layer: 5
  m_Name: SoundFXVolumeSettingContainer
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &214815528
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 214815527}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0.0001495125}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1088778216}
  - {fileID: 1658130456}
  m_Father: {fileID: 869431280}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 1}
  m_AnchorMax: {x: 0, y: 1}
  m_AnchoredPosition: {x: 200, y: -35}
  m_SizeDelta: {x: 288.36, y: 70}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &214815529
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 214815527}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 59f8146938fff824cb5fd77236b75775, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Padding:
    m_Left: 0
    m_Right: 0
    m_Top: 0
    m_Bottom: 0
  m_ChildAlignment: 1
  m_Spacing: 0
  m_ChildForceExpandWidth: 1
  m_ChildForceExpandHeight: 1
  m_ChildControlWidth: 0
  m_ChildControlHeight: 0
  m_ChildScaleWidth: 0
  m_ChildScaleHeight: 0
  m_ReverseArrangement: 0
--- !u!1 &216111615
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 216111616}
  m_Layer: 0
  m_Name: HLetter
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &216111616
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 216111615}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: -0.3, y: 0, z: 0.16}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 293800914}
  - {fileID: 1076455030}
  - {fileID: 809916324}
  m_Father: {fileID: 244853904}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &225600831
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 225600833}
  - component: {fileID: 225600832}
  m_Layer: 0
  m_Name: AmbienceAudioSource
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!82 &225600832
AudioSource:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 225600831}
  m_Enabled: 1
  serializedVersion: 4
  OutputAudioMixerGroup: {fileID: 0}
  m_audioClip: {fileID: 0}
  m_Resource: {fileID: 0}
  m_PlayOnAwake: 1
  m_Volume: 1
  m_Pitch: 1
  Loop: 0
  Mute: 0
  Spatialize: 0
  SpatializePostEffects: 0
  Priority: 128
  DopplerLevel: 1
  MinDistance: 1
  MaxDistance: 500
  Pan2D: 0
  rolloffMode: 0
  BypassEffects: 0
  BypassListenerEffects: 0
  BypassReverbZones: 0
  rolloffCustomCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 0
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  panLevelCustomCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  spreadCustomCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  reverbZoneMixCustomCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
--- !u!4 &225600833
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 225600831}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1439646037}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &230490798
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 230490799}
  - component: {fileID: 230490802}
  - component: {fileID: 230490801}
  - component: {fileID: 230490800}
  m_Layer: 5
  m_Name: SettingsBackButton
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &230490799
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 230490798}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: -0.00024997548}
  m_LocalScale: {x: 1.0025682, y: 1.0025682, z: 1.0025682}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1176990234}
  m_Father: {fileID: 1238260545}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 1}
  m_AnchorMax: {x: 0, y: 1}
  m_AnchoredPosition: {x: 200, y: -454.7}
  m_SizeDelta: {x: 400, y: 60}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &230490800
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 230490798}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 4e29b1a8efbd4b44bb3f3716e73f07ff, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Navigation:
    m_Mode: 3
    m_WrapAround: 0
    m_SelectOnUp: {fileID: 0}
    m_SelectOnDown: {fileID: 0}
    m_SelectOnLeft: {fileID: 0}
    m_SelectOnRight: {fileID: 0}
  m_Transition: 1
  m_Colors:
    m_NormalColor: {r: 0.23529413, g: 0.23529413, b: 0.23529413, a: 1}
    m_HighlightedColor: {r: 0.3019608, g: 0.3019608, b: 0.3019608, a: 1}
    m_PressedColor: {r: 0.2679245, g: 0.2679245, b: 0.2679245, a: 1}
    m_SelectedColor: {r: 0.13333334, g: 0.13333334, b: 0.13333334, a: 1}
    m_DisabledColor: {r: 0.13333334, g: 0.13333334, b: 0.13333334, a: 0.5019608}
    m_ColorMultiplier: 1
    m_FadeDuration: 0.1
  m_SpriteState:
    m_HighlightedSprite: {fileID: 0}
    m_PressedSprite: {fileID: 0}
    m_SelectedSprite: {fileID: 0}
    m_DisabledSprite: {fileID: 0}
  m_AnimationTriggers:
    m_NormalTrigger: Normal
    m_HighlightedTrigger: Highlighted
    m_PressedTrigger: Pressed
    m_SelectedTrigger: Selected
    m_DisabledTrigger: Disabled
  m_Interactable: 1
  m_TargetGraphic: {fileID: 230490801}
  m_OnClick:
    m_PersistentCalls:
      m_Calls:
      - m_Target: {fileID: 0}
        m_TargetAssemblyTypeName: MainMenu, Assembly-CSharp
        m_MethodName: OnSettingsBackButtonClicked
        m_Mode: 1
        m_Arguments:
          m_ObjectArgument: {fileID: 0}
          m_ObjectArgumentAssemblyTypeName: UnityEngine.Object, UnityEngine
          m_IntArgument: 0
          m_FloatArgument: 0
          m_StringArgument: 
          m_BoolArgument: 0
        m_CallState: 2
--- !u!114 &230490801
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 230490798}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fe87c0e1cc204ed48ad3b37840f39efc, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_Sprite: {fileID: 10905, guid: 0000000000000000f000000000000000, type: 0}
  m_Type: 1
  m_PreserveAspect: 0
  m_FillCenter: 1
  m_FillMethod: 4
  m_FillAmount: 1
  m_FillClockwise: 1
  m_FillOrigin: 0
  m_UseSpriteMesh: 0
  m_PixelsPerUnitMultiplier: 1
--- !u!222 &230490802
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 230490798}
  m_CullTransparentMesh: 1
--- !u!1 &244853903
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 244853904}
  m_Layer: 0
  m_Name: Helipad_0
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &244853904
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 244853903}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 1, z: 0, w: 0}
  m_LocalPosition: {x: 6.448999, y: -0.187, z: -6.698999}
  m_LocalScale: {x: 0.2, y: 0.1, z: 0.2}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 507537394}
  - {fileID: 216111616}
  m_Father: {fileID: 1675261855}
  m_LocalEulerAnglesHint: {x: 0, y: 180, z: 0}
--- !u!1 &244927039
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 244927040}
  - component: {fileID: 244927043}
  - component: {fileID: 244927042}
  - component: {fileID: 244927041}
  m_Layer: 0
  m_Name: HelicopterRoofCollider
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &244927040
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 244927039}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: -0.0012609866, y: 0.0001998855, z: 0.05079999}
  m_LocalScale: {x: 0.16, y: 0.04464409, z: 0.06609168}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 666247744}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!65 &244927041
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 244927039}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 3
  m_Size: {x: 1, y: 1, z: 1}
  m_Center: {x: 0, y: 0, z: 0}
--- !u!23 &244927042
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 244927039}
  m_Enabled: 0
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 73c176f402d2c2f4d929aa5da7585d17, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!33 &244927043
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 244927039}
  m_Mesh: {fileID: 10202, guid: 0000000000000000e000000000000000, type: 0}
--- !u!114 &270152455
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 2562637f396e4c10acd3fa9926b53a12, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  enabled: 1
  label: Slope
  blendMode: 0
  opacity: 100
  passIndex: 1
  minMax: {x: 7.5524473, y: 50.97902}
  minFalloff: 10
  maxFalloff: 40.7
--- !u!1001 &281576276
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    serializedVersion: 3
    m_TransformParent: {fileID: 1133823111}
    m_Modifications:
    - target: {fileID: 1031083571, guid: abc00000000010482999517139272622, type: 3}
      propertyPath: m_Name
      value: SM_WalkieTalkie_251
      objectReference: {fileID: 0}
    - target: {fileID: 1031083572, guid: abc00000000010482999517139272622, type: 3}
      propertyPath: m_LocalScale.x
      value: 2
      objectReference: {fileID: 0}
    - target: {fileID: 1031083572, guid: abc00000000010482999517139272622, type: 3}
      propertyPath: m_LocalScale.y
      value: 2
      objectReference: {fileID: 0}
    - target: {fileID: 1031083572, guid: abc00000000010482999517139272622, type: 3}
      propertyPath: m_LocalScale.z
      value: 2
      objectReference: {fileID: 0}
    - target: {fileID: 1031083572, guid: abc00000000010482999517139272622, type: 3}
      propertyPath: m_LocalPosition.x
      value: 6.534607
      objectReference: {fileID: 0}
    - target: {fileID: 1031083572, guid: abc00000000010482999517139272622, type: 3}
      propertyPath: m_LocalPosition.y
      value: 2.0540133
      objectReference: {fileID: 0}
    - target: {fileID: 1031083572, guid: abc00000000010482999517139272622, type: 3}
      propertyPath: m_LocalPosition.z
      value: -0.6870117
      objectReference: {fileID: 0}
    - target: {fileID: 1031083572, guid: abc00000000010482999517139272622, type: 3}
      propertyPath: m_LocalRotation.w
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 1031083572, guid: abc00000000010482999517139272622, type: 3}
      propertyPath: m_LocalRotation.x
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 1031083572, guid: abc00000000010482999517139272622, type: 3}
      propertyPath: m_LocalRotation.y
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 1031083572, guid: abc00000000010482999517139272622, type: 3}
      propertyPath: m_LocalRotation.z
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 1031083572, guid: abc00000000010482999517139272622, type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1031083572, guid: abc00000000010482999517139272622, type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1031083572, guid: abc00000000010482999517139272622, type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1031083572, guid: abc00000000010482999517139272622, type: 3}
      propertyPath: m_ConstrainProportionsScale
      value: 1
      objectReference: {fileID: 0}
    m_RemovedComponents: []
    m_RemovedGameObjects: []
    m_AddedGameObjects: []
    m_AddedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: abc00000000010482999517139272622, type: 3}
--- !u!4 &281576277 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: 1031083572, guid: abc00000000010482999517139272622, type: 3}
  m_PrefabInstance: {fileID: 281576276}
  m_PrefabAsset: {fileID: 0}
--- !u!1 &293800913
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 293800914}
  - component: {fileID: 293800917}
  - component: {fileID: 293800916}
  - component: {fileID: 293800915}
  m_Layer: 0
  m_Name: Cube
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &293800914
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 293800913}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 6, y: 0.5109999, z: 3.1140137}
  m_LocalScale: {x: 7.7096415, y: 0.001, z: 0.29843688}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 216111616}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!65 &293800915
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 293800913}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 3
  m_Size: {x: 1, y: 1, z: 1}
  m_Center: {x: 0, y: 0, z: 0}
--- !u!23 &293800916
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 293800913}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: c9ef99fcd36d4d34da42acc2c4a49379, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!33 &293800917
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 293800913}
  m_Mesh: {fileID: 10202, guid: 0000000000000000e000000000000000, type: 0}
--- !u!1001 &295616943
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    serializedVersion: 3
    m_TransformParent: {fileID: 708659531}
    m_Modifications:
    - target: {fileID: 1031057167, guid: abc00000000014508709065446426816, type: 3}
      propertyPath: m_Name
      value: SM_TentMiddle_7
      objectReference: {fileID: 0}
    - target: {fileID: 1031057168, guid: abc00000000014508709065446426816, type: 3}
      propertyPath: m_LocalScale.x
      value: 2.2
      objectReference: {fileID: 0}
    - target: {fileID: 1031057168, guid: abc00000000014508709065446426816, type: 3}
      propertyPath: m_LocalScale.y
      value: 2.2
      objectReference: {fileID: 0}
    - target: {fileID: 1031057168, guid: abc00000000014508709065446426816, type: 3}
      propertyPath: m_LocalScale.z
      value: 2.2
      objectReference: {fileID: 0}
    - target: {fileID: 1031057168, guid: abc00000000014508709065446426816, type: 3}
      propertyPath: m_LocalPosition.x
      value: 2.119995
      objectReference: {fileID: 0}
    - target: {fileID: 1031057168, guid: abc00000000014508709065446426816, type: 3}
      propertyPath: m_LocalPosition.y
      value: 0.02675891
      objectReference: {fileID: 0}
    - target: {fileID: 1031057168, guid: abc00000000014508709065446426816, type: 3}
      propertyPath: m_LocalPosition.z
      value: 2.5899963
      objectReference: {fileID: 0}
    - target: {fileID: 1031057168, guid: abc00000000014508709065446426816, type: 3}
      propertyPath: m_LocalRotation.w
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 1031057168, guid: abc00000000014508709065446426816, type: 3}
      propertyPath: m_LocalRotation.x
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 1031057168, guid: abc00000000014508709065446426816, type: 3}
      propertyPath: m_LocalRotation.y
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 1031057168, guid: abc00000000014508709065446426816, type: 3}
      propertyPath: m_LocalRotation.z
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 1031057168, guid: abc00000000014508709065446426816, type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1031057168, guid: abc00000000014508709065446426816, type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1031057168, guid: abc00000000014508709065446426816, type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1031057168, guid: abc00000000014508709065446426816, type: 3}
      propertyPath: m_ConstrainProportionsScale
      value: 1
      objectReference: {fileID: 0}
    m_RemovedComponents: []
    m_RemovedGameObjects: []
    m_AddedGameObjects: []
    m_AddedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: abc00000000014508709065446426816, type: 3}
--- !u!4 &295616944 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: 1031057168, guid: abc00000000014508709065446426816, type: 3}
  m_PrefabInstance: {fileID: 295616943}
  m_PrefabAsset: {fileID: 0}
--- !u!1 &305942179
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 305942180}
  - component: {fileID: 305942182}
  - component: {fileID: 305942181}
  m_Layer: 5
  m_Name: MusicVolumeSettingLabelMin
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &305942180
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 305942179}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 2124822543}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0.5, y: 0.5}
  m_AnchorMax: {x: 0.5, y: 0.5}
  m_AnchoredPosition: {x: -153.9, y: 0}
  m_SizeDelta: {x: 100, y: 100}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &305942181
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 305942179}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f4688fdb7df04437aeb418b961361dc5, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_text: MUTE
  m_isRightToLeft: 0
  m_fontAsset: {fileID: 11400000, guid: 8f586378b4e144a9851e7b34d9b748ee, type: 2}
  m_sharedMaterial: {fileID: 2180264, guid: 8f586378b4e144a9851e7b34d9b748ee, type: 2}
  m_fontSharedMaterials: []
  m_fontMaterial: {fileID: 0}
  m_fontMaterials: []
  m_fontColor32:
    serializedVersion: 2
    rgba: 4294967295
  m_fontColor: {r: 1, g: 1, b: 1, a: 1}
  m_enableVertexGradient: 0
  m_colorMode: 3
  m_fontColorGradient:
    topLeft: {r: 1, g: 1, b: 1, a: 1}
    topRight: {r: 1, g: 1, b: 1, a: 1}
    bottomLeft: {r: 1, g: 1, b: 1, a: 1}
    bottomRight: {r: 1, g: 1, b: 1, a: 1}
  m_fontColorGradientPreset: {fileID: 0}
  m_spriteAsset: {fileID: 0}
  m_tintAllSprites: 0
  m_StyleSheet: {fileID: 0}
  m_TextStyleHashCode: -1183493901
  m_overrideHtmlColors: 0
  m_faceColor:
    serializedVersion: 2
    rgba: 4294967295
  m_fontSize: 20
  m_fontSizeBase: 20
  m_fontWeight: 400
  m_enableAutoSizing: 0
  m_fontSizeMin: 18
  m_fontSizeMax: 72
  m_fontStyle: 0
  m_HorizontalAlignment: 2
  m_VerticalAlignment: 512
  m_textAlignment: 65535
  m_characterSpacing: 0
  m_wordSpacing: 0
  m_lineSpacing: 0
  m_lineSpacingMax: 0
  m_paragraphSpacing: 0
  m_charWidthMaxAdj: 0
  m_TextWrappingMode: 0
  m_wordWrappingRatios: 0.4
  m_overflowMode: 0
  m_linkedTextComponent: {fileID: 0}
  parentLinkedComponent: {fileID: 0}
  m_enableKerning: 0
  m_ActiveFontFeatures: 6e72656b
  m_enableExtraPadding: 0
  checkPaddingRequired: 0
  m_isRichText: 1
  m_EmojiFallbackSupport: 1
  m_parseCtrlCharacters: 1
  m_isOrthographic: 1
  m_isCullingEnabled: 0
  m_horizontalMapping: 0
  m_verticalMapping: 0
  m_uvLineOffset: 0
  m_geometrySortingOrder: 0
  m_IsTextObjectScaleStatic: 0
  m_VertexBufferAutoSizeReduction: 0
  m_useMaxVisibleDescender: 1
  m_pageToDisplay: 1
  m_margin: {x: 0, y: 0, z: 0, w: 0}
  m_isUsingLegacyAnimationComponent: 0
  m_isVolumetricText: 0
  m_hasFontAssetChanged: 0
  m_baseMaterial: {fileID: 0}
  m_maskOffset: {x: 0, y: 0, z: 0, w: 0}
--- !u!222 &305942182
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 305942179}
  m_CullTransparentMesh: 1
--- !u!1 &309580894
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 309580895}
  - component: {fileID: 309580897}
  - component: {fileID: 309580896}
  m_Layer: 5
  m_Name: Background
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &309580895
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 309580894}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 2124822543}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0.25}
  m_AnchorMax: {x: 1, y: 0.75}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 0, y: 0}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &309580896
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 309580894}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fe87c0e1cc204ed48ad3b37840f39efc, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_Sprite: {fileID: 10907, guid: 0000000000000000f000000000000000, type: 0}
  m_Type: 1
  m_PreserveAspect: 0
  m_FillCenter: 1
  m_FillMethod: 4
  m_FillAmount: 1
  m_FillClockwise: 1
  m_FillOrigin: 0
  m_UseSpriteMesh: 0
  m_PixelsPerUnitMultiplier: 1
--- !u!222 &309580897
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 309580894}
  m_CullTransparentMesh: 1
--- !u!1001 &331287264
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    serializedVersion: 3
    m_TransformParent: {fileID: 519976997}
    m_Modifications:
    - target: {fileID: 1031089831, guid: abc00000000000152043680928551015, type: 3}
      propertyPath: m_Name
      value: SM_MERGED_Barrels01_44
      objectReference: {fileID: 0}
    - target: {fileID: 1031089832, guid: abc00000000000152043680928551015, type: 3}
      propertyPath: m_LocalScale.x
      value: 2.3
      objectReference: {fileID: 0}
    - target: {fileID: 1031089832, guid: abc00000000000152043680928551015, type: 3}
      propertyPath: m_LocalScale.y
      value: 2.3
      objectReference: {fileID: 0}
    - target: {fileID: 1031089832, guid: abc00000000000152043680928551015, type: 3}
      propertyPath: m_LocalScale.z
      value: 2.3
      objectReference: {fileID: 0}
    - target: {fileID: 1031089832, guid: abc00000000000152043680928551015, type: 3}
      propertyPath: m_LocalPosition.x
      value: 2.2700195
      objectReference: {fileID: 0}
    - target: {fileID: 1031089832, guid: abc00000000000152043680928551015, type: 3}
      propertyPath: m_LocalPosition.y
      value: -3.3099997
      objectReference: {fileID: 0}
    - target: {fileID: 1031089832, guid: abc00000000000152043680928551015, type: 3}
      propertyPath: m_LocalPosition.z
      value: 13.279999
      objectReference: {fileID: 0}
    - target: {fileID: 1031089832, guid: abc00000000000152043680928551015, type: 3}
      propertyPath: m_LocalRotation.w
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 1031089832, guid: abc00000000000152043680928551015, type: 3}
      propertyPath: m_LocalRotation.x
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 1031089832, guid: abc00000000000152043680928551015, type: 3}
      propertyPath: m_LocalRotation.y
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 1031089832, guid: abc00000000000152043680928551015, type: 3}
      propertyPath: m_LocalRotation.z
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 1031089832, guid: abc00000000000152043680928551015, type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1031089832, guid: abc00000000000152043680928551015, type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1031089832, guid: abc00000000000152043680928551015, type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1031089832, guid: abc00000000000152043680928551015, type: 3}
      propertyPath: m_ConstrainProportionsScale
      value: 1
      objectReference: {fileID: 0}
    m_RemovedComponents: []
    m_RemovedGameObjects: []
    m_AddedGameObjects: []
    m_AddedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: abc00000000000152043680928551015, type: 3}
--- !u!4 &331287265 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: 1031089832, guid: abc00000000000152043680928551015, type: 3}
  m_PrefabInstance: {fileID: 331287264}
  m_PrefabAsset: {fileID: 0}
--- !u!1 &331546770
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 331546771}
  m_Layer: 0
  m_Name: HelicopterWaypoint_2
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &331546771
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 331546770}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: -1.5900269, y: 47.084, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1812692992}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1001 &344447160
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    serializedVersion: 3
    m_TransformParent: {fileID: 1769772629}
    m_Modifications:
    - target: {fileID: 1031104985, guid: abc00000000017280041634371412424, type: 3}
      propertyPath: m_Name
      value: SM_Mouse_01a_109
      objectReference: {fileID: 0}
    - target: {fileID: 1031104986, guid: abc00000000017280041634371412424, type: 3}
      propertyPath: m_LocalScale.x
      value: 2.3
      objectReference: {fileID: 0}
    - target: {fileID: 1031104986, guid: abc00000000017280041634371412424, type: 3}
      propertyPath: m_LocalScale.y
      value: 2.3
      objectReference: {fileID: 0}
    - target: {fileID: 1031104986, guid: abc00000000017280041634371412424, type: 3}
      propertyPath: m_LocalScale.z
      value: 2.3
      objectReference: {fileID: 0}
    - target: {fileID: 1031104986, guid: abc00000000017280041634371412424, type: 3}
      propertyPath: m_LocalPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1031104986, guid: abc00000000017280041634371412424, type: 3}
      propertyPath: m_LocalPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1031104986, guid: abc00000000017280041634371412424, type: 3}
      propertyPath: m_LocalPosition.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1031104986, guid: abc00000000017280041634371412424, type: 3}
      propertyPath: m_LocalRotation.w
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 1031104986, guid: abc00000000017280041634371412424, type: 3}
      propertyPath: m_LocalRotation.x
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 1031104986, guid: abc00000000017280041634371412424, type: 3}
      propertyPath: m_LocalRotation.y
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 1031104986, guid: abc00000000017280041634371412424, type: 3}
      propertyPath: m_LocalRotation.z
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 1031104986, guid: abc00000000017280041634371412424, type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1031104986, guid: abc00000000017280041634371412424, type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1031104986, guid: abc00000000017280041634371412424, type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1031104986, guid: abc00000000017280041634371412424, type: 3}
      propertyPath: m_ConstrainProportionsScale
      value: 1
      objectReference: {fileID: 0}
    m_RemovedComponents: []
    m_RemovedGameObjects: []
    m_AddedGameObjects: []
    m_AddedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: abc00000000017280041634371412424, type: 3}
--- !u!4 &344447161 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: 1031104986, guid: abc00000000017280041634371412424, type: 3}
  m_PrefabInstance: {fileID: 344447160}
  m_PrefabAsset: {fileID: 0}
--- !u!1001 &368253825
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    serializedVersion: 3
    m_TransformParent: {fileID: 708659531}
    m_Modifications:
    - target: {fileID: 1031057509, guid: abc00000000010272314754052036963, type: 3}
      propertyPath: m_Name
      value: SM_TentStart_5
      objectReference: {fileID: 0}
    - target: {fileID: 1031057510, guid: abc00000000010272314754052036963, type: 3}
      propertyPath: m_LocalScale.x
      value: 2.2
      objectReference: {fileID: 0}
    - target: {fileID: 1031057510, guid: abc00000000010272314754052036963, type: 3}
      propertyPath: m_LocalScale.y
      value: 2.2
      objectReference: {fileID: 0}
    - target: {fileID: 1031057510, guid: abc00000000010272314754052036963, type: 3}
      propertyPath: m_LocalScale.z
      value: 2.2
      objectReference: {fileID: 0}
    - target: {fileID: 1031057510, guid: abc00000000010272314754052036963, type: 3}
      propertyPath: m_LocalPosition.x
      value: 2.1481934
      objectReference: {fileID: 0}
    - target: {fileID: 1031057510, guid: abc00000000010272314754052036963, type: 3}
      propertyPath: m_LocalPosition.y
      value: 0.034609556
      objectReference: {fileID: 0}
    - target: {fileID: 1031057510, guid: abc00000000010272314754052036963, type: 3}
      propertyPath: m_LocalPosition.z
      value: -6.6279907
      objectReference: {fileID: 0}
    - target: {fileID: 1031057510, guid: abc00000000010272314754052036963, type: 3}
      propertyPath: m_LocalRotation.w
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 1031057510, guid: abc00000000010272314754052036963, type: 3}
      propertyPath: m_LocalRotation.x
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 1031057510, guid: abc00000000010272314754052036963, type: 3}
      propertyPath: m_LocalRotation.y
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 1031057510, guid: abc00000000010272314754052036963, type: 3}
      propertyPath: m_LocalRotation.z
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 1031057510, guid: abc00000000010272314754052036963, type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1031057510, guid: abc00000000010272314754052036963, type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1031057510, guid: abc00000000010272314754052036963, type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1031057510, guid: abc00000000010272314754052036963, type: 3}
      propertyPath: m_ConstrainProportionsScale
      value: 1
      objectReference: {fileID: 0}
    m_RemovedComponents: []
    m_RemovedGameObjects: []
    m_AddedGameObjects: []
    m_AddedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: abc00000000010272314754052036963, type: 3}
--- !u!4 &368253826 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: 1031057510, guid: abc00000000010272314754052036963, type: 3}
  m_PrefabInstance: {fileID: 368253825}
  m_PrefabAsset: {fileID: 0}
--- !u!1 &369533475
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 369533476}
  m_Layer: 0
  m_Name: HelicopterWaypoint_1
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &369533476
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 369533475}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 221.31, y: 47.084, z: 194.1}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1812692992}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1001 &371304056
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    serializedVersion: 3
    m_TransformParent: {fileID: 1675261855}
    m_Modifications:
    - target: {fileID: 1030975294, guid: abc00000000006504693431563510938, type: 3}
      propertyPath: m_Name
      value: SM_Roadblock_10
      objectReference: {fileID: 0}
    - target: {fileID: 1030975295, guid: abc00000000006504693431563510938, type: 3}
      propertyPath: m_LocalScale.x
      value: 0.14657001
      objectReference: {fileID: 0}
    - target: {fileID: 1030975295, guid: abc00000000006504693431563510938, type: 3}
      propertyPath: m_LocalScale.y
      value: 0.14657001
      objectReference: {fileID: 0}
    - target: {fileID: 1030975295, guid: abc00000000006504693431563510938, type: 3}
      propertyPath: m_LocalScale.z
      value: 0.14657001
      objectReference: {fileID: 0}
    - target: {fileID: 1030975295, guid: abc00000000006504693431563510938, type: 3}
      propertyPath: m_LocalPosition.x
      value: 7.767203
      objectReference: {fileID: 0}
    - target: {fileID: 1030975295, guid: abc00000000006504693431563510938, type: 3}
      propertyPath: m_LocalPosition.y
      value: -0.14096747
      objectReference: {fileID: 0}
    - target: {fileID: 1030975295, guid: abc00000000006504693431563510938, type: 3}
      propertyPath: m_LocalPosition.z
      value: -9.07695
      objectReference: {fileID: 0}
    - target: {fileID: 1030975295, guid: abc00000000006504693431563510938, type: 3}
      propertyPath: m_LocalRotation.w
      value: 0.57431865
      objectReference: {fileID: 0}
    - target: {fileID: 1030975295, guid: abc00000000006504693431563510938, type: 3}
      propertyPath: m_LocalRotation.x
      value: -0.034076147
      objectReference: {fileID: 0}
    - target: {fileID: 1030975295, guid: abc00000000006504693431563510938, type: 3}
      propertyPath: m_LocalRotation.y
      value: 0.81757194
      objectReference: {fileID: 0}
    - target: {fileID: 1030975295, guid: abc00000000006504693431563510938, type: 3}
      propertyPath: m_LocalRotation.z
      value: 0.023937413
      objectReference: {fileID: 0}
    - target: {fileID: 1030975295, guid: abc00000000006504693431563510938, type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: -4.49
      objectReference: {fileID: 0}
    - target: {fileID: 1030975295, guid: abc00000000006504693431563510938, type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 109.89
      objectReference: {fileID: 0}
    - target: {fileID: 1030975295, guid: abc00000000006504693431563510938, type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: -1.622
      objectReference: {fileID: 0}
    - target: {fileID: 1030975295, guid: abc00000000006504693431563510938, type: 3}
      propertyPath: m_ConstrainProportionsScale
      value: 1
      objectReference: {fileID: 0}
    m_RemovedComponents: []
    m_RemovedGameObjects: []
    m_AddedGameObjects: []
    m_AddedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: abc00000000006504693431563510938, type: 3}
--- !u!4 &371304057 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: 1030975295, guid: abc00000000006504693431563510938, type: 3}
  m_PrefabInstance: {fileID: 371304056}
  m_PrefabAsset: {fileID: 0}
--- !u!1 &433576127
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 433576128}
  - component: {fileID: 433576130}
  - component: {fileID: 433576129}
  m_Layer: 5
  m_Name: SoundFXVolumeSettingLabelMax
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &433576128
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 433576127}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1658130456}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0.5, y: 0.5}
  m_AnchorMax: {x: 0.5, y: 0.5}
  m_AnchoredPosition: {x: 148.3, y: 0}
  m_SizeDelta: {x: 100, y: 100}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &433576129
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 433576127}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f4688fdb7df04437aeb418b961361dc5, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_text: MAX
  m_isRightToLeft: 0
  m_fontAsset: {fileID: 11400000, guid: 8f586378b4e144a9851e7b34d9b748ee, type: 2}
  m_sharedMaterial: {fileID: 2180264, guid: 8f586378b4e144a9851e7b34d9b748ee, type: 2}
  m_fontSharedMaterials: []
  m_fontMaterial: {fileID: 0}
  m_fontMaterials: []
  m_fontColor32:
    serializedVersion: 2
    rgba: 4294967295
  m_fontColor: {r: 1, g: 1, b: 1, a: 1}
  m_enableVertexGradient: 0
  m_colorMode: 3
  m_fontColorGradient:
    topLeft: {r: 1, g: 1, b: 1, a: 1}
    topRight: {r: 1, g: 1, b: 1, a: 1}
    bottomLeft: {r: 1, g: 1, b: 1, a: 1}
    bottomRight: {r: 1, g: 1, b: 1, a: 1}
  m_fontColorGradientPreset: {fileID: 0}
  m_spriteAsset: {fileID: 0}
  m_tintAllSprites: 0
  m_StyleSheet: {fileID: 0}
  m_TextStyleHashCode: -1183493901
  m_overrideHtmlColors: 0
  m_faceColor:
    serializedVersion: 2
    rgba: 4294967295
  m_fontSize: 20
  m_fontSizeBase: 20
  m_fontWeight: 400
  m_enableAutoSizing: 0
  m_fontSizeMin: 18
  m_fontSizeMax: 72
  m_fontStyle: 0
  m_HorizontalAlignment: 2
  m_VerticalAlignment: 512
  m_textAlignment: 65535
  m_characterSpacing: 0
  m_wordSpacing: 0
  m_lineSpacing: 0
  m_lineSpacingMax: 0
  m_paragraphSpacing: 0
  m_charWidthMaxAdj: 0
  m_TextWrappingMode: 0
  m_wordWrappingRatios: 0.4
  m_overflowMode: 0
  m_linkedTextComponent: {fileID: 0}
  parentLinkedComponent: {fileID: 0}
  m_enableKerning: 0
  m_ActiveFontFeatures: 6e72656b
  m_enableExtraPadding: 0
  checkPaddingRequired: 0
  m_isRichText: 1
  m_EmojiFallbackSupport: 1
  m_parseCtrlCharacters: 1
  m_isOrthographic: 1
  m_isCullingEnabled: 0
  m_horizontalMapping: 0
  m_verticalMapping: 0
  m_uvLineOffset: 0
  m_geometrySortingOrder: 0
  m_IsTextObjectScaleStatic: 0
  m_VertexBufferAutoSizeReduction: 0
  m_useMaxVisibleDescender: 1
  m_pageToDisplay: 1
  m_margin: {x: 0, y: 0, z: 0, w: 0}
  m_isUsingLegacyAnimationComponent: 0
  m_isVolumetricText: 0
  m_hasFontAssetChanged: 0
  m_baseMaterial: {fileID: 0}
  m_maskOffset: {x: 0, y: 0, z: 0, w: 0}
--- !u!222 &433576130
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 433576127}
  m_CullTransparentMesh: 1
--- !u!1 &440133746
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 440133750}
  - component: {fileID: 440133749}
  - component: {fileID: 440133748}
  - component: {fileID: 440133747}
  - component: {fileID: 440133751}
  m_Layer: 5
  m_Name: ForestMapUserInterface
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!114 &440133747
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 440133746}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: dc42784cf147c0c48a680349fa168899, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_IgnoreReversedGraphics: 1
  m_BlockingObjects: 0
  m_BlockingMask:
    serializedVersion: 2
    m_Bits: 4294967295
--- !u!114 &440133748
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 440133746}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 0cd44c1031e13a943bb63640046fad76, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_UiScaleMode: 1
  m_ReferencePixelsPerUnit: 100
  m_ScaleFactor: 1
  m_ReferenceResolution: {x: 1920, y: 1080}
  m_ScreenMatchMode: 0
  m_MatchWidthOrHeight: 1
  m_PhysicalUnit: 3
  m_FallbackScreenDPI: 96
  m_DefaultSpriteDPI: 96
  m_DynamicPixelsPerUnit: 1
  m_PresetInfoIsWorld: 0
--- !u!223 &440133749
Canvas:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 440133746}
  m_Enabled: 1
  serializedVersion: 3
  m_RenderMode: 1
  m_Camera: {fileID: 179687902}
  m_PlaneDistance: 0.67
  m_PixelPerfect: 0
  m_ReceivesEvents: 1
  m_OverrideSorting: 0
  m_OverridePixelPerfect: 0
  m_SortingBucketNormalizedSize: 0
  m_VertexColorAlwaysGammaSpace: 0
  m_AdditionalShaderChannelsFlag: 25
  m_UpdateRectTransformForStandalone: 0
  m_SortingLayerID: 0
  m_SortingOrder: 1
  m_TargetDisplay: 0
--- !u!224 &440133750
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 440133746}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 0, y: 0, z: 0}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 189974504}
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 0, y: 0}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 0, y: 0}
  m_Pivot: {x: 0, y: 0}
--- !u!114 &440133751
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 440133746}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: bce09ffa8afef234da3c4230e47d633c, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  settingsPanel: {fileID: 1238260546}
--- !u!1 &443102733
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 443102734}
  - component: {fileID: 443102736}
  - component: {fileID: 443102735}
  m_Layer: 5
  m_Name: SettingsTitle
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &443102734
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 443102733}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1238260545}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 1}
  m_AnchorMax: {x: 0, y: 1}
  m_AnchoredPosition: {x: 200, y: -42.5}
  m_SizeDelta: {x: 400, y: 50}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &443102735
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 443102733}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f4688fdb7df04437aeb418b961361dc5, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_text: SETTINGS
  m_isRightToLeft: 0
  m_fontAsset: {fileID: 11400000, guid: 8f586378b4e144a9851e7b34d9b748ee, type: 2}
  m_sharedMaterial: {fileID: 2180264, guid: 8f586378b4e144a9851e7b34d9b748ee, type: 2}
  m_fontSharedMaterials: []
  m_fontMaterial: {fileID: 0}
  m_fontMaterials: []
  m_fontColor32:
    serializedVersion: 2
    rgba: 4294967295
  m_fontColor: {r: 1, g: 1, b: 1, a: 1}
  m_enableVertexGradient: 0
  m_colorMode: 3
  m_fontColorGradient:
    topLeft: {r: 1, g: 1, b: 1, a: 1}
    topRight: {r: 1, g: 1, b: 1, a: 1}
    bottomLeft: {r: 1, g: 1, b: 1, a: 1}
    bottomRight: {r: 1, g: 1, b: 1, a: 1}
  m_fontColorGradientPreset: {fileID: 0}
  m_spriteAsset: {fileID: 0}
  m_tintAllSprites: 0
  m_StyleSheet: {fileID: 0}
  m_TextStyleHashCode: -1183493901
  m_overrideHtmlColors: 0
  m_faceColor:
    serializedVersion: 2
    rgba: 4294967295
  m_fontSize: 36
  m_fontSizeBase: 36
  m_fontWeight: 400
  m_enableAutoSizing: 0
  m_fontSizeMin: 18
  m_fontSizeMax: 72
  m_fontStyle: 1
  m_HorizontalAlignment: 2
  m_VerticalAlignment: 512
  m_textAlignment: 65535
  m_characterSpacing: 0
  m_wordSpacing: 0
  m_lineSpacing: 0
  m_lineSpacingMax: 0
  m_paragraphSpacing: 0
  m_charWidthMaxAdj: 0
  m_TextWrappingMode: 0
  m_wordWrappingRatios: 0.4
  m_overflowMode: 0
  m_linkedTextComponent: {fileID: 0}
  parentLinkedComponent: {fileID: 0}
  m_enableKerning: 0
  m_ActiveFontFeatures: 6e72656b
  m_enableExtraPadding: 0
  checkPaddingRequired: 0
  m_isRichText: 1
  m_EmojiFallbackSupport: 1
  m_parseCtrlCharacters: 1
  m_isOrthographic: 1
  m_isCullingEnabled: 0
  m_horizontalMapping: 0
  m_verticalMapping: 0
  m_uvLineOffset: 0
  m_geometrySortingOrder: 0
  m_IsTextObjectScaleStatic: 0
  m_VertexBufferAutoSizeReduction: 0
  m_useMaxVisibleDescender: 1
  m_pageToDisplay: 1
  m_margin: {x: 0, y: 0, z: 0, w: 0}
  m_isUsingLegacyAnimationComponent: 0
  m_isVolumetricText: 0
  m_hasFontAssetChanged: 0
  m_baseMaterial: {fileID: 0}
  m_maskOffset: {x: 0, y: 0, z: 0, w: 0}
--- !u!222 &443102736
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 443102733}
  m_CullTransparentMesh: 1
--- !u!134 &499636778
PhysicsMaterial:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: 
  serializedVersion: 2
  m_DynamicFriction: 0.6
  m_StaticFriction: 0.6
  m_Bounciness: 0
  m_FrictionCombine: 0
  m_BounceCombine: 0
--- !u!1001 &507537393
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    serializedVersion: 3
    m_TransformParent: {fileID: 244853904}
    m_Modifications:
    - target: {fileID: 1031059011, guid: abc00000000007020720680617858536, type: 3}
      propertyPath: m_Name
      value: SM_FFloorMiddle450x454_33
      objectReference: {fileID: 0}
    - target: {fileID: 1031059012, guid: abc00000000007020720680617858536, type: 3}
      propertyPath: m_LocalScale.x
      value: 2.5
      objectReference: {fileID: 0}
    - target: {fileID: 1031059012, guid: abc00000000007020720680617858536, type: 3}
      propertyPath: m_LocalScale.y
      value: 1.5891
      objectReference: {fileID: 0}
    - target: {fileID: 1031059012, guid: abc00000000007020720680617858536, type: 3}
      propertyPath: m_LocalScale.z
      value: 2.5
      objectReference: {fileID: 0}
    - target: {fileID: 1031059012, guid: abc00000000007020720680617858536, type: 3}
      propertyPath: m_LocalPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1031059012, guid: abc00000000007020720680617858536, type: 3}
      propertyPath: m_LocalPosition.y
      value: -0.287
      objectReference: {fileID: 0}
    - target: {fileID: 1031059012, guid: abc00000000007020720680617858536, type: 3}
      propertyPath: m_LocalPosition.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1031059012, guid: abc00000000007020720680617858536, type: 3}
      propertyPath: m_LocalRotation.w
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 1031059012, guid: abc00000000007020720680617858536, type: 3}
      propertyPath: m_LocalRotation.x
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 1031059012, guid: abc00000000007020720680617858536, type: 3}
      propertyPath: m_LocalRotation.y
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 1031059012, guid: abc00000000007020720680617858536, type: 3}
      propertyPath: m_LocalRotation.z
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 1031059012, guid: abc00000000007020720680617858536, type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1031059012, guid: abc00000000007020720680617858536, type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1031059012, guid: abc00000000007020720680617858536, type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1031059012, guid: abc00000000007020720680617858536, type: 3}
      propertyPath: m_ConstrainProportionsScale
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1031059013, guid: abc00000000007020720680617858536, type: 3}
      propertyPath: m_Size.z
      value: 4.5
      objectReference: {fileID: 0}
    - target: {fileID: 1031059013, guid: abc00000000007020720680617858536, type: 3}
      propertyPath: m_Material
      value: 
      objectReference: {fileID: 941483613}
    m_RemovedComponents: []
    m_RemovedGameObjects: []
    m_AddedGameObjects: []
    m_AddedComponents:
    - targetCorrespondingSourceObject: {fileID: 1031059011, guid: abc00000000007020720680617858536, type: 3}
      insertIndex: -1
      addedObject: {fileID: 507537396}
  m_SourcePrefab: {fileID: 100100000, guid: abc00000000007020720680617858536, type: 3}
--- !u!4 &507537394 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: 1031059012, guid: abc00000000007020720680617858536, type: 3}
  m_PrefabInstance: {fileID: 507537393}
  m_PrefabAsset: {fileID: 0}
--- !u!1 &507537395 stripped
GameObject:
  m_CorrespondingSourceObject: {fileID: 1031059011, guid: abc00000000007020720680617858536, type: 3}
  m_PrefabInstance: {fileID: 507537393}
  m_PrefabAsset: {fileID: 0}
--- !u!114 &507537396
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 507537395}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 7a5ac11cc976e418e8d13136b07e1f52, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_SerializedVersion: 0
  m_AgentTypeID: 0
  m_CollectObjects: 0
  m_Size: {x: 10, y: 10, z: 10}
  m_Center: {x: 0, y: 2, z: 0}
  m_LayerMask:
    serializedVersion: 2
    m_Bits: 4294967295
  m_UseGeometry: 0
  m_DefaultArea: 0
  m_GenerateLinks: 0
  m_IgnoreNavMeshAgent: 1
  m_IgnoreNavMeshObstacle: 1
  m_OverrideTileSize: 0
  m_TileSize: 256
  m_OverrideVoxelSize: 0
  m_VoxelSize: 0.16666667
  m_MinRegionArea: 2
  m_NavMeshData: {fileID: 23800000, guid: 8220472425cbf1b49afd53b184186b38, type: 2}
  m_BuildHeightMesh: 0
--- !u!1 &514748379
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 514748380}
  m_Layer: 5
  m_Name: Fill Area
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &514748380
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 514748379}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 2078784409}
  m_Father: {fileID: 2120279515}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0.25}
  m_AnchorMax: {x: 1, y: 0.75}
  m_AnchoredPosition: {x: -4.9999847, y: 0}
  m_SizeDelta: {x: -20, y: 0}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!1 &519976996
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 519976997}
  m_Layer: 0
  m_Name: CampsiteLocation
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &519976997
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 519976996}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 556.11, y: 6.21, z: 318.23}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 708659531}
  - {fileID: 753665810}
  - {fileID: 630522170}
  - {fileID: 1086602229}
  - {fileID: 1228985242}
  - {fileID: 1133823111}
  - {fileID: 1558815746}
  - {fileID: 331287265}
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &542679763
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 542679764}
  m_Layer: 0
  m_Name: SpawnPoint_1
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &542679764
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 542679763}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 896623775}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &555802033
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 555802034}
  - component: {fileID: 555802036}
  - component: {fileID: 555802035}
  m_Layer: 5
  m_Name: Handle
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &555802034
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 555802033}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1147994954}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 0, y: 0}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 20, y: 0}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &555802035
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 555802033}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fe87c0e1cc204ed48ad3b37840f39efc, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_Sprite: {fileID: 10913, guid: 0000000000000000f000000000000000, type: 0}
  m_Type: 0
  m_PreserveAspect: 0
  m_FillCenter: 1
  m_FillMethod: 4
  m_FillAmount: 1
  m_FillClockwise: 1
  m_FillOrigin: 0
  m_UseSpriteMesh: 0
  m_PixelsPerUnitMultiplier: 1
--- !u!222 &555802036
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 555802033}
  m_CullTransparentMesh: 1
--- !u!1001 &585421228
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    serializedVersion: 3
    m_TransformParent: {fileID: 708659531}
    m_Modifications:
    - target: {fileID: 1031081025, guid: abc00000000006132371766603354423, type: 3}
      propertyPath: m_Name
      value: SM_Monitor_01a2
      objectReference: {fileID: 0}
    - target: {fileID: 1031081026, guid: abc00000000006132371766603354423, type: 3}
      propertyPath: m_LocalScale.x
      value: 2.5
      objectReference: {fileID: 0}
    - target: {fileID: 1031081026, guid: abc00000000006132371766603354423, type: 3}
      propertyPath: m_LocalScale.y
      value: 2.5
      objectReference: {fileID: 0}
    - target: {fileID: 1031081026, guid: abc00000000006132371766603354423, type: 3}
      propertyPath: m_LocalScale.z
      value: 2.5
      objectReference: {fileID: 0}
    - target: {fileID: 1031081026, guid: abc00000000006132371766603354423, type: 3}
      propertyPath: m_LocalPosition.x
      value: -5.805725
      objectReference: {fileID: 0}
    - target: {fileID: 1031081026, guid: abc00000000006132371766603354423, type: 3}
      propertyPath: m_LocalPosition.y
      value: 2.3430002
      objectReference: {fileID: 0}
    - target: {fileID: 1031081026, guid: abc00000000006132371766603354423, type: 3}
      propertyPath: m_LocalPosition.z
      value: 10.083984
      objectReference: {fileID: 0}
    - target: {fileID: 1031081026, guid: abc00000000006132371766603354423, type: 3}
      propertyPath: m_LocalRotation.w
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 1031081026, guid: abc00000000006132371766603354423, type: 3}
      propertyPath: m_LocalRotation.x
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 1031081026, guid: abc00000000006132371766603354423, type: 3}
      propertyPath: m_LocalRotation.y
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 1031081026, guid: abc00000000006132371766603354423, type: 3}
      propertyPath: m_LocalRotation.z
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 1031081026, guid: abc00000000006132371766603354423, type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1031081026, guid: abc00000000006132371766603354423, type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1031081026, guid: abc00000000006132371766603354423, type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1031081026, guid: abc00000000006132371766603354423, type: 3}
      propertyPath: m_ConstrainProportionsScale
      value: 1
      objectReference: {fileID: 0}
    m_RemovedComponents: []
    m_RemovedGameObjects: []
    m_AddedGameObjects: []
    m_AddedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: abc00000000006132371766603354423, type: 3}
--- !u!4 &585421229 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: 1031081026, guid: abc00000000006132371766603354423, type: 3}
  m_PrefabInstance: {fileID: 585421228}
  m_PrefabAsset: {fileID: 0}
--- !u!1 &590927385
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 590927388}
  - component: {fileID: 590927387}
  - component: {fileID: 590927386}
  m_Layer: 0
  m_Name: Sun
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!114 &590927386
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 590927385}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 7a68c43fe1f2a47cfa234b5eeaa98012, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_PointlightHDType: 0
  m_SpotLightShape: 0
  m_AreaLightShape: 0
  m_EnableSpotReflector: 0
  m_LightUnit: 2
  m_LuxAtDistance: 1
  m_Intensity: 130000
  m_InnerSpotPercent: 0
  m_SpotIESCutoffPercent: 100
  m_LightDimmer: 1
  m_VolumetricDimmer: 1
  m_FadeDistance: 10000
  m_VolumetricFadeDistance: 10000
  m_AffectDiffuse: 1
  m_AffectSpecular: 1
  m_NonLightmappedOnly: 0
  m_ShapeWidth: 0.5
  m_ShapeHeight: 0.5
  m_AspectRatio: 1
  m_ShapeRadius: 0.025
  m_SoftnessScale: 1
  m_UseCustomSpotLightShadowCone: 0
  m_CustomSpotLightShadowCone: 30
  m_MaxSmoothness: 0.99
  m_ApplyRangeAttenuation: 1
  m_DisplayAreaLightEmissiveMesh: 0
  m_AreaLightCookie: {fileID: 0}
  m_IESPoint: {fileID: 0}
  m_IESSpot: {fileID: 0}
  m_IncludeForRayTracing: 1
  m_IncludeForPathTracing: 1
  m_AreaLightShadowCone: 120
  m_UseScreenSpaceShadows: 0
  m_InteractsWithSky: 1
  m_AngularDiameter: 9.2
  diameterMultiplerMode: 0
  diameterMultiplier: 1
  diameterOverride: 0.5
  celestialBodyShadingSource: 1
  sunLightOverride: {fileID: 0}
  sunColor: {r: 1, g: 1, b: 1, a: 1}
  sunIntensity: 130000
  moonPhase: 0.2
  moonPhaseRotation: 0
  earthshine: 1
  flareSize: 2
  flareTint: {r: 1, g: 1, b: 1, a: 1}
  flareFalloff: 4
  flareMultiplier: 1
  surfaceTexture: {fileID: 0}
  surfaceTint: {r: 1, g: 1, b: 1, a: 1}
  m_Distance: 1.5e+11
  m_UseRayTracedShadows: 0
  m_NumRayTracingSamples: 4
  m_FilterTracedShadow: 1
  m_FilterSizeTraced: 16
  m_SunLightConeAngle: 0.5
  m_LightShadowRadius: 0.5
  m_SemiTransparentShadow: 0
  m_ColorShadow: 1
  m_DistanceBasedFiltering: 0
  m_EvsmExponent: 15
  m_EvsmLightLeakBias: 0
  m_EvsmVarianceBias: 0.00001
  m_EvsmBlurPasses: 0
  m_LightlayersMask: 1
  m_LinkShadowLayers: 1
  m_ShadowNearPlane: 0.1
  m_BlockerSampleCount: 24
  m_FilterSampleCount: 16
  m_MinFilterSize: 0.1
  m_DirLightPCSSBlockerSampleCount: 24
  m_DirLightPCSSFilterSampleCount: 16
  m_DirLightPCSSMaxPenumbraSize: 0.56
  m_DirLightPCSSMaxSamplingDistance: 0.5
  m_DirLightPCSSMinFilterSizeTexels: 1.5
  m_DirLightPCSSMinFilterMaxAngularDiameter: 10
  m_DirLightPCSSBlockerSearchAngularDiameter: 12
  m_DirLightPCSSBlockerSamplingClumpExponent: 2
  m_KernelSize: 5
  m_LightAngle: 1
  m_MaxDepthBias: 0.001
  m_ShadowResolution:
    m_Override: 512
    m_UseOverride: 1
    m_Level: 0
  m_ShadowDimmer: 1
  m_VolumetricShadowDimmer: 1
  m_ShadowFadeDistance: 10000
  m_UseContactShadow:
    m_Override: 0
    m_UseOverride: 1
    m_Level: 0
  m_RayTracedContactShadow: 0
  m_ShadowTint: {r: 0, g: 0, b: 0, a: 1}
  m_PenumbraTint: 0
  m_NormalBias: 0.75
  m_SlopeBias: 0.5
  m_ShadowUpdateMode: 0
  m_AlwaysDrawDynamicShadows: 0
  m_UpdateShadowOnLightMovement: 0
  m_CachedShadowTranslationThreshold: 0.01
  m_CachedShadowAngularThreshold: 0.5
  m_BarnDoorAngle: 90
  m_BarnDoorLength: 0.05
  m_preserveCachedShadow: 0
  m_OnDemandShadowRenderOnPlacement: 1
  m_ShadowCascadeRatios:
  - 0.05
  - 0.2
  - 0.3
  m_ShadowCascadeBorders:
  - 0.2
  - 0.2
  - 0.2
  - 0.2
  m_ShadowAlgorithm: 0
  m_ShadowVariant: 0
  m_ShadowPrecision: 0
  useOldInspector: 0
  useVolumetric: 1
  featuresFoldout: 1
  m_AreaLightEmissiveMeshShadowCastingMode: 0
  m_AreaLightEmissiveMeshMotionVectorGenerationMode: 0
  m_AreaLightEmissiveMeshLayer: -1
  m_Version: 13
  m_ObsoleteShadowResolutionTier: 1
  m_ObsoleteUseShadowQualitySettings: 0
  m_ObsoleteCustomShadowResolution: 512
  m_ObsoleteContactShadows: 0
--- !u!108 &590927387
Light:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 590927385}
  m_Enabled: 1
  serializedVersion: 11
  m_Type: 1
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_Intensity: 1784.2025
  m_Range: 10
  m_SpotAngle: 30
  m_InnerSpotAngle: 21.80208
  m_CookieSize: 10
  m_Shadows:
    m_Type: 1
    m_Resolution: -1
    m_CustomResolution: -1
    m_Strength: 1
    m_Bias: 0.05
    m_NormalBias: 0.4
    m_NearPlane: 0.2
    m_CullingMatrixOverride:
      e00: 1
      e01: 0
      e02: 0
      e03: 0
      e10: 0
      e11: 1
      e12: 0
      e13: 0
      e20: 0
      e21: 0
      e22: 1
      e23: 0
      e30: 0
      e31: 0
      e32: 0
      e33: 1
    m_UseCullingMatrixOverride: 0
  m_Cookie: {fileID: 0}
  m_DrawHalo: 0
  m_Flare: {fileID: 0}
  m_RenderMode: 0
  m_CullingMask:
    serializedVersion: 2
    m_Bits: 4294967295
  m_RenderingLayerMask: 1
  m_Lightmapping: 4
  m_LightShadowCasterMode: 2
  m_AreaSize: {x: 0.5, y: 0.5}
  m_BounceIntensity: 1
  m_ColorTemperature: 12045
  m_UseColorTemperature: 1
  m_BoundingSphereOverride: {x: 0, y: 0, z: 0, w: 0}
  m_UseBoundingSphereOverride: 0
  m_UseViewFrustumForShadowCasterCull: 1
  m_ForceVisible: 0
  m_ShadowRadius: 0
  m_ShadowAngle: 9.2
  m_LightUnit: 2
  m_LuxAtDistance: 1
  m_EnableSpotReflector: 0
--- !u!4 &590927388
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 590927385}
  serializedVersion: 2
  m_LocalRotation: {x: -0.97357535, y: -0.025639962, z: 0.13942702, w: -0.17903586}
  m_LocalPosition: {x: 414, y: 3, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 159.16, y: 376.3, z: 0}
--- !u!1001 &630522169
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    serializedVersion: 3
    m_TransformParent: {fileID: 519976997}
    m_Modifications:
    - target: {fileID: 1031088684, guid: abc00000000014341865315992137117, type: 3}
      propertyPath: m_Name
      value: LightTower_164
      objectReference: {fileID: 0}
    - target: {fileID: 1031088685, guid: abc00000000014341865315992137117, type: 3}
      propertyPath: m_LocalScale.x
      value: 2.3
      objectReference: {fileID: 0}
    - target: {fileID: 1031088685, guid: abc00000000014341865315992137117, type: 3}
      propertyPath: m_LocalScale.y
      value: 2.3
      objectReference: {fileID: 0}
    - target: {fileID: 1031088685, guid: abc00000000014341865315992137117, type: 3}
      propertyPath: m_LocalScale.z
      value: 2.3
      objectReference: {fileID: 0}
    - target: {fileID: 1031088685, guid: abc00000000014341865315992137117, type: 3}
      propertyPath: m_LocalPosition.x
      value: 19.450012
      objectReference: {fileID: 0}
    - target: {fileID: 1031088685, guid: abc00000000014341865315992137117, type: 3}
      propertyPath: m_LocalPosition.y
      value: -4.206078
      objectReference: {fileID: 0}
    - target: {fileID: 1031088685, guid: abc00000000014341865315992137117, type: 3}
      propertyPath: m_LocalPosition.z
      value: -10.75
      objectReference: {fileID: 0}
    - target: {fileID: 1031088685, guid: abc00000000014341865315992137117, type: 3}
      propertyPath: m_LocalRotation.w
      value: 0.9986373
      objectReference: {fileID: 0}
    - target: {fileID: 1031088685, guid: abc00000000014341865315992137117, type: 3}
      propertyPath: m_LocalRotation.x
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 1031088685, guid: abc00000000014341865315992137117, type: 3}
      propertyPath: m_LocalRotation.y
      value: 0.05218817
      objectReference: {fileID: 0}
    - target: {fileID: 1031088685, guid: abc00000000014341865315992137117, type: 3}
      propertyPath: m_LocalRotation.z
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 1031088685, guid: abc00000000014341865315992137117, type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1031088685, guid: abc00000000014341865315992137117, type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 5.983
      objectReference: {fileID: 0}
    - target: {fileID: 1031088685, guid: abc00000000014341865315992137117, type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1031088685, guid: abc00000000014341865315992137117, type: 3}
      propertyPath: m_ConstrainProportionsScale
      value: 1
      objectReference: {fileID: 0}
    m_RemovedComponents: []
    m_RemovedGameObjects: []
    m_AddedGameObjects: []
    m_AddedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: abc00000000014341865315992137117, type: 3}
--- !u!4 &630522170 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: 1031088685, guid: abc00000000014341865315992137117, type: 3}
  m_PrefabInstance: {fileID: 630522169}
  m_PrefabAsset: {fileID: 0}
--- !u!1 &641934373
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 641934374}
  m_Layer: 0
  m_Name: SpawnPoint
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &641934374
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 641934373}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 2147199879}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &666247743
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 666247744}
  m_Layer: 0
  m_Name: Colliders
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &666247744
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 666247743}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 90960998}
  - {fileID: 1925205526}
  - {fileID: 1670769542}
  - {fileID: 244927040}
  m_Father: {fileID: 543450653026896001}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &692591511
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 692591513}
  - component: {fileID: 692591512}
  m_Layer: 0
  m_Name: UISoundFXAudioSource
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!82 &692591512
AudioSource:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 692591511}
  m_Enabled: 1
  serializedVersion: 4
  OutputAudioMixerGroup: {fileID: 0}
  m_audioClip: {fileID: 0}
  m_Resource: {fileID: 0}
  m_PlayOnAwake: 1
  m_Volume: 1
  m_Pitch: 1
  Loop: 0
  Mute: 0
  Spatialize: 0
  SpatializePostEffects: 0
  Priority: 128
  DopplerLevel: 1
  MinDistance: 1
  MaxDistance: 500
  Pan2D: 0
  rolloffMode: 0
  BypassEffects: 0
  BypassListenerEffects: 0
  BypassReverbZones: 0
  rolloffCustomCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 0
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  panLevelCustomCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  spreadCustomCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  reverbZoneMixCustomCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
--- !u!4 &692591513
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 692591511}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1439646037}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &708659530
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 708659531}
  m_Layer: 0
  m_Name: Tent
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &708659531
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 708659530}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: -3.2399902, y: -4.226, z: 6.5}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4904207304937004223}
  - {fileID: 1887566593}
  - {fileID: 2106630467}
  - {fileID: 848146119}
  - {fileID: 368253826}
  - {fileID: 295616944}
  - {fileID: 1197962280}
  - {fileID: 1705384076}
  - {fileID: 585421229}
  - {fileID: 868000375}
  - {fileID: 1480547228}
  - {fileID: 1166037172}
  - {fileID: 1651501498}
  - {fileID: 1426693937}
  m_Father: {fileID: 519976997}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1001 &753665809
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    serializedVersion: 3
    m_TransformParent: {fileID: 519976997}
    m_Modifications:
    - target: {fileID: 1031088684, guid: abc00000000014341865315992137117, type: 3}
      propertyPath: m_Name
      value: LightTower_163
      objectReference: {fileID: 0}
    - target: {fileID: 1031088685, guid: abc00000000014341865315992137117, type: 3}
      propertyPath: m_LocalScale.x
      value: 2.3
      objectReference: {fileID: 0}
    - target: {fileID: 1031088685, guid: abc00000000014341865315992137117, type: 3}
      propertyPath: m_LocalScale.y
      value: 2.3
      objectReference: {fileID: 0}
    - target: {fileID: 1031088685, guid: abc00000000014341865315992137117, type: 3}
      propertyPath: m_LocalScale.z
      value: 2.3
      objectReference: {fileID: 0}
    - target: {fileID: 1031088685, guid: abc00000000014341865315992137117, type: 3}
      propertyPath: m_LocalPosition.x
      value: 5.5264893
      objectReference: {fileID: 0}
    - target: {fileID: 1031088685, guid: abc00000000014341865315992137117, type: 3}
      propertyPath: m_LocalPosition.y
      value: -4.206078
      objectReference: {fileID: 0}
    - target: {fileID: 1031088685, guid: abc00000000014341865315992137117, type: 3}
      propertyPath: m_LocalPosition.z
      value: 19.510712
      objectReference: {fileID: 0}
    - target: {fileID: 1031088685, guid: abc00000000014341865315992137117, type: 3}
      propertyPath: m_LocalRotation.w
      value: 0.88934153
      objectReference: {fileID: 0}
    - target: {fileID: 1031088685, guid: abc00000000014341865315992137117, type: 3}
      propertyPath: m_LocalRotation.x
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 1031088685, guid: abc00000000014341865315992137117, type: 3}
      propertyPath: m_LocalRotation.y
      value: -0.45724362
      objectReference: {fileID: 0}
    - target: {fileID: 1031088685, guid: abc00000000014341865315992137117, type: 3}
      propertyPath: m_LocalRotation.z
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 1031088685, guid: abc00000000014341865315992137117, type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1031088685, guid: abc00000000014341865315992137117, type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: -54.419
      objectReference: {fileID: 0}
    - target: {fileID: 1031088685, guid: abc00000000014341865315992137117, type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1031088685, guid: abc00000000014341865315992137117, type: 3}
      propertyPath: m_ConstrainProportionsScale
      value: 1
      objectReference: {fileID: 0}
    m_RemovedComponents: []
    m_RemovedGameObjects: []
    m_AddedGameObjects: []
    m_AddedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: abc00000000014341865315992137117, type: 3}
--- !u!4 &753665810 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: 1031088685, guid: abc00000000014341865315992137117, type: 3}
  m_PrefabInstance: {fileID: 753665809}
  m_PrefabAsset: {fileID: 0}
--- !u!1 &808853746
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 808853747}
  - component: {fileID: 808853750}
  - component: {fileID: 808853749}
  - component: {fileID: 808853748}
  m_Layer: 0
  m_Name: Cube_1
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &808853747
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 808853746}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 6, y: 0.5109999, z: 7.6789856}
  m_LocalScale: {x: 7.7096415, y: 0.001, z: 0.29843688}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 2073337801}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!65 &808853748
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 808853746}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 3
  m_Size: {x: 1, y: 1, z: 1}
  m_Center: {x: 0, y: 0, z: 0}
--- !u!23 &808853749
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 808853746}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: c9ef99fcd36d4d34da42acc2c4a49379, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!33 &808853750
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 808853746}
  m_Mesh: {fileID: 10202, guid: 0000000000000000e000000000000000, type: 0}
--- !u!1 &809916323
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 809916324}
  - component: {fileID: 809916327}
  - component: {fileID: 809916326}
  - component: {fileID: 809916325}
  m_Layer: 0
  m_Name: Cube_2
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &809916324
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 809916323}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: 0.7071068, z: -0, w: 0.7071068}
  m_LocalPosition: {x: 5.8099976, y: 0.5109999, z: 5.395996}
  m_LocalScale: {x: 4.272373, y: 0.001, z: 0.29843688}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 216111616}
  m_LocalEulerAnglesHint: {x: 0, y: 90, z: 0}
--- !u!65 &809916325
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 809916323}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 3
  m_Size: {x: 1, y: 1, z: 1}
  m_Center: {x: 0, y: 0, z: 0}
--- !u!23 &809916326
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 809916323}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: c9ef99fcd36d4d34da42acc2c4a49379, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!33 &809916327
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 809916323}
  m_Mesh: {fileID: 10202, guid: 0000000000000000e000000000000000, type: 0}
--- !u!1 &810982373
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 810982375}
  - component: {fileID: 810982374}
  m_Layer: 0
  m_Name: MusicAudioSource
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!82 &810982374
AudioSource:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 810982373}
  m_Enabled: 1
  serializedVersion: 4
  OutputAudioMixerGroup: {fileID: 0}
  m_audioClip: {fileID: 0}
  m_Resource: {fileID: 0}
  m_PlayOnAwake: 1
  m_Volume: 1
  m_Pitch: 1
  Loop: 0
  Mute: 0
  Spatialize: 0
  SpatializePostEffects: 0
  Priority: 128
  DopplerLevel: 1
  MinDistance: 1
  MaxDistance: 500
  Pan2D: 0
  rolloffMode: 0
  BypassEffects: 0
  BypassListenerEffects: 0
  BypassReverbZones: 0
  rolloffCustomCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 0
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  panLevelCustomCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  spreadCustomCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  reverbZoneMixCustomCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
--- !u!4 &810982375
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 810982373}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1439646037}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &811078242
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 811078245}
  - component: {fileID: 811078244}
  - component: {fileID: 811078243}
  m_Layer: 0
  m_Name: Colorchecker Geometry
  m_TagString: EditorOnly
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!33 &811078243
MeshFilter:
  m_ObjectHideFlags: 8
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 811078242}
  m_Mesh: {fileID: 0}
--- !u!23 &811078244
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 811078242}
  m_Enabled: 1
  m_CastShadows: 0
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 7a1044256d39dba4aa777255550527bf, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!4 &811078245
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 811078242}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1423917792}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1001 &848146118
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    serializedVersion: 3
    m_TransformParent: {fileID: 708659531}
    m_Modifications:
    - target: {fileID: 4349889308364899038, guid: 6e55e6f93c1f7994a921018c2285a659, type: 3}
      propertyPath: m_LocalPosition.x
      value: -0.17999268
      objectReference: {fileID: 0}
    - target: {fileID: 4349889308364899038, guid: 6e55e6f93c1f7994a921018c2285a659, type: 3}
      propertyPath: m_LocalPosition.y
      value: 0.022486448
      objectReference: {fileID: 0}
    - target: {fileID: 4349889308364899038, guid: 6e55e6f93c1f7994a921018c2285a659, type: 3}
      propertyPath: m_LocalPosition.z
      value: -3.6200256
      objectReference: {fileID: 0}
    - target: {fileID: 4349889308364899038, guid: 6e55e6f93c1f7994a921018c2285a659, type: 3}
      propertyPath: m_LocalRotation.w
      value: 0.48203933
      objectReference: {fileID: 0}
    - target: {fileID: 4349889308364899038, guid: 6e55e6f93c1f7994a921018c2285a659, type: 3}
      propertyPath: m_LocalRotation.x
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 4349889308364899038, guid: 6e55e6f93c1f7994a921018c2285a659, type: 3}
      propertyPath: m_LocalRotation.y
      value: 0.87614965
      objectReference: {fileID: 0}
    - target: {fileID: 4349889308364899038, guid: 6e55e6f93c1f7994a921018c2285a659, type: 3}
      propertyPath: m_LocalRotation.z
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 4349889308364899038, guid: 6e55e6f93c1f7994a921018c2285a659, type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4349889308364899038, guid: 6e55e6f93c1f7994a921018c2285a659, type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 122.363
      objectReference: {fileID: 0}
    - target: {fileID: 4349889308364899038, guid: 6e55e6f93c1f7994a921018c2285a659, type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8982426684407628973, guid: 6e55e6f93c1f7994a921018c2285a659, type: 3}
      propertyPath: m_Name
      value: FoldableChairPrefab_3
      objectReference: {fileID: 0}
    m_RemovedComponents: []
    m_RemovedGameObjects: []
    m_AddedGameObjects: []
    m_AddedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: 6e55e6f93c1f7994a921018c2285a659, type: 3}
--- !u!4 &848146119 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: 4349889308364899038, guid: 6e55e6f93c1f7994a921018c2285a659, type: 3}
  m_PrefabInstance: {fileID: 848146118}
  m_PrefabAsset: {fileID: 0}
--- !u!1001 &868000374
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    serializedVersion: 3
    m_TransformParent: {fileID: 708659531}
    m_Modifications:
    - target: {fileID: 1031104985, guid: abc00000000017280041634371412424, type: 3}
      propertyPath: m_Name
      value: SM_Mouse_01a_108
      objectReference: {fileID: 0}
    - target: {fileID: 1031104986, guid: abc00000000017280041634371412424, type: 3}
      propertyPath: m_LocalScale.x
      value: 2.3
      objectReference: {fileID: 0}
    - target: {fileID: 1031104986, guid: abc00000000017280041634371412424, type: 3}
      propertyPath: m_LocalScale.y
      value: 2.3
      objectReference: {fileID: 0}
    - target: {fileID: 1031104986, guid: abc00000000017280041634371412424, type: 3}
      propertyPath: m_LocalScale.z
      value: 2.3
      objectReference: {fileID: 0}
    - target: {fileID: 1031104986, guid: abc00000000017280041634371412424, type: 3}
      propertyPath: m_LocalPosition.x
      value: -4.9818115
      objectReference: {fileID: 0}
    - target: {fileID: 1031104986, guid: abc00000000017280041634371412424, type: 3}
      propertyPath: m_LocalPosition.y
      value: 1.8870001
      objectReference: {fileID: 0}
    - target: {fileID: 1031104986, guid: abc00000000017280041634371412424, type: 3}
      propertyPath: m_LocalPosition.z
      value: 9.389984
      objectReference: {fileID: 0}
    - target: {fileID: 1031104986, guid: abc00000000017280041634371412424, type: 3}
      propertyPath: m_LocalRotation.w
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 1031104986, guid: abc00000000017280041634371412424, type: 3}
      propertyPath: m_LocalRotation.x
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 1031104986, guid: abc00000000017280041634371412424, type: 3}
      propertyPath: m_LocalRotation.y
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 1031104986, guid: abc00000000017280041634371412424, type: 3}
      propertyPath: m_LocalRotation.z
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 1031104986, guid: abc00000000017280041634371412424, type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1031104986, guid: abc00000000017280041634371412424, type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1031104986, guid: abc00000000017280041634371412424, type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1031104986, guid: abc00000000017280041634371412424, type: 3}
      propertyPath: m_ConstrainProportionsScale
      value: 1
      objectReference: {fileID: 0}
    m_RemovedComponents: []
    m_RemovedGameObjects: []
    m_AddedGameObjects: []
    m_AddedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: abc00000000017280041634371412424, type: 3}
--- !u!4 &868000375 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: 1031104986, guid: abc00000000017280041634371412424, type: 3}
  m_PrefabInstance: {fileID: 868000374}
  m_PrefabAsset: {fileID: 0}
--- !u!1 &868595560
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 868595561}
  m_Layer: 5
  m_Name: Handle Slide Area
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &868595561
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 868595560}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 2112910344}
  m_Father: {fileID: 2120279515}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 1, y: 1}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: -20, y: 0}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!1 &869431279
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 869431280}
  m_Layer: 5
  m_Name: SoundFXVolumeSetting
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &869431280
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 869431279}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0.00028129626}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 214815528}
  m_Father: {fileID: 1341548309}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 0, y: 0}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 0, y: 80.666664}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!1 &872838982
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 872838983}
  - component: {fileID: 872839032}
  - component: {fileID: 872839033}
  m_Layer: 0
  m_Name: Helipad
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 2147483647
  m_IsActive: 0
--- !u!4 &872838983
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 872838982}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 6.448999, y: -0.13599996, z: -6.698999}
  m_LocalScale: {x: 0.1, y: 0.1, z: 0.1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 872839035}
  - {fileID: 872839039}
  - {fileID: 872839027}
  m_Father: {fileID: 1675261855}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!23 &872839024
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 872839038}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 7df4602e1a480434eac3bc7fde90e794, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 1
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!33 &872839025
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 872839038}
  m_Mesh: {fileID: 4300002, guid: abc00000000009096612966278784113, type: 3}
--- !u!1 &872839026
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 872839027}
  - component: {fileID: 872839028}
  - component: {fileID: 872839029}
  m_Layer: 0
  m_Name: SM_Ground_1_LOD2
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 2147483647
  m_IsActive: 1
--- !u!4 &872839027
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 872839026}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 872838983}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!23 &872839028
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 872839026}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 7df4602e1a480434eac3bc7fde90e794, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 1
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!33 &872839029
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 872839026}
  m_Mesh: {fileID: 4300004, guid: abc00000000009096612966278784113, type: 3}
--- !u!205 &872839032
LODGroup:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 872838982}
  serializedVersion: 2
  m_LocalReferencePoint: {x: -12, y: -1.000002, z: -12}
  m_Size: 34
  m_FadeMode: 0
  m_AnimateCrossFading: 0
  m_LastLODIsBillboard: 0
  m_LODs:
  - screenRelativeHeight: 0.72
    fadeTransitionWidth: 0
    renderers:
    - renderer: {fileID: 872839036}
  - screenRelativeHeight: 0.52
    fadeTransitionWidth: 0
    renderers:
    - renderer: {fileID: 872839024}
  - screenRelativeHeight: 0.01
    fadeTransitionWidth: 0
    renderers:
    - renderer: {fileID: 872839028}
    - renderer: {fileID: 0}
  m_Enabled: 1
--- !u!64 &872839033
MeshCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 872838982}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 5
  m_Convex: 0
  m_CookingOptions: 30
  m_Mesh: {fileID: 4300006, guid: abc00000000009096612966278784113, type: 3}
--- !u!1 &872839034
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 872839035}
  - component: {fileID: 872839036}
  - component: {fileID: 872839037}
  m_Layer: 0
  m_Name: SM_Ground_1_LOD0
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 2147483647
  m_IsActive: 1
--- !u!4 &872839035
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 872839034}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 872838983}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!23 &872839036
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 872839034}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 7df4602e1a480434eac3bc7fde90e794, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 1
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!33 &872839037
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 872839034}
  m_Mesh: {fileID: 4300000, guid: abc00000000009096612966278784113, type: 3}
--- !u!1 &872839038
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 872839039}
  - component: {fileID: 872839024}
  - component: {fileID: 872839025}
  m_Layer: 0
  m_Name: SM_Ground_1_LOD1
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 2147483647
  m_IsActive: 1
--- !u!4 &872839039
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 872839038}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 872838983}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &872894154
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 872894155}
  - component: {fileID: 872894158}
  - component: {fileID: 872894157}
  - component: {fileID: 872894156}
  m_Layer: 0
  m_Name: Cube
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &872894155
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 872894154}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 6, y: 0.5109999, z: 3.1140137}
  m_LocalScale: {x: 7.7096415, y: 0.001, z: 0.29843688}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 2073337801}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!65 &872894156
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 872894154}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 3
  m_Size: {x: 1, y: 1, z: 1}
  m_Center: {x: 0, y: 0, z: 0}
--- !u!23 &872894157
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 872894154}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: c9ef99fcd36d4d34da42acc2c4a49379, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!33 &872894158
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 872894154}
  m_Mesh: {fileID: 10202, guid: 0000000000000000e000000000000000, type: 0}
--- !u!1001 &885994458
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    serializedVersion: 3
    m_TransformParent: {fileID: 1133823111}
    m_Modifications:
    - target: {fileID: 1031059145, guid: abc00000000009736037106712408070, type: 3}
      propertyPath: m_Name
      value: SM_Door_004_84
      objectReference: {fileID: 0}
    - target: {fileID: 1031059146, guid: abc00000000009736037106712408070, type: 3}
      propertyPath: m_LocalScale.x
      value: 1.6075
      objectReference: {fileID: 0}
    - target: {fileID: 1031059146, guid: abc00000000009736037106712408070, type: 3}
      propertyPath: m_LocalScale.y
      value: 2.300338
      objectReference: {fileID: 0}
    - target: {fileID: 1031059146, guid: abc00000000009736037106712408070, type: 3}
      propertyPath: m_LocalPosition.x
      value: 7.9817505
      objectReference: {fileID: 0}
    - target: {fileID: 1031059146, guid: abc00000000009736037106712408070, type: 3}
      propertyPath: m_LocalPosition.y
      value: 0.010013342
      objectReference: {fileID: 0}
    - target: {fileID: 1031059146, guid: abc00000000009736037106712408070, type: 3}
      propertyPath: m_LocalPosition.z
      value: 8.190979
      objectReference: {fileID: 0}
    - target: {fileID: 1031059146, guid: abc00000000009736037106712408070, type: 3}
      propertyPath: m_LocalRotation.w
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 1031059146, guid: abc00000000009736037106712408070, type: 3}
      propertyPath: m_LocalRotation.x
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 1031059146, guid: abc00000000009736037106712408070, type: 3}
      propertyPath: m_LocalRotation.y
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 1031059146, guid: abc00000000009736037106712408070, type: 3}
      propertyPath: m_LocalRotation.z
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 1031059146, guid: abc00000000009736037106712408070, type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1031059146, guid: abc00000000009736037106712408070, type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1031059146, guid: abc00000000009736037106712408070, type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0
      objectReference: {fileID: 0}
    m_RemovedComponents: []
    m_RemovedGameObjects: []
    m_AddedGameObjects: []
    m_AddedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: abc00000000009736037106712408070, type: 3}
--- !u!4 &885994459 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: 1031059146, guid: abc00000000009736037106712408070, type: 3}
  m_PrefabInstance: {fileID: 885994458}
  m_PrefabAsset: {fileID: 0}
--- !u!1 &896623774
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 896623775}
  m_Layer: 0
  m_Name: HelicopterSittingPlayerPrefab_1
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &896623775
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 896623774}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: 0.7071067, z: 0.7071068, w: 0}
  m_LocalPosition: {x: -0.0071255504, y: -0.013262936, z: -0.01522}
  m_LocalScale: {x: 0.010000002, y: 0.0100000035, z: 0.010000002}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 542679764}
  m_Father: {fileID: 1238138302}
  m_LocalEulerAnglesHint: {x: 0, y: 180, z: 0}
--- !u!1 &902296189
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 902296190}
  m_Layer: 5
  m_Name: Fill Area
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &902296190
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 902296189}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1165948200}
  m_Father: {fileID: 2124822543}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0.25}
  m_AnchorMax: {x: 1, y: 0.75}
  m_AnchoredPosition: {x: -4.9999847, y: 0}
  m_SizeDelta: {x: -20, y: 0}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!1 &910232115
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 910232116}
  m_Layer: 5
  m_Name: MasterVolumeSetting
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &910232116
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 910232115}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: -0.000011819171}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1428216459}
  m_Father: {fileID: 1341548309}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 0, y: 0}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 0, y: 80.666664}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!134 &941483613
PhysicsMaterial:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: 
  serializedVersion: 2
  m_DynamicFriction: 0.6
  m_StaticFriction: 0.6
  m_Bounciness: 0
  m_FrictionCombine: 0
  m_BounceCombine: 0
--- !u!1 &949861109
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 949861110}
  - component: {fileID: 949861112}
  - component: {fileID: 949861111}
  m_Layer: 5
  m_Name: MasterVolumeSettingLabel
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &949861110
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 949861109}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0.0000939624}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1428216459}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 0, y: 0}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 288.36, y: 50}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &949861111
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 949861109}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f4688fdb7df04437aeb418b961361dc5, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_text: Master Volume
  m_isRightToLeft: 0
  m_fontAsset: {fileID: 11400000, guid: 8f586378b4e144a9851e7b34d9b748ee, type: 2}
  m_sharedMaterial: {fileID: 2180264, guid: 8f586378b4e144a9851e7b34d9b748ee, type: 2}
  m_fontSharedMaterials: []
  m_fontMaterial: {fileID: 0}
  m_fontMaterials: []
  m_fontColor32:
    serializedVersion: 2
    rgba: 4294967295
  m_fontColor: {r: 1, g: 1, b: 1, a: 1}
  m_enableVertexGradient: 0
  m_colorMode: 3
  m_fontColorGradient:
    topLeft: {r: 1, g: 1, b: 1, a: 1}
    topRight: {r: 1, g: 1, b: 1, a: 1}
    bottomLeft: {r: 1, g: 1, b: 1, a: 1}
    bottomRight: {r: 1, g: 1, b: 1, a: 1}
  m_fontColorGradientPreset: {fileID: 0}
  m_spriteAsset: {fileID: 0}
  m_tintAllSprites: 0
  m_StyleSheet: {fileID: 0}
  m_TextStyleHashCode: -1183493901
  m_overrideHtmlColors: 0
  m_faceColor:
    serializedVersion: 2
    rgba: 4294967295
  m_fontSize: 33.6
  m_fontSizeBase: 33.6
  m_fontWeight: 400
  m_enableAutoSizing: 0
  m_fontSizeMin: 18
  m_fontSizeMax: 72
  m_fontStyle: 0
  m_HorizontalAlignment: 2
  m_VerticalAlignment: 512
  m_textAlignment: 65535
  m_characterSpacing: 0
  m_wordSpacing: 0
  m_lineSpacing: 0
  m_lineSpacingMax: 0
  m_paragraphSpacing: 0
  m_charWidthMaxAdj: 0
  m_TextWrappingMode: 0
  m_wordWrappingRatios: 0.4
  m_overflowMode: 0
  m_linkedTextComponent: {fileID: 0}
  parentLinkedComponent: {fileID: 0}
  m_enableKerning: 0
  m_ActiveFontFeatures: 6e72656b
  m_enableExtraPadding: 0
  checkPaddingRequired: 0
  m_isRichText: 1
  m_EmojiFallbackSupport: 1
  m_parseCtrlCharacters: 1
  m_isOrthographic: 1
  m_isCullingEnabled: 0
  m_horizontalMapping: 0
  m_verticalMapping: 0
  m_uvLineOffset: 0
  m_geometrySortingOrder: 0
  m_IsTextObjectScaleStatic: 0
  m_VertexBufferAutoSizeReduction: 0
  m_useMaxVisibleDescender: 1
  m_pageToDisplay: 1
  m_margin: {x: 0, y: 0, z: 0, w: 0}
  m_isUsingLegacyAnimationComponent: 0
  m_isVolumetricText: 0
  m_hasFontAssetChanged: 0
  m_baseMaterial: {fileID: 0}
  m_maskOffset: {x: 0, y: 0, z: 0, w: 0}
--- !u!222 &949861112
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 949861109}
  m_CullTransparentMesh: 1
--- !u!1 &954295165
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 954295166}
  - component: {fileID: 954295167}
  m_Layer: 5
  m_Name: AmbienceVolumeSettingSlider
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &954295166
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 954295165}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0.0001495125}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 2147134075}
  - {fileID: 1703431731}
  - {fileID: 1066343027}
  - {fileID: 109180144}
  - {fileID: 1564596058}
  m_Father: {fileID: 1275455219}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 0, y: 0}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 232.7, y: 20}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &954295167
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 954295165}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 67db9e8f0e2ae9c40bc1e2b64352a6b4, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Navigation:
    m_Mode: 3
    m_WrapAround: 0
    m_SelectOnUp: {fileID: 0}
    m_SelectOnDown: {fileID: 0}
    m_SelectOnLeft: {fileID: 0}
    m_SelectOnRight: {fileID: 0}
  m_Transition: 1
  m_Colors:
    m_NormalColor: {r: 1, g: 1, b: 1, a: 1}
    m_HighlightedColor: {r: 0.9607843, g: 0.9607843, b: 0.9607843, a: 1}
    m_PressedColor: {r: 0.78431374, g: 0.78431374, b: 0.78431374, a: 1}
    m_SelectedColor: {r: 0.9607843, g: 0.9607843, b: 0.9607843, a: 1}
    m_DisabledColor: {r: 0.78431374, g: 0.78431374, b: 0.78431374, a: 0.5019608}
    m_ColorMultiplier: 1
    m_FadeDuration: 0.1
  m_SpriteState:
    m_HighlightedSprite: {fileID: 0}
    m_PressedSprite: {fileID: 0}
    m_SelectedSprite: {fileID: 0}
    m_DisabledSprite: {fileID: 0}
  m_AnimationTriggers:
    m_NormalTrigger: Normal
    m_HighlightedTrigger: Highlighted
    m_PressedTrigger: Pressed
    m_SelectedTrigger: Selected
    m_DisabledTrigger: Disabled
  m_Interactable: 1
  m_TargetGraphic: {fileID: 1152112747}
  m_FillRect: {fileID: 1822947782}
  m_HandleRect: {fileID: 1152112746}
  m_Direction: 0
  m_MinValue: 0
  m_MaxValue: 1
  m_WholeNumbers: 0
  m_Value: 0
  m_OnValueChanged:
    m_PersistentCalls:
      m_Calls: []
--- !u!1 &967290190
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 967290192}
  - component: {fileID: 967290191}
  - component: {fileID: 967290193}
  m_Layer: 0
  m_Name: ForestIntroHelicopter
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!114 &967290191
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 967290190}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 535d857985dc2cf47af70e514580801f, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  helicopterModel: {fileID: 967290190}
  waypoints:
  - {fileID: 982418701}
  - {fileID: 369533476}
  - {fileID: 331546771}
  - {fileID: 1371030940}
  moveSpeed: 15
  rotationSpeed: 2
  waitTimeAtWaypoint: 1
  landingSpeedFactor: 0.1
  specificLandingTime: 3
--- !u!4 &967290192
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 967290190}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: -0.55602676, z: 0, w: 0.8311644}
  m_LocalPosition: {x: 903.5697, y: 54.2, z: 468.58392}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 543450653026896001}
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!82 &967290193
AudioSource:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 967290190}
  m_Enabled: 1
  serializedVersion: 4
  OutputAudioMixerGroup: {fileID: 0}
  m_audioClip: {fileID: 0}
  m_Resource: {fileID: 0}
  m_PlayOnAwake: 1
  m_Volume: 1
  m_Pitch: 1
  Loop: 0
  Mute: 0
  Spatialize: 0
  SpatializePostEffects: 0
  Priority: 128
  DopplerLevel: 1
  MinDistance: 1
  MaxDistance: 500
  Pan2D: 0
  rolloffMode: 0
  BypassEffects: 0
  BypassListenerEffects: 0
  BypassReverbZones: 0
  rolloffCustomCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 0
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  panLevelCustomCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  spreadCustomCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  reverbZoneMixCustomCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
--- !u!1001 &981846355
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    serializedVersion: 3
    m_TransformParent: {fileID: 1133823111}
    m_Modifications:
    - target: {fileID: 1031081388, guid: abc00000000016754684817399611798, type: 3}
      propertyPath: m_Name
      value: SM_Field_Radio_61
      objectReference: {fileID: 0}
    - target: {fileID: 1031081389, guid: abc00000000016754684817399611798, type: 3}
      propertyPath: m_LocalScale.x
      value: 2
      objectReference: {fileID: 0}
    - target: {fileID: 1031081389, guid: abc00000000016754684817399611798, type: 3}
      propertyPath: m_LocalScale.y
      value: 2
      objectReference: {fileID: 0}
    - target: {fileID: 1031081389, guid: abc00000000016754684817399611798, type: 3}
      propertyPath: m_LocalScale.z
      value: 2
      objectReference: {fileID: 0}
    - target: {fileID: 1031081389, guid: abc00000000016754684817399611798, type: 3}
      propertyPath: m_LocalPosition.x
      value: 2.9100342
      objectReference: {fileID: 0}
    - target: {fileID: 1031081389, guid: abc00000000016754684817399611798, type: 3}
      propertyPath: m_LocalPosition.y
      value: 2.0060134
      objectReference: {fileID: 0}
    - target: {fileID: 1031081389, guid: abc00000000016754684817399611798, type: 3}
      propertyPath: m_LocalPosition.z
      value: -1.0370178
      objectReference: {fileID: 0}
    - target: {fileID: 1031081389, guid: abc00000000016754684817399611798, type: 3}
      propertyPath: m_LocalRotation.w
      value: 0.9824956
      objectReference: {fileID: 0}
    - target: {fileID: 1031081389, guid: abc00000000016754684817399611798, type: 3}
      propertyPath: m_LocalRotation.x
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 1031081389, guid: abc00000000016754684817399611798, type: 3}
      propertyPath: m_LocalRotation.y
      value: 0.18628561
      objectReference: {fileID: 0}
    - target: {fileID: 1031081389, guid: abc00000000016754684817399611798, type: 3}
      propertyPath: m_LocalRotation.z
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 1031081389, guid: abc00000000016754684817399611798, type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1031081389, guid: abc00000000016754684817399611798, type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 21.472
      objectReference: {fileID: 0}
    - target: {fileID: 1031081389, guid: abc00000000016754684817399611798, type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1031081389, guid: abc00000000016754684817399611798, type: 3}
      propertyPath: m_ConstrainProportionsScale
      value: 1
      objectReference: {fileID: 0}
    m_RemovedComponents: []
    m_RemovedGameObjects: []
    m_AddedGameObjects: []
    m_AddedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: abc00000000016754684817399611798, type: 3}
--- !u!4 &981846356 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: 1031081389, guid: abc00000000016754684817399611798, type: 3}
  m_PrefabInstance: {fileID: 981846355}
  m_PrefabAsset: {fileID: 0}
--- !u!1 &982418700
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 982418701}
  m_Layer: 0
  m_Name: HelicopterWaypoint_0
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &982418701
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 982418700}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 452.7, y: 47.084, z: 98.552704}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1812692992}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1001 &1014937991
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    serializedVersion: 3
    m_TransformParent: {fileID: 1133823111}
    m_Modifications:
    - target: {fileID: 1031089409, guid: abc00000000009525387784473402460, type: 3}
      propertyPath: m_Name
      value: SM_Security_Booth2_13
      objectReference: {fileID: 0}
    - target: {fileID: 1031089410, guid: abc00000000009525387784473402460, type: 3}
      propertyPath: m_LocalScale.x
      value: 2.3
      objectReference: {fileID: 0}
    - target: {fileID: 1031089410, guid: abc00000000009525387784473402460, type: 3}
      propertyPath: m_LocalScale.y
      value: 2.3
      objectReference: {fileID: 0}
    - target: {fileID: 1031089410, guid: abc00000000009525387784473402460, type: 3}
      propertyPath: m_LocalScale.z
      value: 2.3
      objectReference: {fileID: 0}
    - target: {fileID: 1031089410, guid: abc00000000009525387784473402460, type: 3}
      propertyPath: m_LocalPosition.x
      value: 4.01001
      objectReference: {fileID: 0}
    - target: {fileID: 1031089410, guid: abc00000000009525387784473402460, type: 3}
      propertyPath: m_LocalPosition.y
      value: 0.000024795532
      objectReference: {fileID: 0}
    - target: {fileID: 1031089410, guid: abc00000000009525387784473402460, type: 3}
      propertyPath: m_LocalPosition.z
      value: 3.2139893
      objectReference: {fileID: 0}
    - target: {fileID: 1031089410, guid: abc00000000009525387784473402460, type: 3}
      propertyPath: m_LocalRotation.w
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 1031089410, guid: abc00000000009525387784473402460, type: 3}
      propertyPath: m_LocalRotation.x
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 1031089410, guid: abc00000000009525387784473402460, type: 3}
      propertyPath: m_LocalRotation.y
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 1031089410, guid: abc00000000009525387784473402460, type: 3}
      propertyPath: m_LocalRotation.z
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 1031089410, guid: abc00000000009525387784473402460, type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1031089410, guid: abc00000000009525387784473402460, type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1031089410, guid: abc00000000009525387784473402460, type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1031089410, guid: abc00000000009525387784473402460, type: 3}
      propertyPath: m_ConstrainProportionsScale
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 1031089412, guid: abc00000000009525387784473402460, type: 3}
      propertyPath: m_Size.z
      value: 5.174552
      objectReference: {fileID: 0}
    - target: {fileID: 1031089412, guid: abc00000000009525387784473402460, type: 3}
      propertyPath: m_Material
      value: 
      objectReference: {fileID: 1407554859}
    - target: {fileID: 1031089413, guid: abc00000000009525387784473402460, type: 3}
      propertyPath: m_Material
      value: 
      objectReference: {fileID: 1892681617}
    - target: {fileID: 1031089414, guid: abc00000000009525387784473402460, type: 3}
      propertyPath: m_Material
      value: 
      objectReference: {fileID: 1435913709}
    - target: {fileID: 1031089415, guid: abc00000000009525387784473402460, type: 3}
      propertyPath: m_Material
      value: 
      objectReference: {fileID: 1733125366}
    - target: {fileID: 1031089416, guid: abc00000000009525387784473402460, type: 3}
      propertyPath: m_Material
      value: 
      objectReference: {fileID: 499636778}
    - target: {fileID: 1031089417, guid: abc00000000009525387784473402460, type: 3}
      propertyPath: m_Size.z
      value: 4.498335
      objectReference: {fileID: 0}
    - target: {fileID: 1031089417, guid: abc00000000009525387784473402460, type: 3}
      propertyPath: m_Material
      value: 
      objectReference: {fileID: 1195133933}
    m_RemovedComponents: []
    m_RemovedGameObjects: []
    m_AddedGameObjects: []
    m_AddedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: abc00000000009525387784473402460, type: 3}
--- !u!4 &1014937992 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: 1031089410, guid: abc00000000009525387784473402460, type: 3}
  m_PrefabInstance: {fileID: 1014937991}
  m_PrefabAsset: {fileID: 0}
--- !u!1001 &1037443436
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    serializedVersion: 3
    m_TransformParent: {fileID: 1675261855}
    m_Modifications:
    - target: {fileID: 1030973110, guid: abc00000000001588025897481649393, type: 3}
      propertyPath: m_Name
      value: SM_Sign_3_SpeedLimit
      objectReference: {fileID: 0}
    - target: {fileID: 1030973111, guid: abc00000000001588025897481649393, type: 3}
      propertyPath: m_LocalScale.x
      value: 0.10000001
      objectReference: {fileID: 0}
    - target: {fileID: 1030973111, guid: abc00000000001588025897481649393, type: 3}
      propertyPath: m_LocalScale.y
      value: 0.1
      objectReference: {fileID: 0}
    - target: {fileID: 1030973111, guid: abc00000000001588025897481649393, type: 3}
      propertyPath: m_LocalScale.z
      value: 0.10000001
      objectReference: {fileID: 0}
    - target: {fileID: 1030973111, guid: abc00000000001588025897481649393, type: 3}
      propertyPath: m_LocalPosition.x
      value: 8.179496
      objectReference: {fileID: 0}
    - target: {fileID: 1030973111, guid: abc00000000001588025897481649393, type: 3}
      propertyPath: m_LocalPosition.y
      value: -0.0009331703
      objectReference: {fileID: 0}
    - target: {fileID: 1030973111, guid: abc00000000001588025897481649393, type: 3}
      propertyPath: m_LocalPosition.z
      value: -7.1730957
      objectReference: {fileID: 0}
    - target: {fileID: 1030973111, guid: abc00000000001588025897481649393, type: 3}
      propertyPath: m_LocalRotation.w
      value: 0.005856173
      objectReference: {fileID: 0}
    - target: {fileID: 1030973111, guid: abc00000000001588025897481649393, type: 3}
      propertyPath: m_LocalRotation.x
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 1030973111, guid: abc00000000001588025897481649393, type: 3}
      propertyPath: m_LocalRotation.y
      value: 0.9999829
      objectReference: {fileID: 0}
    - target: {fileID: 1030973111, guid: abc00000000001588025897481649393, type: 3}
      propertyPath: m_LocalRotation.z
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 1030973111, guid: abc00000000001588025897481649393, type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1030973111, guid: abc00000000001588025897481649393, type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 179.329
      objectReference: {fileID: 0}
    - target: {fileID: 1030973111, guid: abc00000000001588025897481649393, type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0
      objectReference: {fileID: 0}
    m_RemovedComponents: []
    m_RemovedGameObjects: []
    m_AddedGameObjects: []
    m_AddedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: abc00000000001588025897481649393, type: 3}
--- !u!4 &1037443437 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: 1030973111, guid: abc00000000001588025897481649393, type: 3}
  m_PrefabInstance: {fileID: 1037443436}
  m_PrefabAsset: {fileID: 0}
--- !u!1 &1066343026
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1066343027}
  m_Layer: 5
  m_Name: Handle Slide Area
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &1066343027
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1066343026}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1152112746}
  m_Father: {fileID: 954295166}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 1, y: 1}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: -20, y: 0}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!1001 &1073820111
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    serializedVersion: 3
    m_TransformParent: {fileID: 1769772629}
    m_Modifications:
    - target: {fileID: 1031104978, guid: abc00000000006676651557972390903, type: 3}
      propertyPath: m_Name
      value: SM_Keyboard_01a_106
      objectReference: {fileID: 0}
    - target: {fileID: 1031104979, guid: abc00000000006676651557972390903, type: 3}
      propertyPath: m_LocalScale.x
      value: 2.3
      objectReference: {fileID: 0}
    - target: {fileID: 1031104979, guid: abc00000000006676651557972390903, type: 3}
      propertyPath: m_LocalScale.y
      value: 2.3
      objectReference: {fileID: 0}
    - target: {fileID: 1031104979, guid: abc00000000006676651557972390903, type: 3}
      propertyPath: m_LocalScale.z
      value: 2.3
      objectReference: {fileID: 0}
    - target: {fileID: 1031104979, guid: abc00000000006676651557972390903, type: 3}
      propertyPath: m_LocalPosition.x
      value: -0.80718994
      objectReference: {fileID: 0}
    - target: {fileID: 1031104979, guid: abc00000000006676651557972390903, type: 3}
      propertyPath: m_LocalPosition.y
      value: -0.04700017
      objectReference: {fileID: 0}
    - target: {fileID: 1031104979, guid: abc00000000006676651557972390903, type: 3}
      propertyPath: m_LocalPosition.z
      value: 0.033996582
      objectReference: {fileID: 0}
    - target: {fileID: 1031104979, guid: abc00000000006676651557972390903, type: 3}
      propertyPath: m_LocalRotation.w
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 1031104979, guid: abc00000000006676651557972390903, type: 3}
      propertyPath: m_LocalRotation.x
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 1031104979, guid: abc00000000006676651557972390903, type: 3}
      propertyPath: m_LocalRotation.y
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 1031104979, guid: abc00000000006676651557972390903, type: 3}
      propertyPath: m_LocalRotation.z
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 1031104979, guid: abc00000000006676651557972390903, type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1031104979, guid: abc00000000006676651557972390903, type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1031104979, guid: abc00000000006676651557972390903, type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1031104979, guid: abc00000000006676651557972390903, type: 3}
      propertyPath: m_ConstrainProportionsScale
      value: 1
      objectReference: {fileID: 0}
    m_RemovedComponents: []
    m_RemovedGameObjects: []
    m_AddedGameObjects: []
    m_AddedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: abc00000000006676651557972390903, type: 3}
--- !u!4 &1073820112 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: 1031104979, guid: abc00000000006676651557972390903, type: 3}
  m_PrefabInstance: {fileID: 1073820111}
  m_PrefabAsset: {fileID: 0}
--- !u!1 &1076447431
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1076447432}
  m_Layer: 0
  m_Name: HelicopterSittingPlayerPrefab_0
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1076447432
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1076447431}
  serializedVersion: 2
  m_LocalRotation: {x: 0.7071068, y: -0, z: -0, w: 0.7071067}
  m_LocalPosition: {x: 0.022427067, y: 0.008527224, z: -0.015219999}
  m_LocalScale: {x: 0.010000002, y: 0.0100000035, z: 0.010000002}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1513151534}
  m_Father: {fileID: 1238138302}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1076455029
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1076455030}
  - component: {fileID: 1076455033}
  - component: {fileID: 1076455032}
  - component: {fileID: 1076455031}
  m_Layer: 0
  m_Name: Cube_1
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1076455030
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1076455029}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 6, y: 0.5109999, z: 7.6789856}
  m_LocalScale: {x: 7.7096415, y: 0.001, z: 0.29843688}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 216111616}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!65 &1076455031
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1076455029}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 3
  m_Size: {x: 1, y: 1, z: 1}
  m_Center: {x: 0, y: 0, z: 0}
--- !u!23 &1076455032
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1076455029}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: c9ef99fcd36d4d34da42acc2c4a49379, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!33 &1076455033
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1076455029}
  m_Mesh: {fileID: 10202, guid: 0000000000000000e000000000000000, type: 0}
--- !u!1001 &1086602228
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    serializedVersion: 3
    m_TransformParent: {fileID: 519976997}
    m_Modifications:
    - target: {fileID: 1031130836, guid: 23f3af5f0be46494c88cb3893d45084f, type: 3}
      propertyPath: m_Name
      value: SM_Monkey_Bars
      objectReference: {fileID: 0}
    - target: {fileID: 1031130837, guid: 23f3af5f0be46494c88cb3893d45084f, type: 3}
      propertyPath: m_LocalScale.x
      value: 1.6000001
      objectReference: {fileID: 0}
    - target: {fileID: 1031130837, guid: 23f3af5f0be46494c88cb3893d45084f, type: 3}
      propertyPath: m_LocalScale.y
      value: 1.6
      objectReference: {fileID: 0}
    - target: {fileID: 1031130837, guid: 23f3af5f0be46494c88cb3893d45084f, type: 3}
      propertyPath: m_LocalScale.z
      value: 1.6000001
      objectReference: {fileID: 0}
    - target: {fileID: 1031130837, guid: 23f3af5f0be46494c88cb3893d45084f, type: 3}
      propertyPath: m_LocalPosition.x
      value: 6.23999
      objectReference: {fileID: 0}
    - target: {fileID: 1031130837, guid: 23f3af5f0be46494c88cb3893d45084f, type: 3}
      propertyPath: m_LocalPosition.y
      value: -4.20998
      objectReference: {fileID: 0}
    - target: {fileID: 1031130837, guid: 23f3af5f0be46494c88cb3893d45084f, type: 3}
      propertyPath: m_LocalPosition.z
      value: -23.220001
      objectReference: {fileID: 0}
    - target: {fileID: 1031130837, guid: 23f3af5f0be46494c88cb3893d45084f, type: 3}
      propertyPath: m_LocalRotation.w
      value: 0.7071068
      objectReference: {fileID: 0}
    - target: {fileID: 1031130837, guid: 23f3af5f0be46494c88cb3893d45084f, type: 3}
      propertyPath: m_LocalRotation.x
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 1031130837, guid: 23f3af5f0be46494c88cb3893d45084f, type: 3}
      propertyPath: m_LocalRotation.y
      value: 0.7071068
      objectReference: {fileID: 0}
    - target: {fileID: 1031130837, guid: 23f3af5f0be46494c88cb3893d45084f, type: 3}
      propertyPath: m_LocalRotation.z
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 1031130837, guid: 23f3af5f0be46494c88cb3893d45084f, type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1031130837, guid: 23f3af5f0be46494c88cb3893d45084f, type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 90
      objectReference: {fileID: 0}
    - target: {fileID: 1031130837, guid: 23f3af5f0be46494c88cb3893d45084f, type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1031130837, guid: 23f3af5f0be46494c88cb3893d45084f, type: 3}
      propertyPath: m_ConstrainProportionsScale
      value: 1
      objectReference: {fileID: 0}
    m_RemovedComponents: []
    m_RemovedGameObjects: []
    m_AddedGameObjects: []
    m_AddedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: 23f3af5f0be46494c88cb3893d45084f, type: 3}
--- !u!4 &1086602229 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: 1031130837, guid: 23f3af5f0be46494c88cb3893d45084f, type: 3}
  m_PrefabInstance: {fileID: 1086602228}
  m_PrefabAsset: {fileID: 0}
--- !u!1 &1088778215
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1088778216}
  - component: {fileID: 1088778218}
  - component: {fileID: 1088778217}
  m_Layer: 5
  m_Name: SoundFXVolumeSettingLabel
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &1088778216
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1088778215}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0.0000939624}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 214815528}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 0, y: 0}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 288.36, y: 50}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &1088778217
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1088778215}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f4688fdb7df04437aeb418b961361dc5, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_text: SoundFX Volume
  m_isRightToLeft: 0
  m_fontAsset: {fileID: 11400000, guid: 8f586378b4e144a9851e7b34d9b748ee, type: 2}
  m_sharedMaterial: {fileID: 2180264, guid: 8f586378b4e144a9851e7b34d9b748ee, type: 2}
  m_fontSharedMaterials: []
  m_fontMaterial: {fileID: 0}
  m_fontMaterials: []
  m_fontColor32:
    serializedVersion: 2
    rgba: 4294967295
  m_fontColor: {r: 1, g: 1, b: 1, a: 1}
  m_enableVertexGradient: 0
  m_colorMode: 3
  m_fontColorGradient:
    topLeft: {r: 1, g: 1, b: 1, a: 1}
    topRight: {r: 1, g: 1, b: 1, a: 1}
    bottomLeft: {r: 1, g: 1, b: 1, a: 1}
    bottomRight: {r: 1, g: 1, b: 1, a: 1}
  m_fontColorGradientPreset: {fileID: 0}
  m_spriteAsset: {fileID: 0}
  m_tintAllSprites: 0
  m_StyleSheet: {fileID: 0}
  m_TextStyleHashCode: -1183493901
  m_overrideHtmlColors: 0
  m_faceColor:
    serializedVersion: 2
    rgba: 4294967295
  m_fontSize: 33.6
  m_fontSizeBase: 33.6
  m_fontWeight: 400
  m_enableAutoSizing: 0
  m_fontSizeMin: 18
  m_fontSizeMax: 72
  m_fontStyle: 0
  m_HorizontalAlignment: 2
  m_VerticalAlignment: 512
  m_textAlignment: 65535
  m_characterSpacing: 0
  m_wordSpacing: 0
  m_lineSpacing: 0
  m_lineSpacingMax: 0
  m_paragraphSpacing: 0
  m_charWidthMaxAdj: 0
  m_TextWrappingMode: 0
  m_wordWrappingRatios: 0.4
  m_overflowMode: 0
  m_linkedTextComponent: {fileID: 0}
  parentLinkedComponent: {fileID: 0}
  m_enableKerning: 0
  m_ActiveFontFeatures: 6e72656b
  m_enableExtraPadding: 0
  checkPaddingRequired: 0
  m_isRichText: 1
  m_EmojiFallbackSupport: 1
  m_parseCtrlCharacters: 1
  m_isOrthographic: 1
  m_isCullingEnabled: 0
  m_horizontalMapping: 0
  m_verticalMapping: 0
  m_uvLineOffset: 0
  m_geometrySortingOrder: 0
  m_IsTextObjectScaleStatic: 0
  m_VertexBufferAutoSizeReduction: 0
  m_useMaxVisibleDescender: 1
  m_pageToDisplay: 1
  m_margin: {x: 0, y: 0, z: 0, w: 0}
  m_isUsingLegacyAnimationComponent: 0
  m_isVolumetricText: 0
  m_hasFontAssetChanged: 0
  m_baseMaterial: {fileID: 0}
  m_maskOffset: {x: 0, y: 0, z: 0, w: 0}
--- !u!222 &1088778218
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1088778215}
  m_CullTransparentMesh: 1
--- !u!1 &1133823110
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1133823111}
  m_Layer: 0
  m_Name: SatelliteBuilding
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1133823111
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1133823110}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: -23.25, y: -4.2100134, z: -17.970001}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1527419935}
  - {fileID: 1014937992}
  - {fileID: 1878862096}
  - {fileID: 885994459}
  - {fileID: 1928151667}
  - {fileID: 281576277}
  - {fileID: 981846356}
  - {fileID: 1769772629}
  m_Father: {fileID: 519976997}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1147994953
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1147994954}
  m_Layer: 5
  m_Name: Handle Slide Area
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &1147994954
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1147994953}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 555802034}
  m_Father: {fileID: 1658130456}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 1, y: 1}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: -20, y: 0}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!1 &1152112745
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1152112746}
  - component: {fileID: 1152112748}
  - component: {fileID: 1152112747}
  m_Layer: 5
  m_Name: Handle
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &1152112746
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1152112745}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1066343027}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 0, y: 0}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 20, y: 0}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &1152112747
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1152112745}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fe87c0e1cc204ed48ad3b37840f39efc, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_Sprite: {fileID: 10913, guid: 0000000000000000f000000000000000, type: 0}
  m_Type: 0
  m_PreserveAspect: 0
  m_FillCenter: 1
  m_FillMethod: 4
  m_FillAmount: 1
  m_FillClockwise: 1
  m_FillOrigin: 0
  m_UseSpriteMesh: 0
  m_PixelsPerUnitMultiplier: 1
--- !u!222 &1152112748
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1152112745}
  m_CullTransparentMesh: 1
--- !u!114 &1153949932
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: b1d046e3917e4d8580903c69758b6558, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  enabled: 1
  label: Height
  blendMode: 0
  opacity: 20
  passIndex: 0
  min: 0
  minFalloff: 1
  max: 1
  maxFalloff: 1
--- !u!1 &1156203011
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1156203013}
  - component: {fileID: 1156203012}
  m_Layer: 0
  m_Name: ForestPlayerManager
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!114 &1156203012
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1156203011}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 5471ae9acb13db04293e33ff760900ce, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  clientPlayerPrefab: {fileID: 7159553026424513117, guid: bdf1b37a360b55640a0e9874d05d5711, type: 3}
  replicatedPlayerPrefabOne: {fileID: 1902618288162282744, guid: dce085bae5db6154db7424777e64ca25, type: 3}
  replicatedPlayerPrefabTwo: {fileID: 5367346475881215968, guid: d6f84caf2551d9446b716e475a76245f, type: 3}
  replicatedPlayerPrefabThree: {fileID: 5456586332487622522, guid: 14a7a6fe93ab1e24c90961a8c6d48e2d, type: 3}
  localPlayerSpawnPoint: {fileID: 641934374}
  replicatedPlayerSpawnPoints:
  - {fileID: 1513151534}
  - {fileID: 542679764}
  - {fileID: 141025193}
  helicopterTransform: {fileID: 967290192}
--- !u!4 &1156203013
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1156203011}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 895.6544, y: 57.309254, z: 469.6816}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1165948199
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1165948200}
  - component: {fileID: 1165948202}
  - component: {fileID: 1165948201}
  m_Layer: 5
  m_Name: Fill
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &1165948200
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1165948199}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 902296190}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 0, y: 0}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 10, y: 0}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &1165948201
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1165948199}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fe87c0e1cc204ed48ad3b37840f39efc, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_Sprite: {fileID: 10905, guid: 0000000000000000f000000000000000, type: 0}
  m_Type: 1
  m_PreserveAspect: 0
  m_FillCenter: 1
  m_FillMethod: 4
  m_FillAmount: 1
  m_FillClockwise: 1
  m_FillOrigin: 0
  m_UseSpriteMesh: 0
  m_PixelsPerUnitMultiplier: 1
--- !u!222 &1165948202
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1165948199}
  m_CullTransparentMesh: 1
--- !u!1001 &1166037171
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    serializedVersion: 3
    m_TransformParent: {fileID: 708659531}
    m_Modifications:
    - target: {fileID: 1031073326, guid: abc00000000012523168504177100815, type: 3}
      propertyPath: m_Name
      value: SM_Storage_RackModule4_96
      objectReference: {fileID: 0}
    - target: {fileID: 1031073327, guid: abc00000000012523168504177100815, type: 3}
      propertyPath: m_LocalPosition.x
      value: -11.570007
      objectReference: {fileID: 0}
    - target: {fileID: 1031073327, guid: abc00000000012523168504177100815, type: 3}
      propertyPath: m_LocalPosition.y
      value: 0.14360404
      objectReference: {fileID: 0}
    - target: {fileID: 1031073327, guid: abc00000000012523168504177100815, type: 3}
      propertyPath: m_LocalPosition.z
      value: 11.136993
      objectReference: {fileID: 0}
    - target: {fileID: 1031073327, guid: abc00000000012523168504177100815, type: 3}
      propertyPath: m_LocalRotation.w
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 1031073327, guid: abc00000000012523168504177100815, type: 3}
      propertyPath: m_LocalRotation.x
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 1031073327, guid: abc00000000012523168504177100815, type: 3}
      propertyPath: m_LocalRotation.y
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 1031073327, guid: abc00000000012523168504177100815, type: 3}
      propertyPath: m_LocalRotation.z
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 1031073327, guid: abc00000000012523168504177100815, type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1031073327, guid: abc00000000012523168504177100815, type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1031073327, guid: abc00000000012523168504177100815, type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0
      objectReference: {fileID: 0}
    m_RemovedComponents: []
    m_RemovedGameObjects: []
    m_AddedGameObjects: []
    m_AddedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: abc00000000012523168504177100815, type: 3}
--- !u!4 &1166037172 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: 1031073327, guid: abc00000000012523168504177100815, type: 3}
  m_PrefabInstance: {fileID: 1166037171}
  m_PrefabAsset: {fileID: 0}
--- !u!1 &1176333023
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1176333024}
  - component: {fileID: 1176333025}
  m_Layer: 5
  m_Name: MusicVolumeSettingContainer
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &1176333024
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1176333023}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0.0001495125}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1279474993}
  - {fileID: 2124822543}
  m_Father: {fileID: 1245742951}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 1}
  m_AnchorMax: {x: 0, y: 1}
  m_AnchoredPosition: {x: 200, y: -35}
  m_SizeDelta: {x: 288.36, y: 70}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &1176333025
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1176333023}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 59f8146938fff824cb5fd77236b75775, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Padding:
    m_Left: 0
    m_Right: 0
    m_Top: 0
    m_Bottom: 0
  m_ChildAlignment: 1
  m_Spacing: 0
  m_ChildForceExpandWidth: 1
  m_ChildForceExpandHeight: 1
  m_ChildControlWidth: 0
  m_ChildControlHeight: 0
  m_ChildScaleWidth: 0
  m_ChildScaleHeight: 0
  m_ReverseArrangement: 0
--- !u!1 &1176990233
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1176990234}
  - component: {fileID: 1176990236}
  - component: {fileID: 1176990235}
  m_Layer: 5
  m_Name: SettingsBackButtonText
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &1176990234
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1176990233}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 230490799}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 1, y: 1}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 0, y: 0}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &1176990235
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1176990233}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f4688fdb7df04437aeb418b961361dc5, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_text: BACK TO MAIN MENU
  m_isRightToLeft: 0
  m_fontAsset: {fileID: 11400000, guid: 8f586378b4e144a9851e7b34d9b748ee, type: 2}
  m_sharedMaterial: {fileID: 2180264, guid: 8f586378b4e144a9851e7b34d9b748ee, type: 2}
  m_fontSharedMaterials: []
  m_fontMaterial: {fileID: 0}
  m_fontMaterials: []
  m_fontColor32:
    serializedVersion: 2
    rgba: 4294967295
  m_fontColor: {r: 1, g: 1, b: 1, a: 1}
  m_enableVertexGradient: 0
  m_colorMode: 3
  m_fontColorGradient:
    topLeft: {r: 1, g: 1, b: 1, a: 1}
    topRight: {r: 1, g: 1, b: 1, a: 1}
    bottomLeft: {r: 1, g: 1, b: 1, a: 1}
    bottomRight: {r: 1, g: 1, b: 1, a: 1}
  m_fontColorGradientPreset: {fileID: 0}
  m_spriteAsset: {fileID: 0}
  m_tintAllSprites: 0
  m_StyleSheet: {fileID: 0}
  m_TextStyleHashCode: -1183493901
  m_overrideHtmlColors: 0
  m_faceColor:
    serializedVersion: 2
    rgba: 4294967295
  m_fontSize: 24
  m_fontSizeBase: 24
  m_fontWeight: 400
  m_enableAutoSizing: 0
  m_fontSizeMin: 18
  m_fontSizeMax: 72
  m_fontStyle: 0
  m_HorizontalAlignment: 2
  m_VerticalAlignment: 512
  m_textAlignment: 65535
  m_characterSpacing: 0
  m_wordSpacing: 0
  m_lineSpacing: 0
  m_lineSpacingMax: 0
  m_paragraphSpacing: 0
  m_charWidthMaxAdj: 0
  m_TextWrappingMode: 1
  m_wordWrappingRatios: 0.4
  m_overflowMode: 0
  m_linkedTextComponent: {fileID: 0}
  parentLinkedComponent: {fileID: 0}
  m_enableKerning: 0
  m_ActiveFontFeatures: 6e72656b
  m_enableExtraPadding: 0
  checkPaddingRequired: 0
  m_isRichText: 1
  m_EmojiFallbackSupport: 1
  m_parseCtrlCharacters: 1
  m_isOrthographic: 1
  m_isCullingEnabled: 0
  m_horizontalMapping: 0
  m_verticalMapping: 0
  m_uvLineOffset: 0
  m_geometrySortingOrder: 0
  m_IsTextObjectScaleStatic: 0
  m_VertexBufferAutoSizeReduction: 0
  m_useMaxVisibleDescender: 1
  m_pageToDisplay: 1
  m_margin: {x: 0, y: 0, z: 0, w: 0}
  m_isUsingLegacyAnimationComponent: 0
  m_isVolumetricText: 0
  m_hasFontAssetChanged: 0
  m_baseMaterial: {fileID: 0}
  m_maskOffset: {x: 0, y: 0, z: 0, w: 0}
--- !u!222 &1176990236
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1176990233}
  m_CullTransparentMesh: 1
--- !u!134 &1195133933
PhysicsMaterial:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: 
  serializedVersion: 2
  m_DynamicFriction: 0.6
  m_StaticFriction: 0.6
  m_Bounciness: 0
  m_FrictionCombine: 0
  m_BounceCombine: 0
--- !u!1001 &1197962279
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    serializedVersion: 3
    m_TransformParent: {fileID: 708659531}
    m_Modifications:
    - target: {fileID: 1031072879, guid: abc00000000001332876369726052611, type: 3}
      propertyPath: m_Name
      value: SM_OfficeDesk_7
      objectReference: {fileID: 0}
    - target: {fileID: 1031072880, guid: abc00000000001332876369726052611, type: 3}
      propertyPath: m_LocalScale.x
      value: 2
      objectReference: {fileID: 0}
    - target: {fileID: 1031072880, guid: abc00000000001332876369726052611, type: 3}
      propertyPath: m_LocalScale.y
      value: 2
      objectReference: {fileID: 0}
    - target: {fileID: 1031072880, guid: abc00000000001332876369726052611, type: 3}
      propertyPath: m_LocalScale.z
      value: 2
      objectReference: {fileID: 0}
    - target: {fileID: 1031072880, guid: abc00000000001332876369726052611, type: 3}
      propertyPath: m_LocalPosition.x
      value: -5.799988
      objectReference: {fileID: 0}
    - target: {fileID: 1031072880, guid: abc00000000001332876369726052611, type: 3}
      propertyPath: m_LocalPosition.y
      value: 0.00999999
      objectReference: {fileID: 0}
    - target: {fileID: 1031072880, guid: abc00000000001332876369726052611, type: 3}
      propertyPath: m_LocalPosition.z
      value: 9.929993
      objectReference: {fileID: 0}
    - target: {fileID: 1031072880, guid: abc00000000001332876369726052611, type: 3}
      propertyPath: m_LocalRotation.w
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 1031072880, guid: abc00000000001332876369726052611, type: 3}
      propertyPath: m_LocalRotation.x
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 1031072880, guid: abc00000000001332876369726052611, type: 3}
      propertyPath: m_LocalRotation.y
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 1031072880, guid: abc00000000001332876369726052611, type: 3}
      propertyPath: m_LocalRotation.z
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 1031072880, guid: abc00000000001332876369726052611, type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1031072880, guid: abc00000000001332876369726052611, type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1031072880, guid: abc00000000001332876369726052611, type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1031072880, guid: abc00000000001332876369726052611, type: 3}
      propertyPath: m_ConstrainProportionsScale
      value: 1
      objectReference: {fileID: 0}
    m_RemovedComponents: []
    m_RemovedGameObjects: []
    m_AddedGameObjects: []
    m_AddedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: abc00000000001332876369726052611, type: 3}
--- !u!4 &1197962280 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: 1031072880, guid: abc00000000001332876369726052611, type: 3}
  m_PrefabInstance: {fileID: 1197962279}
  m_PrefabAsset: {fileID: 0}
--- !u!1 &1228985241
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1228985242}
  m_Layer: 0
  m_Name: Helipad
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1228985242
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1228985241}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: -8.559998, y: -4.5, z: -21.450012}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 2080285413}
  - {fileID: 2073337801}
  m_Father: {fileID: 519976997}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1238138301
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1238138302}
  m_Layer: 0
  m_Name: PlayerSeats
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1238138302
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1238138301}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 2147199879}
  - {fileID: 1076447432}
  - {fileID: 896623775}
  - {fileID: 47753184}
  m_Father: {fileID: 543450653026896001}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1238260544
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1238260545}
  - component: {fileID: 1238260548}
  - component: {fileID: 1238260547}
  - component: {fileID: 1238260546}
  m_Layer: 5
  m_Name: SettingsPanel
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &1238260545
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1238260544}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 1
  m_Children:
  - {fileID: 443102734}
  - {fileID: 230490799}
  m_Father: {fileID: 2002829529}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0.5, y: 0.5}
  m_AnchorMax: {x: 0.5, y: 0.5}
  m_AnchoredPosition: {x: 0, y: -116.3299}
  m_SizeDelta: {x: 400, y: 500}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!225 &1238260546
CanvasGroup:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1238260544}
  m_Enabled: 1
  m_Alpha: 0
  m_Interactable: 0
  m_BlocksRaycasts: 0
  m_IgnoreParentGroups: 0
--- !u!114 &1238260547
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1238260544}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fe87c0e1cc204ed48ad3b37840f39efc, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 0, g: 0, b: 0, a: 0.9843137}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 0
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_Sprite: {fileID: 10907, guid: 0000000000000000f000000000000000, type: 0}
  m_Type: 2
  m_PreserveAspect: 0
  m_FillCenter: 1
  m_FillMethod: 4
  m_FillAmount: 1
  m_FillClockwise: 1
  m_FillOrigin: 0
  m_UseSpriteMesh: 0
  m_PixelsPerUnitMultiplier: 1
--- !u!222 &1238260548
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1238260544}
  m_CullTransparentMesh: 1
--- !u!1 &1245742950
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1245742951}
  m_Layer: 5
  m_Name: MusicVolumeSetting
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &1245742951
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1245742950}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: -0.00015364922}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1176333024}
  m_Father: {fileID: 1341548309}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 0, y: 0}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 0, y: 80.666664}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!1 &1260272814
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1260272815}
  - component: {fileID: 1260272818}
  - component: {fileID: 1260272817}
  - component: {fileID: 1260272816}
  m_Layer: 0
  m_Name: HelicopterExitWaypoint
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1260272815
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1260272814}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: 1, z: -0, w: 0}
  m_LocalPosition: {x: 7.316, y: 0.024, z: -7.8806}
  m_LocalScale: {x: 0.09, y: 0.09, z: 0.09}
  m_ConstrainProportionsScale: 1
  m_Children: []
  m_Father: {fileID: 1675261855}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!135 &1260272816
SphereCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1260272814}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 1
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 3
  m_Radius: 0.5
  m_Center: {x: 0, y: 0, z: 0}
--- !u!23 &1260272817
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1260272814}
  m_Enabled: 0
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 73c176f402d2c2f4d929aa5da7585d17, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!33 &1260272818
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1260272814}
  m_Mesh: {fileID: 10207, guid: 0000000000000000e000000000000000, type: 0}
--- !u!1 &1265990967
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1265990968}
  m_Layer: 0
  m_Name: ForestSceneManager
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1265990968
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1265990967}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 436.61496, y: 29.456612, z: 322.44885}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &1267524449
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: b1d046e3917e4d8580903c69758b6558, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  enabled: 1
  label: Height
  blendMode: 0
  opacity: 20
  passIndex: 0
  min: 0
  minFalloff: 1
  max: 8.14
  maxFalloff: 5.95
--- !u!1 &1275455218
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1275455219}
  - component: {fileID: 1275455220}
  m_Layer: 5
  m_Name: AmbienceVolumeSettingContainer
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &1275455219
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1275455218}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0.0001495125}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1801178230}
  - {fileID: 954295166}
  m_Father: {fileID: 1720069235}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 1}
  m_AnchorMax: {x: 0, y: 1}
  m_AnchoredPosition: {x: 200, y: -35}
  m_SizeDelta: {x: 288.36, y: 70}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &1275455220
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1275455218}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 59f8146938fff824cb5fd77236b75775, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Padding:
    m_Left: 0
    m_Right: 0
    m_Top: 0
    m_Bottom: 0
  m_ChildAlignment: 1
  m_Spacing: 0
  m_ChildForceExpandWidth: 1
  m_ChildForceExpandHeight: 1
  m_ChildControlWidth: 0
  m_ChildControlHeight: 0
  m_ChildScaleWidth: 0
  m_ChildScaleHeight: 0
  m_ReverseArrangement: 0
--- !u!1 &1279474992
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1279474993}
  - component: {fileID: 1279474995}
  - component: {fileID: 1279474994}
  m_Layer: 5
  m_Name: MusicVolumeSettingLabel
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &1279474993
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1279474992}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0.0000939624}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1176333024}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 0, y: 0}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 288.36, y: 50}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &1279474994
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1279474992}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f4688fdb7df04437aeb418b961361dc5, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_text: Music Volume
  m_isRightToLeft: 0
  m_fontAsset: {fileID: 11400000, guid: 8f586378b4e144a9851e7b34d9b748ee, type: 2}
  m_sharedMaterial: {fileID: 2180264, guid: 8f586378b4e144a9851e7b34d9b748ee, type: 2}
  m_fontSharedMaterials: []
  m_fontMaterial: {fileID: 0}
  m_fontMaterials: []
  m_fontColor32:
    serializedVersion: 2
    rgba: 4294967295
  m_fontColor: {r: 1, g: 1, b: 1, a: 1}
  m_enableVertexGradient: 0
  m_colorMode: 3
  m_fontColorGradient:
    topLeft: {r: 1, g: 1, b: 1, a: 1}
    topRight: {r: 1, g: 1, b: 1, a: 1}
    bottomLeft: {r: 1, g: 1, b: 1, a: 1}
    bottomRight: {r: 1, g: 1, b: 1, a: 1}
  m_fontColorGradientPreset: {fileID: 0}
  m_spriteAsset: {fileID: 0}
  m_tintAllSprites: 0
  m_StyleSheet: {fileID: 0}
  m_TextStyleHashCode: -1183493901
  m_overrideHtmlColors: 0
  m_faceColor:
    serializedVersion: 2
    rgba: 4294967295
  m_fontSize: 33.6
  m_fontSizeBase: 33.6
  m_fontWeight: 400
  m_enableAutoSizing: 0
  m_fontSizeMin: 18
  m_fontSizeMax: 72
  m_fontStyle: 0
  m_HorizontalAlignment: 2
  m_VerticalAlignment: 512
  m_textAlignment: 65535
  m_characterSpacing: 0
  m_wordSpacing: 0
  m_lineSpacing: 0
  m_lineSpacingMax: 0
  m_paragraphSpacing: 0
  m_charWidthMaxAdj: 0
  m_TextWrappingMode: 0
  m_wordWrappingRatios: 0.4
  m_overflowMode: 0
  m_linkedTextComponent: {fileID: 0}
  parentLinkedComponent: {fileID: 0}
  m_enableKerning: 0
  m_ActiveFontFeatures: 6e72656b
  m_enableExtraPadding: 0
  checkPaddingRequired: 0
  m_isRichText: 1
  m_EmojiFallbackSupport: 1
  m_parseCtrlCharacters: 1
  m_isOrthographic: 1
  m_isCullingEnabled: 0
  m_horizontalMapping: 0
  m_verticalMapping: 0
  m_uvLineOffset: 0
  m_geometrySortingOrder: 0
  m_IsTextObjectScaleStatic: 0
  m_VertexBufferAutoSizeReduction: 0
  m_useMaxVisibleDescender: 1
  m_pageToDisplay: 1
  m_margin: {x: 0, y: 0, z: 0, w: 0}
  m_isUsingLegacyAnimationComponent: 0
  m_isVolumetricText: 0
  m_hasFontAssetChanged: 0
  m_baseMaterial: {fileID: 0}
  m_maskOffset: {x: 0, y: 0, z: 0, w: 0}
--- !u!222 &1279474995
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1279474992}
  m_CullTransparentMesh: 1
--- !u!1 &1309079193
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1309079194}
  - component: {fileID: 1309079197}
  - component: {fileID: 1309079196}
  - component: {fileID: 1309079195}
  m_Layer: 0
  m_Name: Cube_2
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1309079194
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1309079193}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: 0.7071068, z: -0, w: 0.7071068}
  m_LocalPosition: {x: 5.8099976, y: 0.5109999, z: 5.395996}
  m_LocalScale: {x: 4.272373, y: 0.001, z: 0.29843688}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 2073337801}
  m_LocalEulerAnglesHint: {x: 0, y: 90, z: 0}
--- !u!65 &1309079195
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1309079193}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 3
  m_Size: {x: 1, y: 1, z: 1}
  m_Center: {x: 0, y: 0, z: 0}
--- !u!23 &1309079196
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1309079193}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: c9ef99fcd36d4d34da42acc2c4a49379, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!33 &1309079197
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1309079193}
  m_Mesh: {fileID: 10202, guid: 0000000000000000e000000000000000, type: 0}
--- !u!1 &1309550036
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1309550037}
  - component: {fileID: 1309550039}
  - component: {fileID: 1309550038}
  m_Layer: 5
  m_Name: Background
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &1309550037
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1309550036}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1658130456}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0.25}
  m_AnchorMax: {x: 1, y: 0.75}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 0, y: 0}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &1309550038
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1309550036}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fe87c0e1cc204ed48ad3b37840f39efc, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_Sprite: {fileID: 10907, guid: 0000000000000000f000000000000000, type: 0}
  m_Type: 1
  m_PreserveAspect: 0
  m_FillCenter: 1
  m_FillMethod: 4
  m_FillAmount: 1
  m_FillClockwise: 1
  m_FillOrigin: 0
  m_UseSpriteMesh: 0
  m_PixelsPerUnitMultiplier: 1
--- !u!222 &1309550039
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1309550036}
  m_CullTransparentMesh: 1
--- !u!1 &1341548308
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1341548309}
  - component: {fileID: 1341548311}
  - component: {fileID: 1341548310}
  m_Layer: 5
  m_Name: AudioSettings
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &1341548309
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1341548308}
  m_LocalRotation: {x: -0, y: -0.18600939, z: -0, w: -0.982548}
  m_LocalPosition: {x: 0, y: 0, z: 464.34363}
  m_LocalScale: {x: 0.0007879763, y: 0.0007879763, z: 0.0007879763}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 910232116}
  - {fileID: 1245742951}
  - {fileID: 869431280}
  - {fileID: 1720069235}
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 1}
  m_AnchorMax: {x: 0, y: 1}
  m_AnchoredPosition: {x: 841988.75, y: 358374.38}
  m_SizeDelta: {x: 400, y: 288.01}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &1341548310
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1341548308}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 2adc56f699f9dd740a70044e99d67915, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  masterVolume: 100
  musicVolume: 100
  soundFxVolume: 100
  dialogueVolume: 100
  enemyVolume: 100
  ambienceVolume: 75
--- !u!114 &1341548311
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1341548308}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 59f8146938fff824cb5fd77236b75775, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Padding:
    m_Left: 0
    m_Right: 0
    m_Top: 0
    m_Bottom: 74
  m_ChildAlignment: 1
  m_Spacing: 0
  m_ChildForceExpandWidth: 1
  m_ChildForceExpandHeight: 0
  m_ChildControlWidth: 1
  m_ChildControlHeight: 0
  m_ChildScaleWidth: 1
  m_ChildScaleHeight: 0
  m_ReverseArrangement: 0
--- !u!1 &1352050520
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1352050521}
  - component: {fileID: 1352050523}
  - component: {fileID: 1352050522}
  m_Layer: 5
  m_Name: MasterVolumeSettingLabelMin
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &1352050521
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1352050520}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 2120279515}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0.5, y: 0.5}
  m_AnchorMax: {x: 0.5, y: 0.5}
  m_AnchoredPosition: {x: -153.9, y: 0}
  m_SizeDelta: {x: 100, y: 100}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &1352050522
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1352050520}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f4688fdb7df04437aeb418b961361dc5, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_text: MUTE
  m_isRightToLeft: 0
  m_fontAsset: {fileID: 11400000, guid: 8f586378b4e144a9851e7b34d9b748ee, type: 2}
  m_sharedMaterial: {fileID: 2180264, guid: 8f586378b4e144a9851e7b34d9b748ee, type: 2}
  m_fontSharedMaterials: []
  m_fontMaterial: {fileID: 0}
  m_fontMaterials: []
  m_fontColor32:
    serializedVersion: 2
    rgba: 4294967295
  m_fontColor: {r: 1, g: 1, b: 1, a: 1}
  m_enableVertexGradient: 0
  m_colorMode: 3
  m_fontColorGradient:
    topLeft: {r: 1, g: 1, b: 1, a: 1}
    topRight: {r: 1, g: 1, b: 1, a: 1}
    bottomLeft: {r: 1, g: 1, b: 1, a: 1}
    bottomRight: {r: 1, g: 1, b: 1, a: 1}
  m_fontColorGradientPreset: {fileID: 0}
  m_spriteAsset: {fileID: 0}
  m_tintAllSprites: 0
  m_StyleSheet: {fileID: 0}
  m_TextStyleHashCode: -1183493901
  m_overrideHtmlColors: 0
  m_faceColor:
    serializedVersion: 2
    rgba: 4294967295
  m_fontSize: 20
  m_fontSizeBase: 20
  m_fontWeight: 400
  m_enableAutoSizing: 0
  m_fontSizeMin: 18
  m_fontSizeMax: 72
  m_fontStyle: 0
  m_HorizontalAlignment: 2
  m_VerticalAlignment: 512
  m_textAlignment: 65535
  m_characterSpacing: 0
  m_wordSpacing: 0
  m_lineSpacing: 0
  m_lineSpacingMax: 0
  m_paragraphSpacing: 0
  m_charWidthMaxAdj: 0
  m_TextWrappingMode: 0
  m_wordWrappingRatios: 0.4
  m_overflowMode: 0
  m_linkedTextComponent: {fileID: 0}
  parentLinkedComponent: {fileID: 0}
  m_enableKerning: 0
  m_ActiveFontFeatures: 6e72656b
  m_enableExtraPadding: 0
  checkPaddingRequired: 0
  m_isRichText: 1
  m_EmojiFallbackSupport: 1
  m_parseCtrlCharacters: 1
  m_isOrthographic: 1
  m_isCullingEnabled: 0
  m_horizontalMapping: 0
  m_verticalMapping: 0
  m_uvLineOffset: 0
  m_geometrySortingOrder: 0
  m_IsTextObjectScaleStatic: 0
  m_VertexBufferAutoSizeReduction: 0
  m_useMaxVisibleDescender: 1
  m_pageToDisplay: 1
  m_margin: {x: 0, y: 0, z: 0, w: 0}
  m_isUsingLegacyAnimationComponent: 0
  m_isVolumetricText: 0
  m_hasFontAssetChanged: 0
  m_baseMaterial: {fileID: 0}
  m_maskOffset: {x: 0, y: 0, z: 0, w: 0}
--- !u!222 &1352050523
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1352050520}
  m_CullTransparentMesh: 1
--- !u!1 &1355259849
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1355259850}
  m_Layer: 5
  m_Name: Handle Slide Area
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &1355259850
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1355259849}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1879765801}
  m_Father: {fileID: 2124822543}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 1, y: 1}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: -20, y: 0}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!1 &1371030939
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1371030940}
  m_Layer: 0
  m_Name: HelicopterWaypoint_3
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1371030940
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1371030939}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1812692992}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!134 &1407554859
PhysicsMaterial:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: 
  serializedVersion: 2
  m_DynamicFriction: 0.6
  m_StaticFriction: 0.6
  m_Bounciness: 0
  m_FrictionCombine: 0
  m_BounceCombine: 0
--- !u!1 &1423917790
GameObject:
  m_ObjectHideFlags: 16
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1423917792}
  - component: {fileID: 1423917791}
  m_Layer: 0
  m_Name: Color Checker
  m_TagString: EditorOnly
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!114 &1423917791
MonoBehaviour:
  m_ObjectHideFlags: 16
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1423917790}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 202560f0d85a19845ad0a8f387f992bb, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  Mode: 0
  addGradient: 0
  unlitCompare: 0
  sphereMode: 0
  fieldCount: 24
  materialFieldsCount: 6
  fieldsPerRow: 6
  gridThickness: 0.1
  fieldSize: 0.1
  gradientPower: 2.2
  gradientA:
    serializedVersion: 2
    rgba: 4279637011
  gradientB:
    serializedVersion: 2
    rgba: 4293126633
  colorCheckerTexture: {fileID: 0}
  userTexture: {fileID: 0}
  userTextureRaw: {fileID: 0}
  textureSlice: 0
  unlitTextureExposure: 1
  customColors:
  - serializedVersion: 2
    rgba: 4293981685
  - serializedVersion: 2
    rgba: 4291414729
  - serializedVersion: 2
    rgba: 4288848545
  - serializedVersion: 2
    rgba: 4286151032
  - serializedVersion: 2
    rgba: 4283782483
  - serializedVersion: 2
    rgba: 4281545266
  - serializedVersion: 2
    rgba: 4287840042
  - serializedVersion: 2
    rgba: 4282946888
  - serializedVersion: 2
    rgba: 4281938607
  - serializedVersion: 2
    rgba: 4279683310
  - serializedVersion: 2
    rgba: 4288042172
  - serializedVersion: 2
    rgba: 4289104128
  - serializedVersion: 2
    rgba: 4281236444
  - serializedVersion: 2
    rgba: 4289223752
  - serializedVersion: 2
    rgba: 4284568770
  - serializedVersion: 2
    rgba: 4285021019
  - serializedVersion: 2
    rgba: 4282301857
  - serializedVersion: 2
    rgba: 4280852965
  - serializedVersion: 2
    rgba: 4282667635
  - serializedVersion: 2
    rgba: 4286617026
  - serializedVersion: 2
    rgba: 4288510813
  - serializedVersion: 2
    rgba: 4282477659
  - serializedVersion: 2
    rgba: 4289692034
  - serializedVersion: 2
    rgba: 4289445731
  - serializedVersion: 2
    rgba: 4281479730
  - serializedVersion: 2
    rgba: 4294177779
  - serializedVersion: 2
    rgba: 4281417045
  - serializedVersion: 2
    rgba: 4282145927
  - serializedVersion: 2
    rgba: 4284180338
  - serializedVersion: 2
    rgba: 4281631355
  - serializedVersion: 2
    rgba: 4285889940
  - serializedVersion: 2
    rgba: 4286810247
  - serializedVersion: 2
    rgba: 4288914339
  - serializedVersion: 2
    rgba: 4286883761
  - serializedVersion: 2
    rgba: 4290494400
  - serializedVersion: 2
    rgba: 4289251296
  - serializedVersion: 2
    rgba: 4289895884
  - serializedVersion: 2
    rgba: 4287396028
  - serializedVersion: 2
    rgba: 4288505467
  - serializedVersion: 2
    rgba: 4289103207
  - serializedVersion: 2
    rgba: 4291143561
  - serializedVersion: 2
    rgba: 4287340407
  - serializedVersion: 2
    rgba: 4286407217
  - serializedVersion: 2
    rgba: 4283793986
  - serializedVersion: 2
    rgba: 4281638105
  - serializedVersion: 2
    rgba: 4283200456
  - serializedVersion: 2
    rgba: 4282136239
  - serializedVersion: 2
    rgba: 4286333876
  - serializedVersion: 2
    rgba: 4287188791
  - serializedVersion: 2
    rgba: 4287389992
  - serializedVersion: 2
    rgba: 4288643161
  - serializedVersion: 2
    rgba: 4285243272
  - serializedVersion: 2
    rgba: 4285894241
  - serializedVersion: 2
    rgba: 4283913001
  - serializedVersion: 2
    rgba: 4280431502
  - serializedVersion: 2
    rgba: 4283200456
  - serializedVersion: 2
    rgba: 4279732180
  - serializedVersion: 2
    rgba: 4285685412
  - serializedVersion: 2
    rgba: 4287396298
  - serializedVersion: 2
    rgba: 4284365920
  - serializedVersion: 2
    rgba: 4293126633
  - serializedVersion: 2
    rgba: 4287796115
  - serializedVersion: 2
    rgba: 4282006071
  - serializedVersion: 2
    rgba: 4279637011
  textureColors:
  - serializedVersion: 2
    rgba: 4293981685
  - serializedVersion: 2
    rgba: 4291414729
  - serializedVersion: 2
    rgba: 4288848545
  - serializedVersion: 2
    rgba: 4286151032
  - serializedVersion: 2
    rgba: 4283782483
  - serializedVersion: 2
    rgba: 4281545266
  - serializedVersion: 2
    rgba: 4287840042
  - serializedVersion: 2
    rgba: 4282946888
  - serializedVersion: 2
    rgba: 4281938607
  - serializedVersion: 2
    rgba: 4279683310
  - serializedVersion: 2
    rgba: 4288042172
  - serializedVersion: 2
    rgba: 4289104128
  - serializedVersion: 2
    rgba: 4281236444
  - serializedVersion: 2
    rgba: 4289223752
  - serializedVersion: 2
    rgba: 4284568770
  - serializedVersion: 2
    rgba: 4285021019
  - serializedVersion: 2
    rgba: 4282301857
  - serializedVersion: 2
    rgba: 4280852965
  - serializedVersion: 2
    rgba: 4282667635
  - serializedVersion: 2
    rgba: 4286617026
  - serializedVersion: 2
    rgba: 4288510813
  - serializedVersion: 2
    rgba: 4282477659
  - serializedVersion: 2
    rgba: 4289692034
  - serializedVersion: 2
    rgba: 4289445731
  - serializedVersion: 2
    rgba: 4281479730
  - serializedVersion: 2
    rgba: 4294177779
  - serializedVersion: 2
    rgba: 4281417045
  - serializedVersion: 2
    rgba: 4282145927
  - serializedVersion: 2
    rgba: 4284180338
  - serializedVersion: 2
    rgba: 4281631355
  - serializedVersion: 2
    rgba: 4285889940
  - serializedVersion: 2
    rgba: 4286810247
  - serializedVersion: 2
    rgba: 4288914339
  - serializedVersion: 2
    rgba: 4286883761
  - serializedVersion: 2
    rgba: 4290494400
  - serializedVersion: 2
    rgba: 4289251296
  - serializedVersion: 2
    rgba: 4289895884
  - serializedVersion: 2
    rgba: 4287396028
  - serializedVersion: 2
    rgba: 4288505467
  - serializedVersion: 2
    rgba: 4289103207
  - serializedVersion: 2
    rgba: 4291143561
  - serializedVersion: 2
    rgba: 4287340407
  - serializedVersion: 2
    rgba: 4286407217
  - serializedVersion: 2
    rgba: 4283793986
  - serializedVersion: 2
    rgba: 4281638105
  - serializedVersion: 2
    rgba: 4283200456
  - serializedVersion: 2
    rgba: 4282136239
  - serializedVersion: 2
    rgba: 4286333876
  - serializedVersion: 2
    rgba: 4287188791
  - serializedVersion: 2
    rgba: 4287389992
  - serializedVersion: 2
    rgba: 4288643161
  - serializedVersion: 2
    rgba: 4285243272
  - serializedVersion: 2
    rgba: 4285894241
  - serializedVersion: 2
    rgba: 4283913001
  - serializedVersion: 2
    rgba: 4280431502
  - serializedVersion: 2
    rgba: 4283200456
  - serializedVersion: 2
    rgba: 4279732180
  - serializedVersion: 2
    rgba: 4285685412
  - serializedVersion: 2
    rgba: 4287396298
  - serializedVersion: 2
    rgba: 4284365920
  - serializedVersion: 2
    rgba: 4293126633
  - serializedVersion: 2
    rgba: 4287796115
  - serializedVersion: 2
    rgba: 4282006071
  - serializedVersion: 2
    rgba: 4279637011
  CrossPolarizedGrayscale:
  - serializedVersion: 2
    rgba: 4279637011
  - serializedVersion: 2
    rgba: 4282006071
  - serializedVersion: 2
    rgba: 4284769893
  - serializedVersion: 2
    rgba: 4287796115
  - serializedVersion: 2
    rgba: 4290493626
  - serializedVersion: 2
    rgba: 4293126633
  MiddleGray:
  - serializedVersion: 2
    rgba: 4286151032
  steppedLuminance:
  - serializedVersion: 2
    rgba: 4278190080
  - serializedVersion: 2
    rgba: 4279308561
  - serializedVersion: 2
    rgba: 4280427042
  - serializedVersion: 2
    rgba: 4281545523
  - serializedVersion: 2
    rgba: 4282664004
  - serializedVersion: 2
    rgba: 4283782485
  - serializedVersion: 2
    rgba: 4284900966
  - serializedVersion: 2
    rgba: 4286019447
  - serializedVersion: 2
    rgba: 4287137928
  - serializedVersion: 2
    rgba: 4288256409
  - serializedVersion: 2
    rgba: 4289374890
  - serializedVersion: 2
    rgba: 4290493371
  - serializedVersion: 2
    rgba: 4291611852
  - serializedVersion: 2
    rgba: 4292730333
  - serializedVersion: 2
    rgba: 4293848814
  - serializedVersion: 2
    rgba: 4294967295
  customMaterials:
  - serializedVersion: 2
    rgba: 15592941
  - serializedVersion: 2
    rgba: 2565927
  - serializedVersion: 2
    rgba: 4290494145
  - serializedVersion: 2
    rgba: 4290567671
  - serializedVersion: 2
    rgba: 4294375931
  - serializedVersion: 2
    rgba: 4288996601
  - serializedVersion: 2
    rgba: 3946159
  - serializedVersion: 2
    rgba: 8693681
  - serializedVersion: 2
    rgba: 4418647
  - serializedVersion: 2
    rgba: 10320482
  - serializedVersion: 2
    rgba: 4294374901
  - serializedVersion: 2
    rgba: 4289783538
  isMetalBools: 000001010101000000000101
  ColorCheckerObject: {fileID: 811078242}
--- !u!4 &1423917792
Transform:
  m_ObjectHideFlags: 16
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1423917790}
  serializedVersion: 2
  m_LocalRotation: {x: 0.000019298266, y: -0.012253592, z: -0.0015747868, w: 0.9999237}
  m_LocalPosition: {x: 405.454, y: 8.155937, z: 507.5886}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 811078245}
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1001 &1426693936
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    serializedVersion: 3
    m_TransformParent: {fileID: 708659531}
    m_Modifications:
    - target: {fileID: 1031082323, guid: abc00000000013809728047498463989, type: 3}
      propertyPath: m_Name
      value: SM_Locker_01a_Body34
      objectReference: {fileID: 0}
    - target: {fileID: 1031082324, guid: abc00000000013809728047498463989, type: 3}
      propertyPath: m_LocalScale.x
      value: 2
      objectReference: {fileID: 0}
    - target: {fileID: 1031082324, guid: abc00000000013809728047498463989, type: 3}
      propertyPath: m_LocalScale.y
      value: 2
      objectReference: {fileID: 0}
    - target: {fileID: 1031082324, guid: abc00000000013809728047498463989, type: 3}
      propertyPath: m_LocalScale.z
      value: 2
      objectReference: {fileID: 0}
    - target: {fileID: 1031082324, guid: abc00000000013809728047498463989, type: 3}
      propertyPath: m_LocalPosition.x
      value: -1.5200195
      objectReference: {fileID: 0}
    - target: {fileID: 1031082324, guid: abc00000000013809728047498463989, type: 3}
      propertyPath: m_LocalPosition.y
      value: 0.016000032
      objectReference: {fileID: 0}
    - target: {fileID: 1031082324, guid: abc00000000013809728047498463989, type: 3}
      propertyPath: m_LocalPosition.z
      value: 10.22998
      objectReference: {fileID: 0}
    - target: {fileID: 1031082324, guid: abc00000000013809728047498463989, type: 3}
      propertyPath: m_LocalRotation.w
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 1031082324, guid: abc00000000013809728047498463989, type: 3}
      propertyPath: m_LocalRotation.x
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 1031082324, guid: abc00000000013809728047498463989, type: 3}
      propertyPath: m_LocalRotation.y
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 1031082324, guid: abc00000000013809728047498463989, type: 3}
      propertyPath: m_LocalRotation.z
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 1031082324, guid: abc00000000013809728047498463989, type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1031082324, guid: abc00000000013809728047498463989, type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1031082324, guid: abc00000000013809728047498463989, type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1031082324, guid: abc00000000013809728047498463989, type: 3}
      propertyPath: m_ConstrainProportionsScale
      value: 1
      objectReference: {fileID: 0}
    m_RemovedComponents: []
    m_RemovedGameObjects: []
    m_AddedGameObjects: []
    m_AddedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: abc00000000013809728047498463989, type: 3}
--- !u!4 &1426693937 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: 1031082324, guid: abc00000000013809728047498463989, type: 3}
  m_PrefabInstance: {fileID: 1426693936}
  m_PrefabAsset: {fileID: 0}
--- !u!1 &1428216458
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1428216459}
  - component: {fileID: 1428216460}
  m_Layer: 5
  m_Name: MasterVolumeContainer
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &1428216459
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1428216458}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0.0001495125}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 949861110}
  - {fileID: 2120279515}
  m_Father: {fileID: 910232116}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 1}
  m_AnchorMax: {x: 0, y: 1}
  m_AnchoredPosition: {x: 200, y: -35}
  m_SizeDelta: {x: 288.36, y: 70}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &1428216460
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1428216458}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 59f8146938fff824cb5fd77236b75775, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Padding:
    m_Left: 0
    m_Right: 0
    m_Top: 0
    m_Bottom: 0
  m_ChildAlignment: 1
  m_Spacing: 0
  m_ChildForceExpandWidth: 1
  m_ChildForceExpandHeight: 1
  m_ChildControlWidth: 0
  m_ChildControlHeight: 0
  m_ChildScaleWidth: 0
  m_ChildScaleHeight: 0
  m_ReverseArrangement: 0
--- !u!134 &1435913709
PhysicsMaterial:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: 
  serializedVersion: 2
  m_DynamicFriction: 0.6
  m_StaticFriction: 0.6
  m_Bounciness: 0
  m_FrictionCombine: 0
  m_BounceCombine: 0
--- !u!1 &1439646036
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1439646037}
  m_Layer: 0
  m_Name: AudioSources
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1439646037
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1439646036}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: -39.478638, y: -33.43802, z: 2.8634834}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 810982375}
  - {fileID: 692591513}
  - {fileID: 225600833}
  m_Father: {fileID: 173380679}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1001 &1480547227
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    serializedVersion: 3
    m_TransformParent: {fileID: 708659531}
    m_Modifications:
    - target: {fileID: 1031115138, guid: abc00000000010844172548760726064, type: 3}
      propertyPath: m_Name
      value: SM_Stretcher_492
      objectReference: {fileID: 0}
    - target: {fileID: 1031115139, guid: abc00000000010844172548760726064, type: 3}
      propertyPath: m_LocalScale.x
      value: 2.3
      objectReference: {fileID: 0}
    - target: {fileID: 1031115139, guid: abc00000000010844172548760726064, type: 3}
      propertyPath: m_LocalScale.y
      value: 2.3
      objectReference: {fileID: 0}
    - target: {fileID: 1031115139, guid: abc00000000010844172548760726064, type: 3}
      propertyPath: m_LocalScale.z
      value: 2.3
      objectReference: {fileID: 0}
    - target: {fileID: 1031115139, guid: abc00000000010844172548760726064, type: 3}
      propertyPath: m_LocalPosition.x
      value: -10.544128
      objectReference: {fileID: 0}
    - target: {fileID: 1031115139, guid: abc00000000010844172548760726064, type: 3}
      propertyPath: m_LocalPosition.y
      value: 0.826
      objectReference: {fileID: 0}
    - target: {fileID: 1031115139, guid: abc00000000010844172548760726064, type: 3}
      propertyPath: m_LocalPosition.z
      value: -3.9519958
      objectReference: {fileID: 0}
    - target: {fileID: 1031115139, guid: abc00000000010844172548760726064, type: 3}
      propertyPath: m_LocalRotation.w
      value: 0.7071068
      objectReference: {fileID: 0}
    - target: {fileID: 1031115139, guid: abc00000000010844172548760726064, type: 3}
      propertyPath: m_LocalRotation.x
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 1031115139, guid: abc00000000010844172548760726064, type: 3}
      propertyPath: m_LocalRotation.y
      value: -0.7071068
      objectReference: {fileID: 0}
    - target: {fileID: 1031115139, guid: abc00000000010844172548760726064, type: 3}
      propertyPath: m_LocalRotation.z
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 1031115139, guid: abc00000000010844172548760726064, type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1031115139, guid: abc00000000010844172548760726064, type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: -90
      objectReference: {fileID: 0}
    - target: {fileID: 1031115139, guid: abc00000000010844172548760726064, type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1031115139, guid: abc00000000010844172548760726064, type: 3}
      propertyPath: m_ConstrainProportionsScale
      value: 1
      objectReference: {fileID: 0}
    m_RemovedComponents: []
    m_RemovedGameObjects: []
    m_AddedGameObjects: []
    m_AddedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: abc00000000010844172548760726064, type: 3}
--- !u!4 &1480547228 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: 1031115139, guid: abc00000000010844172548760726064, type: 3}
  m_PrefabInstance: {fileID: 1480547227}
  m_PrefabAsset: {fileID: 0}
--- !u!1001 &1496791937
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    serializedVersion: 3
    m_TransformParent: {fileID: 1675261855}
    m_Modifications:
    - target: {fileID: 1030975294, guid: abc00000000006504693431563510938, type: 3}
      propertyPath: m_Name
      value: SM_Roadblock_3
      objectReference: {fileID: 0}
    - target: {fileID: 1030975295, guid: abc00000000006504693431563510938, type: 3}
      propertyPath: m_LocalScale.x
      value: 0.14657001
      objectReference: {fileID: 0}
    - target: {fileID: 1030975295, guid: abc00000000006504693431563510938, type: 3}
      propertyPath: m_LocalScale.y
      value: 0.14657001
      objectReference: {fileID: 0}
    - target: {fileID: 1030975295, guid: abc00000000006504693431563510938, type: 3}
      propertyPath: m_LocalScale.z
      value: 0.14657001
      objectReference: {fileID: 0}
    - target: {fileID: 1030975295, guid: abc00000000006504693431563510938, type: 3}
      propertyPath: m_LocalPosition.x
      value: 10.0000305
      objectReference: {fileID: 0}
    - target: {fileID: 1030975295, guid: abc00000000006504693431563510938, type: 3}
      propertyPath: m_LocalPosition.y
      value: 0.15500002
      objectReference: {fileID: 0}
    - target: {fileID: 1030975295, guid: abc00000000006504693431563510938, type: 3}
      propertyPath: m_LocalPosition.z
      value: -8.686999
      objectReference: {fileID: 0}
    - target: {fileID: 1030975295, guid: abc00000000006504693431563510938, type: 3}
      propertyPath: m_LocalRotation.w
      value: 0.4787991
      objectReference: {fileID: 0}
    - target: {fileID: 1030975295, guid: abc00000000006504693431563510938, type: 3}
      propertyPath: m_LocalRotation.x
      value: -0.06108207
      objectReference: {fileID: 0}
    - target: {fileID: 1030975295, guid: abc00000000006504693431563510938, type: 3}
      propertyPath: m_LocalRotation.y
      value: 0.87515926
      objectReference: {fileID: 0}
    - target: {fileID: 1030975295, guid: abc00000000006504693431563510938, type: 3}
      propertyPath: m_LocalRotation.z
      value: 0.033417974
      objectReference: {fileID: 0}
    - target: {fileID: 1030975295, guid: abc00000000006504693431563510938, type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: -6.718
      objectReference: {fileID: 0}
    - target: {fileID: 1030975295, guid: abc00000000006504693431563510938, type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 122.888
      objectReference: {fileID: 0}
    - target: {fileID: 1030975295, guid: abc00000000006504693431563510938, type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: -4.326
      objectReference: {fileID: 0}
    - target: {fileID: 1030975295, guid: abc00000000006504693431563510938, type: 3}
      propertyPath: m_ConstrainProportionsScale
      value: 1
      objectReference: {fileID: 0}
    m_RemovedComponents: []
    m_RemovedGameObjects: []
    m_AddedGameObjects: []
    m_AddedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: abc00000000006504693431563510938, type: 3}
--- !u!4 &1496791938 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: 1030975295, guid: abc00000000006504693431563510938, type: 3}
  m_PrefabInstance: {fileID: 1496791937}
  m_PrefabAsset: {fileID: 0}
--- !u!1001 &1508310611
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    serializedVersion: 3
    m_TransformParent: {fileID: 1675261855}
    m_Modifications:
    - target: {fileID: 1030975294, guid: abc00000000006504693431563510938, type: 3}
      propertyPath: m_Name
      value: SM_Roadblock_2
      objectReference: {fileID: 0}
    - target: {fileID: 1030975295, guid: abc00000000006504693431563510938, type: 3}
      propertyPath: m_LocalScale.x
      value: 0.14657001
      objectReference: {fileID: 0}
    - target: {fileID: 1030975295, guid: abc00000000006504693431563510938, type: 3}
      propertyPath: m_LocalScale.y
      value: 0.14657001
      objectReference: {fileID: 0}
    - target: {fileID: 1030975295, guid: abc00000000006504693431563510938, type: 3}
      propertyPath: m_LocalScale.z
      value: 0.14657001
      objectReference: {fileID: 0}
    - target: {fileID: 1030975295, guid: abc00000000006504693431563510938, type: 3}
      propertyPath: m_LocalPosition.x
      value: 8.975998
      objectReference: {fileID: 0}
    - target: {fileID: 1030975295, guid: abc00000000006504693431563510938, type: 3}
      propertyPath: m_LocalPosition.y
      value: -0.02199998
      objectReference: {fileID: 0}
    - target: {fileID: 1030975295, guid: abc00000000006504693431563510938, type: 3}
      propertyPath: m_LocalPosition.z
      value: -8.087997
      objectReference: {fileID: 0}
    - target: {fileID: 1030975295, guid: abc00000000006504693431563510938, type: 3}
      propertyPath: m_LocalRotation.w
      value: 0.4787991
      objectReference: {fileID: 0}
    - target: {fileID: 1030975295, guid: abc00000000006504693431563510938, type: 3}
      propertyPath: m_LocalRotation.x
      value: -0.06108207
      objectReference: {fileID: 0}
    - target: {fileID: 1030975295, guid: abc00000000006504693431563510938, type: 3}
      propertyPath: m_LocalRotation.y
      value: 0.87515926
      objectReference: {fileID: 0}
    - target: {fileID: 1030975295, guid: abc00000000006504693431563510938, type: 3}
      propertyPath: m_LocalRotation.z
      value: 0.033417974
      objectReference: {fileID: 0}
    - target: {fileID: 1030975295, guid: abc00000000006504693431563510938, type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: -6.718
      objectReference: {fileID: 0}
    - target: {fileID: 1030975295, guid: abc00000000006504693431563510938, type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 122.888
      objectReference: {fileID: 0}
    - target: {fileID: 1030975295, guid: abc00000000006504693431563510938, type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: -4.326
      objectReference: {fileID: 0}
    - target: {fileID: 1030975295, guid: abc00000000006504693431563510938, type: 3}
      propertyPath: m_ConstrainProportionsScale
      value: 1
      objectReference: {fileID: 0}
    m_RemovedComponents: []
    m_RemovedGameObjects: []
    m_AddedGameObjects: []
    m_AddedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: abc00000000006504693431563510938, type: 3}
--- !u!4 &1508310612 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: 1030975295, guid: abc00000000006504693431563510938, type: 3}
  m_PrefabInstance: {fileID: 1508310611}
  m_PrefabAsset: {fileID: 0}
--- !u!1 &1513151533
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1513151534}
  m_Layer: 0
  m_Name: SpawnPoint_0
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1513151534
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1513151533}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1076447432}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1001 &1515367260
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    serializedVersion: 3
    m_TransformParent: {fileID: 1675261855}
    m_Modifications:
    - target: {fileID: 1030975294, guid: abc00000000006504693431563510938, type: 3}
      propertyPath: m_Name
      value: SM_Roadblock_8
      objectReference: {fileID: 0}
    - target: {fileID: 1030975295, guid: abc00000000006504693431563510938, type: 3}
      propertyPath: m_LocalScale.x
      value: 0.14657001
      objectReference: {fileID: 0}
    - target: {fileID: 1030975295, guid: abc00000000006504693431563510938, type: 3}
      propertyPath: m_LocalScale.y
      value: 0.14657001
      objectReference: {fileID: 0}
    - target: {fileID: 1030975295, guid: abc00000000006504693431563510938, type: 3}
      propertyPath: m_LocalScale.z
      value: 0.14657001
      objectReference: {fileID: 0}
    - target: {fileID: 1030975295, guid: abc00000000006504693431563510938, type: 3}
      propertyPath: m_LocalPosition.x
      value: 9.8280735
      objectReference: {fileID: 0}
    - target: {fileID: 1030975295, guid: abc00000000006504693431563510938, type: 3}
      propertyPath: m_LocalPosition.y
      value: 0.10900002
      objectReference: {fileID: 0}
    - target: {fileID: 1030975295, guid: abc00000000006504693431563510938, type: 3}
      propertyPath: m_LocalPosition.z
      value: -9.8569975
      objectReference: {fileID: 0}
    - target: {fileID: 1030975295, guid: abc00000000006504693431563510938, type: 3}
      propertyPath: m_LocalRotation.w
      value: 0.5607457
      objectReference: {fileID: 0}
    - target: {fileID: 1030975295, guid: abc00000000006504693431563510938, type: 3}
      propertyPath: m_LocalRotation.x
      value: -0.1156017
      objectReference: {fileID: 0}
    - target: {fileID: 1030975295, guid: abc00000000006504693431563510938, type: 3}
      propertyPath: m_LocalRotation.y
      value: 0.81749576
      objectReference: {fileID: 0}
    - target: {fileID: 1030975295, guid: abc00000000006504693431563510938, type: 3}
      propertyPath: m_LocalRotation.z
      value: 0.062459372
      objectReference: {fileID: 0}
    - target: {fileID: 1030975295, guid: abc00000000006504693431563510938, type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: -13.401
      objectReference: {fileID: 0}
    - target: {fileID: 1030975295, guid: abc00000000006504693431563510938, type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 111.931
      objectReference: {fileID: 0}
    - target: {fileID: 1030975295, guid: abc00000000006504693431563510938, type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: -7.024
      objectReference: {fileID: 0}
    - target: {fileID: 1030975295, guid: abc00000000006504693431563510938, type: 3}
      propertyPath: m_ConstrainProportionsScale
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 1030975303, guid: abc00000000006504693431563510938, type: 3}
      propertyPath: m_LocalRotation.w
      value: 0.989596
      objectReference: {fileID: 0}
    - target: {fileID: 1030975303, guid: abc00000000006504693431563510938, type: 3}
      propertyPath: m_LocalRotation.x
      value: 0.1339837
      objectReference: {fileID: 0}
    - target: {fileID: 1030975303, guid: abc00000000006504693431563510938, type: 3}
      propertyPath: m_LocalRotation.y
      value: 0.00396356
      objectReference: {fileID: 0}
    - target: {fileID: 1030975303, guid: abc00000000006504693431563510938, type: 3}
      propertyPath: m_LocalRotation.z
      value: 0.052273847
      objectReference: {fileID: 0}
    - target: {fileID: 1030975303, guid: abc00000000006504693431563510938, type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 15.353
      objectReference: {fileID: 0}
    - target: {fileID: 1030975303, guid: abc00000000006504693431563510938, type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 1.298
      objectReference: {fileID: 0}
    - target: {fileID: 1030975303, guid: abc00000000006504693431563510938, type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 6.223
      objectReference: {fileID: 0}
    m_RemovedComponents: []
    m_RemovedGameObjects: []
    m_AddedGameObjects: []
    m_AddedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: abc00000000006504693431563510938, type: 3}
--- !u!4 &1515367261 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: 1030975295, guid: abc00000000006504693431563510938, type: 3}
  m_PrefabInstance: {fileID: 1515367260}
  m_PrefabAsset: {fileID: 0}
--- !u!1001 &1527419934
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    serializedVersion: 3
    m_TransformParent: {fileID: 1133823111}
    m_Modifications:
    - target: {fileID: 1031089967, guid: abc00000000006644472492457904264, type: 3}
      propertyPath: m_Name
      value: SM_Satellite_LOD1_89
      objectReference: {fileID: 0}
    - target: {fileID: 1031089968, guid: abc00000000006644472492457904264, type: 3}
      propertyPath: m_LocalScale.x
      value: 1.6000001
      objectReference: {fileID: 0}
    - target: {fileID: 1031089968, guid: abc00000000006644472492457904264, type: 3}
      propertyPath: m_LocalScale.y
      value: 1.6
      objectReference: {fileID: 0}
    - target: {fileID: 1031089968, guid: abc00000000006644472492457904264, type: 3}
      propertyPath: m_LocalScale.z
      value: 1.6000001
      objectReference: {fileID: 0}
    - target: {fileID: 1031089968, guid: abc00000000006644472492457904264, type: 3}
      propertyPath: m_LocalPosition.x
      value: 5.8599854
      objectReference: {fileID: 0}
    - target: {fileID: 1031089968, guid: abc00000000006644472492457904264, type: 3}
      propertyPath: m_LocalPosition.y
      value: 5.1000133
      objectReference: {fileID: 0}
    - target: {fileID: 1031089968, guid: abc00000000006644472492457904264, type: 3}
      propertyPath: m_LocalPosition.z
      value: -1.6659851
      objectReference: {fileID: 0}
    - target: {fileID: 1031089968, guid: abc00000000006644472492457904264, type: 3}
      propertyPath: m_LocalRotation.w
      value: 0.7242571
      objectReference: {fileID: 0}
    - target: {fileID: 1031089968, guid: abc00000000006644472492457904264, type: 3}
      propertyPath: m_LocalRotation.x
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 1031089968, guid: abc00000000006644472492457904264, type: 3}
      propertyPath: m_LocalRotation.y
      value: 0.6895301
      objectReference: {fileID: 0}
    - target: {fileID: 1031089968, guid: abc00000000006644472492457904264, type: 3}
      propertyPath: m_LocalRotation.z
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 1031089968, guid: abc00000000006644472492457904264, type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1031089968, guid: abc00000000006644472492457904264, type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 87.186
      objectReference: {fileID: 0}
    - target: {fileID: 1031089968, guid: abc00000000006644472492457904264, type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1031089968, guid: abc00000000006644472492457904264, type: 3}
      propertyPath: m_ConstrainProportionsScale
      value: 1
      objectReference: {fileID: 0}
    m_RemovedComponents: []
    m_RemovedGameObjects: []
    m_AddedGameObjects: []
    m_AddedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: abc00000000006644472492457904264, type: 3}
--- !u!4 &1527419935 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: 1031089968, guid: abc00000000006644472492457904264, type: 3}
  m_PrefabInstance: {fileID: 1527419934}
  m_PrefabAsset: {fileID: 0}
--- !u!134 &1554665989
PhysicsMaterial:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: ' (Instance)'
  serializedVersion: 2
  m_DynamicFriction: 0.6
  m_StaticFriction: 0.6
  m_Bounciness: 0
  m_FrictionCombine: 0
  m_BounceCombine: 0
--- !u!1001 &1558815745
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    serializedVersion: 3
    m_TransformParent: {fileID: 519976997}
    m_Modifications:
    - target: {fileID: 1031089831, guid: abc00000000000152043680928551015, type: 3}
      propertyPath: m_Name
      value: SM_MERGED_Barrels01_43
      objectReference: {fileID: 0}
    - target: {fileID: 1031089832, guid: abc00000000000152043680928551015, type: 3}
      propertyPath: m_LocalScale.x
      value: 2.3
      objectReference: {fileID: 0}
    - target: {fileID: 1031089832, guid: abc00000000000152043680928551015, type: 3}
      propertyPath: m_LocalScale.y
      value: 2.3
      objectReference: {fileID: 0}
    - target: {fileID: 1031089832, guid: abc00000000000152043680928551015, type: 3}
      propertyPath: m_LocalScale.z
      value: 2.3
      objectReference: {fileID: 0}
    - target: {fileID: 1031089832, guid: abc00000000000152043680928551015, type: 3}
      propertyPath: m_LocalPosition.x
      value: 2.2700195
      objectReference: {fileID: 0}
    - target: {fileID: 1031089832, guid: abc00000000000152043680928551015, type: 3}
      propertyPath: m_LocalPosition.y
      value: -3.3099997
      objectReference: {fileID: 0}
    - target: {fileID: 1031089832, guid: abc00000000000152043680928551015, type: 3}
      propertyPath: m_LocalPosition.z
      value: 7.1600037
      objectReference: {fileID: 0}
    - target: {fileID: 1031089832, guid: abc00000000000152043680928551015, type: 3}
      propertyPath: m_LocalRotation.w
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 1031089832, guid: abc00000000000152043680928551015, type: 3}
      propertyPath: m_LocalRotation.x
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 1031089832, guid: abc00000000000152043680928551015, type: 3}
      propertyPath: m_LocalRotation.y
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 1031089832, guid: abc00000000000152043680928551015, type: 3}
      propertyPath: m_LocalRotation.z
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 1031089832, guid: abc00000000000152043680928551015, type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1031089832, guid: abc00000000000152043680928551015, type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1031089832, guid: abc00000000000152043680928551015, type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1031089832, guid: abc00000000000152043680928551015, type: 3}
      propertyPath: m_ConstrainProportionsScale
      value: 1
      objectReference: {fileID: 0}
    m_RemovedComponents: []
    m_RemovedGameObjects: []
    m_AddedGameObjects: []
    m_AddedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: abc00000000000152043680928551015, type: 3}
--- !u!4 &1558815746 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: 1031089832, guid: abc00000000000152043680928551015, type: 3}
  m_PrefabInstance: {fileID: 1558815745}
  m_PrefabAsset: {fileID: 0}
--- !u!1 &1558895503
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1558895504}
  - component: {fileID: 1558895506}
  - component: {fileID: 1558895505}
  m_Layer: 5
  m_Name: SoundFXVolumeSettingLabelMin
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &1558895504
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1558895503}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1658130456}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0.5, y: 0.5}
  m_AnchorMax: {x: 0.5, y: 0.5}
  m_AnchoredPosition: {x: -153.9, y: 0}
  m_SizeDelta: {x: 100, y: 100}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &1558895505
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1558895503}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f4688fdb7df04437aeb418b961361dc5, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_text: MUTE
  m_isRightToLeft: 0
  m_fontAsset: {fileID: 11400000, guid: 8f586378b4e144a9851e7b34d9b748ee, type: 2}
  m_sharedMaterial: {fileID: 2180264, guid: 8f586378b4e144a9851e7b34d9b748ee, type: 2}
  m_fontSharedMaterials: []
  m_fontMaterial: {fileID: 0}
  m_fontMaterials: []
  m_fontColor32:
    serializedVersion: 2
    rgba: 4294967295
  m_fontColor: {r: 1, g: 1, b: 1, a: 1}
  m_enableVertexGradient: 0
  m_colorMode: 3
  m_fontColorGradient:
    topLeft: {r: 1, g: 1, b: 1, a: 1}
    topRight: {r: 1, g: 1, b: 1, a: 1}
    bottomLeft: {r: 1, g: 1, b: 1, a: 1}
    bottomRight: {r: 1, g: 1, b: 1, a: 1}
  m_fontColorGradientPreset: {fileID: 0}
  m_spriteAsset: {fileID: 0}
  m_tintAllSprites: 0
  m_StyleSheet: {fileID: 0}
  m_TextStyleHashCode: -1183493901
  m_overrideHtmlColors: 0
  m_faceColor:
    serializedVersion: 2
    rgba: 4294967295
  m_fontSize: 20
  m_fontSizeBase: 20
  m_fontWeight: 400
  m_enableAutoSizing: 0
  m_fontSizeMin: 18
  m_fontSizeMax: 72
  m_fontStyle: 0
  m_HorizontalAlignment: 2
  m_VerticalAlignment: 512
  m_textAlignment: 65535
  m_characterSpacing: 0
  m_wordSpacing: 0
  m_lineSpacing: 0
  m_lineSpacingMax: 0
  m_paragraphSpacing: 0
  m_charWidthMaxAdj: 0
  m_TextWrappingMode: 0
  m_wordWrappingRatios: 0.4
  m_overflowMode: 0
  m_linkedTextComponent: {fileID: 0}
  parentLinkedComponent: {fileID: 0}
  m_enableKerning: 0
  m_ActiveFontFeatures: 6e72656b
  m_enableExtraPadding: 0
  checkPaddingRequired: 0
  m_isRichText: 1
  m_EmojiFallbackSupport: 1
  m_parseCtrlCharacters: 1
  m_isOrthographic: 1
  m_isCullingEnabled: 0
  m_horizontalMapping: 0
  m_verticalMapping: 0
  m_uvLineOffset: 0
  m_geometrySortingOrder: 0
  m_IsTextObjectScaleStatic: 0
  m_VertexBufferAutoSizeReduction: 0
  m_useMaxVisibleDescender: 1
  m_pageToDisplay: 1
  m_margin: {x: 0, y: 0, z: 0, w: 0}
  m_isUsingLegacyAnimationComponent: 0
  m_isVolumetricText: 0
  m_hasFontAssetChanged: 0
  m_baseMaterial: {fileID: 0}
  m_maskOffset: {x: 0, y: 0, z: 0, w: 0}
--- !u!222 &1558895506
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1558895503}
  m_CullTransparentMesh: 1
--- !u!1 &1564596057
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1564596058}
  - component: {fileID: 1564596060}
  - component: {fileID: 1564596059}
  m_Layer: 5
  m_Name: AmbienceVolumeSettingLabelMax
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &1564596058
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1564596057}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 954295166}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0.5, y: 0.5}
  m_AnchorMax: {x: 0.5, y: 0.5}
  m_AnchoredPosition: {x: 148.3, y: 0}
  m_SizeDelta: {x: 100, y: 100}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &1564596059
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1564596057}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f4688fdb7df04437aeb418b961361dc5, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_text: MAX
  m_isRightToLeft: 0
  m_fontAsset: {fileID: 11400000, guid: 8f586378b4e144a9851e7b34d9b748ee, type: 2}
  m_sharedMaterial: {fileID: 2180264, guid: 8f586378b4e144a9851e7b34d9b748ee, type: 2}
  m_fontSharedMaterials: []
  m_fontMaterial: {fileID: 0}
  m_fontMaterials: []
  m_fontColor32:
    serializedVersion: 2
    rgba: 4294967295
  m_fontColor: {r: 1, g: 1, b: 1, a: 1}
  m_enableVertexGradient: 0
  m_colorMode: 3
  m_fontColorGradient:
    topLeft: {r: 1, g: 1, b: 1, a: 1}
    topRight: {r: 1, g: 1, b: 1, a: 1}
    bottomLeft: {r: 1, g: 1, b: 1, a: 1}
    bottomRight: {r: 1, g: 1, b: 1, a: 1}
  m_fontColorGradientPreset: {fileID: 0}
  m_spriteAsset: {fileID: 0}
  m_tintAllSprites: 0
  m_StyleSheet: {fileID: 0}
  m_TextStyleHashCode: -1183493901
  m_overrideHtmlColors: 0
  m_faceColor:
    serializedVersion: 2
    rgba: 4294967295
  m_fontSize: 20
  m_fontSizeBase: 20
  m_fontWeight: 400
  m_enableAutoSizing: 0
  m_fontSizeMin: 18
  m_fontSizeMax: 72
  m_fontStyle: 0
  m_HorizontalAlignment: 2
  m_VerticalAlignment: 512
  m_textAlignment: 65535
  m_characterSpacing: 0
  m_wordSpacing: 0
  m_lineSpacing: 0
  m_lineSpacingMax: 0
  m_paragraphSpacing: 0
  m_charWidthMaxAdj: 0
  m_TextWrappingMode: 0
  m_wordWrappingRatios: 0.4
  m_overflowMode: 0
  m_linkedTextComponent: {fileID: 0}
  parentLinkedComponent: {fileID: 0}
  m_enableKerning: 0
  m_ActiveFontFeatures: 6e72656b
  m_enableExtraPadding: 0
  checkPaddingRequired: 0
  m_isRichText: 1
  m_EmojiFallbackSupport: 1
  m_parseCtrlCharacters: 1
  m_isOrthographic: 1
  m_isCullingEnabled: 0
  m_horizontalMapping: 0
  m_verticalMapping: 0
  m_uvLineOffset: 0
  m_geometrySortingOrder: 0
  m_IsTextObjectScaleStatic: 0
  m_VertexBufferAutoSizeReduction: 0
  m_useMaxVisibleDescender: 1
  m_pageToDisplay: 1
  m_margin: {x: 0, y: 0, z: 0, w: 0}
  m_isUsingLegacyAnimationComponent: 0
  m_isVolumetricText: 0
  m_hasFontAssetChanged: 0
  m_baseMaterial: {fileID: 0}
  m_maskOffset: {x: 0, y: 0, z: 0, w: 0}
--- !u!222 &1564596060
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1564596057}
  m_CullTransparentMesh: 1
--- !u!134 &1594040693
PhysicsMaterial:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: 
  serializedVersion: 2
  m_DynamicFriction: 0.6
  m_StaticFriction: 0.6
  m_Bounciness: 0
  m_FrictionCombine: 0
  m_BounceCombine: 0
--- !u!1 &1610641477
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1610641478}
  - component: {fileID: 1610641480}
  - component: {fileID: 1610641479}
  m_Layer: 5
  m_Name: Background
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &1610641478
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1610641477}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 2120279515}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0.25}
  m_AnchorMax: {x: 1, y: 0.75}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 0, y: 0}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &1610641479
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1610641477}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fe87c0e1cc204ed48ad3b37840f39efc, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_Sprite: {fileID: 10907, guid: 0000000000000000f000000000000000, type: 0}
  m_Type: 1
  m_PreserveAspect: 0
  m_FillCenter: 1
  m_FillMethod: 4
  m_FillAmount: 1
  m_FillClockwise: 1
  m_FillOrigin: 0
  m_UseSpriteMesh: 0
  m_PixelsPerUnitMultiplier: 1
--- !u!222 &1610641480
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1610641477}
  m_CullTransparentMesh: 1
--- !u!1 &1625213548
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1625213552}
  - component: {fileID: 1625213551}
  - component: {fileID: 1625213550}
  - component: {fileID: 1625213549}
  m_Layer: 0
  m_Name: Terrain
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 2147483647
  m_IsActive: 1
--- !u!114 &1625213549
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1625213548}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 6af2d410bf489d842a543a60d605af5f, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  terrains:
  - {fileID: 1625213551}
  splatmapResolution: 512
  colorMapResolution: 512
  layerSettings:
  - enabled: 0
    layer: {fileID: 8574412962073106934, guid: c8b4c7645a8f5414d8713e113638485e, type: 2}
    modifierStack:
    - {fileID: 1267524449}
  - enabled: 1
    layer: {fileID: 8574412962073106934, guid: 0767c40ecd354594f95bc8204f5ecb3f, type: 2}
    modifierStack: []
  - enabled: 1
    layer: {fileID: 8574412962073106934, guid: 3d0ad62f73bf490488bef6a131aa8e5f, type: 2}
    modifierStack:
    - {fileID: 1153949932}
  - enabled: 1
    layer: {fileID: 8574412962073106934, guid: 69debc15b685aef4aa7bb2783ddd8a2b, type: 2}
    modifierStack:
    - {fileID: 270152455}
  autoRepaint: 0
  terrainListeners: []
  bounds:
    m_Center: {x: 499.99997, y: 302, z: 500}
    m_Extent: {x: 500, y: 300, z: 500}
  filterShader: {fileID: 4800000, guid: 0f44df78e3d64d9cb2616cac3d84cb77, type: 3}
--- !u!154 &1625213550
TerrainCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1625213548}
  m_Material: {fileID: 1554665989}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 4294967295
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_ProvidesContacts: 1
  m_Enabled: 1
  serializedVersion: 2
  m_TerrainData: {fileID: 15600000, guid: 004dfae9f8aac3d49b5320192f764ad3, type: 2}
  m_EnableTreeColliders: 1
--- !u!218 &1625213551
Terrain:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1625213548}
  m_Enabled: 1
  serializedVersion: 6
  m_TerrainData: {fileID: 15600000, guid: 004dfae9f8aac3d49b5320192f764ad3, type: 2}
  m_TreeDistance: 5000
  m_TreeBillboardDistance: 50
  m_TreeCrossFadeLength: 5
  m_TreeMaximumFullLODCount: 50
  m_DetailObjectDistance: 80
  m_DetailObjectDensity: 1
  m_HeightmapPixelError: 5
  m_SplatMapDistance: 1000
  m_HeightmapMinimumLODSimplification: 0
  m_HeightmapMaximumLOD: 0
  m_ShadowCastingMode: 2
  m_DrawHeightmap: 1
  m_DrawInstanced: 0
  m_DrawTreesAndFoliage: 1
  m_StaticShadowCaster: 0
  m_IgnoreQualitySettings: 0
  m_ReflectionProbeUsage: 1
  m_MaterialTemplate: {fileID: 2100000, guid: 22ff8771d87ef27429e670136399094b, type: 2}
  m_BakeLightProbesForTrees: 1
  m_PreserveTreePrototypeLayers: 0
  m_DeringLightProbesForTrees: 1
  m_ReceiveGI: 1
  m_ScaleInLightmap: 0.0256
  m_LightmapParameters: {fileID: 15203, guid: 0000000000000000f000000000000000, type: 0}
  m_GroupingID: 0
  m_RenderingLayerMask: 1
  m_AllowAutoConnect: 1
  m_EnableHeightmapRayTracing: 1
  m_EnableTreesAndDetailsRayTracing: 0
  m_TreeMotionVectorModeOverride: 3
--- !u!4 &1625213552
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1625213548}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: -40.760002, y: -0.36999997, z: -44.48}
  m_LocalScale: {x: 0.1, y: 0.1, z: 0.1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1675261855}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1001 &1651501497
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    serializedVersion: 3
    m_TransformParent: {fileID: 708659531}
    m_Modifications:
    - target: {fileID: 1031104978, guid: abc00000000006676651557972390903, type: 3}
      propertyPath: m_Name
      value: SM_Keyboard_01a_105
      objectReference: {fileID: 0}
    - target: {fileID: 1031104979, guid: abc00000000006676651557972390903, type: 3}
      propertyPath: m_LocalScale.x
      value: 2.3
      objectReference: {fileID: 0}
    - target: {fileID: 1031104979, guid: abc00000000006676651557972390903, type: 3}
      propertyPath: m_LocalScale.y
      value: 2.3
      objectReference: {fileID: 0}
    - target: {fileID: 1031104979, guid: abc00000000006676651557972390903, type: 3}
      propertyPath: m_LocalScale.z
      value: 2.3
      objectReference: {fileID: 0}
    - target: {fileID: 1031104979, guid: abc00000000006676651557972390903, type: 3}
      propertyPath: m_LocalPosition.x
      value: -5.7890015
      objectReference: {fileID: 0}
    - target: {fileID: 1031104979, guid: abc00000000006676651557972390903, type: 3}
      propertyPath: m_LocalPosition.y
      value: 1.8399999
      objectReference: {fileID: 0}
    - target: {fileID: 1031104979, guid: abc00000000006676651557972390903, type: 3}
      propertyPath: m_LocalPosition.z
      value: 9.423981
      objectReference: {fileID: 0}
    - target: {fileID: 1031104979, guid: abc00000000006676651557972390903, type: 3}
      propertyPath: m_LocalRotation.w
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 1031104979, guid: abc00000000006676651557972390903, type: 3}
      propertyPath: m_LocalRotation.x
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 1031104979, guid: abc00000000006676651557972390903, type: 3}
      propertyPath: m_LocalRotation.y
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 1031104979, guid: abc00000000006676651557972390903, type: 3}
      propertyPath: m_LocalRotation.z
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 1031104979, guid: abc00000000006676651557972390903, type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1031104979, guid: abc00000000006676651557972390903, type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1031104979, guid: abc00000000006676651557972390903, type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1031104979, guid: abc00000000006676651557972390903, type: 3}
      propertyPath: m_ConstrainProportionsScale
      value: 1
      objectReference: {fileID: 0}
    m_RemovedComponents: []
    m_RemovedGameObjects: []
    m_AddedGameObjects: []
    m_AddedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: abc00000000006676651557972390903, type: 3}
--- !u!4 &1651501498 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: 1031104979, guid: abc00000000006676651557972390903, type: 3}
  m_PrefabInstance: {fileID: 1651501497}
  m_PrefabAsset: {fileID: 0}
--- !u!1 &1658130455
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1658130456}
  - component: {fileID: 1658130457}
  m_Layer: 5
  m_Name: SoundFXVolumeSettingSlider
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &1658130456
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1658130455}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0.0001495125}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1309550037}
  - {fileID: 1776594957}
  - {fileID: 1147994954}
  - {fileID: 1558895504}
  - {fileID: 433576128}
  m_Father: {fileID: 214815528}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 0, y: 0}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 232.7, y: 20}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &1658130457
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1658130455}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 67db9e8f0e2ae9c40bc1e2b64352a6b4, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Navigation:
    m_Mode: 3
    m_WrapAround: 0
    m_SelectOnUp: {fileID: 0}
    m_SelectOnDown: {fileID: 0}
    m_SelectOnLeft: {fileID: 0}
    m_SelectOnRight: {fileID: 0}
  m_Transition: 1
  m_Colors:
    m_NormalColor: {r: 1, g: 1, b: 1, a: 1}
    m_HighlightedColor: {r: 0.9607843, g: 0.9607843, b: 0.9607843, a: 1}
    m_PressedColor: {r: 0.78431374, g: 0.78431374, b: 0.78431374, a: 1}
    m_SelectedColor: {r: 0.9607843, g: 0.9607843, b: 0.9607843, a: 1}
    m_DisabledColor: {r: 0.78431374, g: 0.78431374, b: 0.78431374, a: 0.5019608}
    m_ColorMultiplier: 1
    m_FadeDuration: 0.1
  m_SpriteState:
    m_HighlightedSprite: {fileID: 0}
    m_PressedSprite: {fileID: 0}
    m_SelectedSprite: {fileID: 0}
    m_DisabledSprite: {fileID: 0}
  m_AnimationTriggers:
    m_NormalTrigger: Normal
    m_HighlightedTrigger: Highlighted
    m_PressedTrigger: Pressed
    m_SelectedTrigger: Selected
    m_DisabledTrigger: Disabled
  m_Interactable: 1
  m_TargetGraphic: {fileID: 555802035}
  m_FillRect: {fileID: 1675110748}
  m_HandleRect: {fileID: 555802034}
  m_Direction: 0
  m_MinValue: 0
  m_MaxValue: 1
  m_WholeNumbers: 0
  m_Value: 0
  m_OnValueChanged:
    m_PersistentCalls:
      m_Calls: []
--- !u!1 &1670769541
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1670769542}
  - component: {fileID: 1670769545}
  - component: {fileID: 1670769544}
  - component: {fileID: 1670769543}
  m_Layer: 0
  m_Name: HelicopterPilotDoorCollider
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1670769542
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1670769541}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: -0.06882112, y: 0.00021514892, z: 0.0015060005}
  m_LocalScale: {x: 0.00090205244, y: 0.04464409, z: 0.033200238}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 666247744}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!65 &1670769543
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1670769541}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 3
  m_Size: {x: 1, y: 1, z: 1}
  m_Center: {x: 0, y: 0, z: 0}
--- !u!23 &1670769544
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1670769541}
  m_Enabled: 0
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 73c176f402d2c2f4d929aa5da7585d17, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!33 &1670769545
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1670769541}
  m_Mesh: {fileID: 10202, guid: 0000000000000000e000000000000000, type: 0}
--- !u!1 &1675110747
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1675110748}
  - component: {fileID: 1675110750}
  - component: {fileID: 1675110749}
  m_Layer: 5
  m_Name: Fill
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &1675110748
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1675110747}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1776594957}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 0, y: 0}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 10, y: 0}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &1675110749
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1675110747}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fe87c0e1cc204ed48ad3b37840f39efc, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_Sprite: {fileID: 10905, guid: 0000000000000000f000000000000000, type: 0}
  m_Type: 1
  m_PreserveAspect: 0
  m_FillCenter: 1
  m_FillMethod: 4
  m_FillAmount: 1
  m_FillClockwise: 1
  m_FillOrigin: 0
  m_UseSpriteMesh: 0
  m_PixelsPerUnitMultiplier: 1
--- !u!222 &1675110750
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1675110747}
  m_CullTransparentMesh: 1
--- !u!1 &1675261854
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1675261855}
  m_Layer: 0
  m_Name: Map
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1675261855
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1675261854}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 407.6, y: 5.7, z: 444.8}
  m_LocalScale: {x: 10, y: 10, z: 10}
  m_ConstrainProportionsScale: 1
  m_Children:
  - {fileID: 1990884367}
  - {fileID: 371304057}
  - {fileID: 1508310612}
  - {fileID: 2133852682}
  - {fileID: 1496791938}
  - {fileID: 1515367261}
  - {fileID: 872838983}
  - {fileID: 1037443437}
  - {fileID: 1625213552}
  - {fileID: 1260272815}
  - {fileID: 244853904}
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1703431730
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1703431731}
  m_Layer: 5
  m_Name: Fill Area
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &1703431731
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1703431730}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1822947782}
  m_Father: {fileID: 954295166}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0.25}
  m_AnchorMax: {x: 1, y: 0.75}
  m_AnchoredPosition: {x: -4.9999847, y: 0}
  m_SizeDelta: {x: -20, y: 0}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!1001 &1705384075
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    serializedVersion: 3
    m_TransformParent: {fileID: 708659531}
    m_Modifications:
    - target: {fileID: 1031069284, guid: abc00000000017184404928698871754, type: 3}
      propertyPath: m_Name
      value: SM_Helmet_1034
      objectReference: {fileID: 0}
    - target: {fileID: 1031069285, guid: abc00000000017184404928698871754, type: 3}
      propertyPath: m_LocalScale.x
      value: 2.3
      objectReference: {fileID: 0}
    - target: {fileID: 1031069285, guid: abc00000000017184404928698871754, type: 3}
      propertyPath: m_LocalScale.y
      value: 2.3
      objectReference: {fileID: 0}
    - target: {fileID: 1031069285, guid: abc00000000017184404928698871754, type: 3}
      propertyPath: m_LocalScale.z
      value: 2.3
      objectReference: {fileID: 0}
    - target: {fileID: 1031069285, guid: abc00000000017184404928698871754, type: 3}
      propertyPath: m_LocalPosition.x
      value: -7.5250244
      objectReference: {fileID: 0}
    - target: {fileID: 1031069285, guid: abc00000000017184404928698871754, type: 3}
      propertyPath: m_LocalPosition.y
      value: 1.8429999
      objectReference: {fileID: 0}
    - target: {fileID: 1031069285, guid: abc00000000017184404928698871754, type: 3}
      propertyPath: m_LocalPosition.z
      value: 10.106995
      objectReference: {fileID: 0}
    - target: {fileID: 1031069285, guid: abc00000000017184404928698871754, type: 3}
      propertyPath: m_LocalRotation.w
      value: 0.98007107
      objectReference: {fileID: 0}
    - target: {fileID: 1031069285, guid: abc00000000017184404928698871754, type: 3}
      propertyPath: m_LocalRotation.x
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 1031069285, guid: abc00000000017184404928698871754, type: 3}
      propertyPath: m_LocalRotation.y
      value: -0.19864766
      objectReference: {fileID: 0}
    - target: {fileID: 1031069285, guid: abc00000000017184404928698871754, type: 3}
      propertyPath: m_LocalRotation.z
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 1031069285, guid: abc00000000017184404928698871754, type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1031069285, guid: abc00000000017184404928698871754, type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: -22.916
      objectReference: {fileID: 0}
    - target: {fileID: 1031069285, guid: abc00000000017184404928698871754, type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1031069285, guid: abc00000000017184404928698871754, type: 3}
      propertyPath: m_ConstrainProportionsScale
      value: 1
      objectReference: {fileID: 0}
    m_RemovedComponents: []
    m_RemovedGameObjects: []
    m_AddedGameObjects: []
    m_AddedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: abc00000000017184404928698871754, type: 3}
--- !u!4 &1705384076 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: 1031069285, guid: abc00000000017184404928698871754, type: 3}
  m_PrefabInstance: {fileID: 1705384075}
  m_PrefabAsset: {fileID: 0}
--- !u!1 &1720069234
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1720069235}
  m_Layer: 5
  m_Name: AmbienceVolumeSetting
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &1720069235
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1720069234}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0.00028129626}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1275455219}
  m_Father: {fileID: 1341548309}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 0, y: 0}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 0, y: 80.666664}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!134 &1733125366
PhysicsMaterial:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: 
  serializedVersion: 2
  m_DynamicFriction: 0.6
  m_StaticFriction: 0.6
  m_Bounciness: 0
  m_FrictionCombine: 0
  m_BounceCombine: 0
--- !u!1 &1769772628
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1769772629}
  m_Layer: 0
  m_Name: ComputerSetup
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1769772629
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1769772628}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: 1, z: -0, w: 0}
  m_LocalPosition: {x: 3.8049927, y: 1.8710134, z: -0.50601196}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 181539675}
  - {fileID: 207742601}
  - {fileID: 344447161}
  - {fileID: 1073820112}
  m_Father: {fileID: 1133823111}
  m_LocalEulerAnglesHint: {x: 0, y: 180, z: 0}
--- !u!1 &1776594956
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1776594957}
  m_Layer: 5
  m_Name: Fill Area
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &1776594957
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1776594956}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1675110748}
  m_Father: {fileID: 1658130456}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0.25}
  m_AnchorMax: {x: 1, y: 0.75}
  m_AnchoredPosition: {x: -4.9999847, y: 0}
  m_SizeDelta: {x: -20, y: 0}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!1 &1801178229
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1801178230}
  - component: {fileID: 1801178232}
  - component: {fileID: 1801178231}
  m_Layer: 5
  m_Name: AmbienceVolumeSettingLabel
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &1801178230
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1801178229}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0.0000939624}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1275455219}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 0, y: 0}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 288.36, y: 50}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &1801178231
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1801178229}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f4688fdb7df04437aeb418b961361dc5, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_text: Ambience Volume
  m_isRightToLeft: 0
  m_fontAsset: {fileID: 11400000, guid: 8f586378b4e144a9851e7b34d9b748ee, type: 2}
  m_sharedMaterial: {fileID: 2180264, guid: 8f586378b4e144a9851e7b34d9b748ee, type: 2}
  m_fontSharedMaterials: []
  m_fontMaterial: {fileID: 0}
  m_fontMaterials: []
  m_fontColor32:
    serializedVersion: 2
    rgba: 4294967295
  m_fontColor: {r: 1, g: 1, b: 1, a: 1}
  m_enableVertexGradient: 0
  m_colorMode: 3
  m_fontColorGradient:
    topLeft: {r: 1, g: 1, b: 1, a: 1}
    topRight: {r: 1, g: 1, b: 1, a: 1}
    bottomLeft: {r: 1, g: 1, b: 1, a: 1}
    bottomRight: {r: 1, g: 1, b: 1, a: 1}
  m_fontColorGradientPreset: {fileID: 0}
  m_spriteAsset: {fileID: 0}
  m_tintAllSprites: 0
  m_StyleSheet: {fileID: 0}
  m_TextStyleHashCode: -1183493901
  m_overrideHtmlColors: 0
  m_faceColor:
    serializedVersion: 2
    rgba: 4294967295
  m_fontSize: 33.6
  m_fontSizeBase: 33.6
  m_fontWeight: 400
  m_enableAutoSizing: 0
  m_fontSizeMin: 18
  m_fontSizeMax: 72
  m_fontStyle: 0
  m_HorizontalAlignment: 2
  m_VerticalAlignment: 512
  m_textAlignment: 65535
  m_characterSpacing: 0
  m_wordSpacing: 0
  m_lineSpacing: 0
  m_lineSpacingMax: 0
  m_paragraphSpacing: 0
  m_charWidthMaxAdj: 0
  m_TextWrappingMode: 0
  m_wordWrappingRatios: 0.4
  m_overflowMode: 0
  m_linkedTextComponent: {fileID: 0}
  parentLinkedComponent: {fileID: 0}
  m_enableKerning: 0
  m_ActiveFontFeatures: 6e72656b
  m_enableExtraPadding: 0
  checkPaddingRequired: 0
  m_isRichText: 1
  m_EmojiFallbackSupport: 1
  m_parseCtrlCharacters: 1
  m_isOrthographic: 1
  m_isCullingEnabled: 0
  m_horizontalMapping: 0
  m_verticalMapping: 0
  m_uvLineOffset: 0
  m_geometrySortingOrder: 0
  m_IsTextObjectScaleStatic: 0
  m_VertexBufferAutoSizeReduction: 0
  m_useMaxVisibleDescender: 1
  m_pageToDisplay: 1
  m_margin: {x: 0, y: 0, z: 0, w: 0}
  m_isUsingLegacyAnimationComponent: 0
  m_isVolumetricText: 0
  m_hasFontAssetChanged: 0
  m_baseMaterial: {fileID: 0}
  m_maskOffset: {x: 0, y: 0, z: 0, w: 0}
--- !u!222 &1801178232
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1801178229}
  m_CullTransparentMesh: 1
--- !u!1 &1812692991
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1812692992}
  m_Layer: 0
  m_Name: HelicopterWaypoints
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1812692992
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1812692991}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 460.39, y: 7.116, z: 366.1}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 982418701}
  - {fileID: 369533476}
  - {fileID: 331546771}
  - {fileID: 1371030940}
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1822947781
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1822947782}
  - component: {fileID: 1822947784}
  - component: {fileID: 1822947783}
  m_Layer: 5
  m_Name: Fill
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &1822947782
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1822947781}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1703431731}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 0, y: 0}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 10, y: 0}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &1822947783
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1822947781}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fe87c0e1cc204ed48ad3b37840f39efc, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_Sprite: {fileID: 10905, guid: 0000000000000000f000000000000000, type: 0}
  m_Type: 1
  m_PreserveAspect: 0
  m_FillCenter: 1
  m_FillMethod: 4
  m_FillAmount: 1
  m_FillClockwise: 1
  m_FillOrigin: 0
  m_UseSpriteMesh: 0
  m_PixelsPerUnitMultiplier: 1
--- !u!222 &1822947784
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1822947781}
  m_CullTransparentMesh: 1
--- !u!1001 &1878862095
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    serializedVersion: 3
    m_TransformParent: {fileID: 1133823111}
    m_Modifications:
    - target: {fileID: 1031089282, guid: abc00000000009121737448075960786, type: 3}
      propertyPath: m_Name
      value: SM_Generator_Open_332
      objectReference: {fileID: 0}
    - target: {fileID: 1031089283, guid: abc00000000009121737448075960786, type: 3}
      propertyPath: m_LocalScale.x
      value: 2.3
      objectReference: {fileID: 0}
    - target: {fileID: 1031089283, guid: abc00000000009121737448075960786, type: 3}
      propertyPath: m_LocalScale.y
      value: 2.3
      objectReference: {fileID: 0}
    - target: {fileID: 1031089283, guid: abc00000000009121737448075960786, type: 3}
      propertyPath: m_LocalScale.z
      value: 2.3
      objectReference: {fileID: 0}
    - target: {fileID: 1031089283, guid: abc00000000009121737448075960786, type: 3}
      propertyPath: m_LocalPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1031089283, guid: abc00000000009121737448075960786, type: 3}
      propertyPath: m_LocalPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1031089283, guid: abc00000000009121737448075960786, type: 3}
      propertyPath: m_LocalPosition.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1031089283, guid: abc00000000009121737448075960786, type: 3}
      propertyPath: m_LocalRotation.w
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 1031089283, guid: abc00000000009121737448075960786, type: 3}
      propertyPath: m_LocalRotation.x
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 1031089283, guid: abc00000000009121737448075960786, type: 3}
      propertyPath: m_LocalRotation.y
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 1031089283, guid: abc00000000009121737448075960786, type: 3}
      propertyPath: m_LocalRotation.z
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 1031089283, guid: abc00000000009121737448075960786, type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1031089283, guid: abc00000000009121737448075960786, type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1031089283, guid: abc00000000009121737448075960786, type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1031089283, guid: abc00000000009121737448075960786, type: 3}
      propertyPath: m_ConstrainProportionsScale
      value: 1
      objectReference: {fileID: 0}
    m_RemovedComponents: []
    m_RemovedGameObjects: []
    m_AddedGameObjects: []
    m_AddedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: abc00000000009121737448075960786, type: 3}
--- !u!4 &1878862096 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: 1031089283, guid: abc00000000009121737448075960786, type: 3}
  m_PrefabInstance: {fileID: 1878862095}
  m_PrefabAsset: {fileID: 0}
--- !u!1 &1879765800
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1879765801}
  - component: {fileID: 1879765803}
  - component: {fileID: 1879765802}
  m_Layer: 5
  m_Name: Handle
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &1879765801
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1879765800}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1355259850}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 0, y: 0}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 20, y: 0}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &1879765802
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1879765800}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fe87c0e1cc204ed48ad3b37840f39efc, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_Sprite: {fileID: 10913, guid: 0000000000000000f000000000000000, type: 0}
  m_Type: 0
  m_PreserveAspect: 0
  m_FillCenter: 1
  m_FillMethod: 4
  m_FillAmount: 1
  m_FillClockwise: 1
  m_FillOrigin: 0
  m_UseSpriteMesh: 0
  m_PixelsPerUnitMultiplier: 1
--- !u!222 &1879765803
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1879765800}
  m_CullTransparentMesh: 1
--- !u!1001 &1887566592
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    serializedVersion: 3
    m_TransformParent: {fileID: 708659531}
    m_Modifications:
    - target: {fileID: 4349889308364899038, guid: 6e55e6f93c1f7994a921018c2285a659, type: 3}
      propertyPath: m_LocalScale.x
      value: 2.3266804
      objectReference: {fileID: 0}
    - target: {fileID: 4349889308364899038, guid: 6e55e6f93c1f7994a921018c2285a659, type: 3}
      propertyPath: m_LocalScale.z
      value: 2.3266804
      objectReference: {fileID: 0}
    - target: {fileID: 4349889308364899038, guid: 6e55e6f93c1f7994a921018c2285a659, type: 3}
      propertyPath: m_LocalPosition.x
      value: 0.28997803
      objectReference: {fileID: 0}
    - target: {fileID: 4349889308364899038, guid: 6e55e6f93c1f7994a921018c2285a659, type: 3}
      propertyPath: m_LocalPosition.y
      value: 0.022485495
      objectReference: {fileID: 0}
    - target: {fileID: 4349889308364899038, guid: 6e55e6f93c1f7994a921018c2285a659, type: 3}
      propertyPath: m_LocalPosition.z
      value: -1.8399963
      objectReference: {fileID: 0}
    - target: {fileID: 4349889308364899038, guid: 6e55e6f93c1f7994a921018c2285a659, type: 3}
      propertyPath: m_LocalRotation.w
      value: 0.680085
      objectReference: {fileID: 0}
    - target: {fileID: 4349889308364899038, guid: 6e55e6f93c1f7994a921018c2285a659, type: 3}
      propertyPath: m_LocalRotation.x
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 4349889308364899038, guid: 6e55e6f93c1f7994a921018c2285a659, type: 3}
      propertyPath: m_LocalRotation.y
      value: 0.7331334
      objectReference: {fileID: 0}
    - target: {fileID: 4349889308364899038, guid: 6e55e6f93c1f7994a921018c2285a659, type: 3}
      propertyPath: m_LocalRotation.z
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 4349889308364899038, guid: 6e55e6f93c1f7994a921018c2285a659, type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4349889308364899038, guid: 6e55e6f93c1f7994a921018c2285a659, type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 94.299
      objectReference: {fileID: 0}
    - target: {fileID: 4349889308364899038, guid: 6e55e6f93c1f7994a921018c2285a659, type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8982426684407628973, guid: 6e55e6f93c1f7994a921018c2285a659, type: 3}
      propertyPath: m_Name
      value: FoldableChairPrefab_1
      objectReference: {fileID: 0}
    m_RemovedComponents: []
    m_RemovedGameObjects: []
    m_AddedGameObjects: []
    m_AddedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: 6e55e6f93c1f7994a921018c2285a659, type: 3}
--- !u!4 &1887566593 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: 4349889308364899038, guid: 6e55e6f93c1f7994a921018c2285a659, type: 3}
  m_PrefabInstance: {fileID: 1887566592}
  m_PrefabAsset: {fileID: 0}
--- !u!134 &1892681617
PhysicsMaterial:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: 
  serializedVersion: 2
  m_DynamicFriction: 0.6
  m_StaticFriction: 0.6
  m_Bounciness: 0
  m_FrictionCombine: 0
  m_BounceCombine: 0
--- !u!1 &1914000736
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1914000737}
  - component: {fileID: 1914000738}
  m_Layer: 0
  m_Name: ForestGameManager
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1914000737
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1914000736}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 436.61496, y: 29.456612, z: 322.44885}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &1914000738
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1914000736}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: b69a322ad5e5e9d428b88e6659dedb25, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  forestIntroHelicopter: {fileID: 967290191}
  forestMapAudioManager: {fileID: 173380678}
  networkedHelicopterManager: {fileID: 2063873618}
--- !u!1 &1925205525
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1925205526}
  - component: {fileID: 1925205530}
  - component: {fileID: 1925205529}
  - component: {fileID: 1925205528}
  - component: {fileID: 1925205527}
  m_Layer: 0
  m_Name: HelicopterFloorCollider_1
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1925205526
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1925205525}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0.27246496, z: 0, w: 0.9621658}
  m_LocalPosition: {x: 0.0825, y: 0.0002, z: -0.02164}
  m_LocalScale: {x: 0.026312603, y: 0.04464409, z: 0.0018142876}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 666247744}
  m_LocalEulerAnglesHint: {x: 0, y: 31.622, z: 0}
--- !u!114 &1925205527
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1925205525}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 7a5ac11cc976e418e8d13136b07e1f52, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_SerializedVersion: 0
  m_AgentTypeID: 0
  m_CollectObjects: 0
  m_Size: {x: 10, y: 10, z: 10}
  m_Center: {x: 0, y: 2, z: 0}
  m_LayerMask:
    serializedVersion: 2
    m_Bits: 4294967295
  m_UseGeometry: 0
  m_DefaultArea: 0
  m_GenerateLinks: 1
  m_IgnoreNavMeshAgent: 1
  m_IgnoreNavMeshObstacle: 1
  m_OverrideTileSize: 0
  m_TileSize: 256
  m_OverrideVoxelSize: 0
  m_VoxelSize: 0.16666667
  m_MinRegionArea: 2
  m_NavMeshData: {fileID: 23800000, guid: f839163ebe147794fae424ac00bdf80a, type: 2}
  m_BuildHeightMesh: 0
--- !u!65 &1925205528
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1925205525}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 3
  m_Size: {x: 1, y: 1, z: 1}
  m_Center: {x: 0, y: 0, z: 0}
--- !u!23 &1925205529
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1925205525}
  m_Enabled: 0
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 73c176f402d2c2f4d929aa5da7585d17, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!33 &1925205530
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1925205525}
  m_Mesh: {fileID: 10202, guid: 0000000000000000e000000000000000, type: 0}
--- !u!1001 &1928151666
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    serializedVersion: 3
    m_TransformParent: {fileID: 1133823111}
    m_Modifications:
    - target: {fileID: 1031082705, guid: abc00000000008147925418740143309, type: 3}
      propertyPath: m_Name
      value: SM_TacticalRadio2_127
      objectReference: {fileID: 0}
    - target: {fileID: 1031082706, guid: abc00000000008147925418740143309, type: 3}
      propertyPath: m_LocalScale.x
      value: 2.3000002
      objectReference: {fileID: 0}
    - target: {fileID: 1031082706, guid: abc00000000008147925418740143309, type: 3}
      propertyPath: m_LocalScale.y
      value: 2.3
      objectReference: {fileID: 0}
    - target: {fileID: 1031082706, guid: abc00000000008147925418740143309, type: 3}
      propertyPath: m_LocalScale.z
      value: 2.3000002
      objectReference: {fileID: 0}
    - target: {fileID: 1031082706, guid: abc00000000008147925418740143309, type: 3}
      propertyPath: m_LocalPosition.x
      value: 6.200012
      objectReference: {fileID: 0}
    - target: {fileID: 1031082706, guid: abc00000000008147925418740143309, type: 3}
      propertyPath: m_LocalPosition.y
      value: 1.9310133
      objectReference: {fileID: 0}
    - target: {fileID: 1031082706, guid: abc00000000008147925418740143309, type: 3}
      propertyPath: m_LocalPosition.z
      value: -0.901001
      objectReference: {fileID: 0}
    - target: {fileID: 1031082706, guid: abc00000000008147925418740143309, type: 3}
      propertyPath: m_LocalRotation.w
      value: 0.31664047
      objectReference: {fileID: 0}
    - target: {fileID: 1031082706, guid: abc00000000008147925418740143309, type: 3}
      propertyPath: m_LocalRotation.x
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 1031082706, guid: abc00000000008147925418740143309, type: 3}
      propertyPath: m_LocalRotation.y
      value: 0.94854563
      objectReference: {fileID: 0}
    - target: {fileID: 1031082706, guid: abc00000000008147925418740143309, type: 3}
      propertyPath: m_LocalRotation.z
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 1031082706, guid: abc00000000008147925418740143309, type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1031082706, guid: abc00000000008147925418740143309, type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 143.08
      objectReference: {fileID: 0}
    - target: {fileID: 1031082706, guid: abc00000000008147925418740143309, type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1031082706, guid: abc00000000008147925418740143309, type: 3}
      propertyPath: m_ConstrainProportionsScale
      value: 1
      objectReference: {fileID: 0}
    m_RemovedComponents: []
    m_RemovedGameObjects: []
    m_AddedGameObjects: []
    m_AddedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: abc00000000008147925418740143309, type: 3}
--- !u!4 &1928151667 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: 1031082706, guid: abc00000000008147925418740143309, type: 3}
  m_PrefabInstance: {fileID: 1928151666}
  m_PrefabAsset: {fileID: 0}
--- !u!1001 &1990884366
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    serializedVersion: 3
    m_TransformParent: {fileID: 1675261855}
    m_Modifications:
    - target: {fileID: 1030975294, guid: abc00000000006504693431563510938, type: 3}
      propertyPath: m_Name
      value: SM_Roadblock_1
      objectReference: {fileID: 0}
    - target: {fileID: 1030975295, guid: abc00000000006504693431563510938, type: 3}
      propertyPath: m_LocalScale.x
      value: 0.14656997
      objectReference: {fileID: 0}
    - target: {fileID: 1030975295, guid: abc00000000006504693431563510938, type: 3}
      propertyPath: m_LocalScale.y
      value: 0.14657001
      objectReference: {fileID: 0}
    - target: {fileID: 1030975295, guid: abc00000000006504693431563510938, type: 3}
      propertyPath: m_LocalScale.z
      value: 0.14656997
      objectReference: {fileID: 0}
    - target: {fileID: 1030975295, guid: abc00000000006504693431563510938, type: 3}
      propertyPath: m_LocalPosition.x
      value: 8.097999
      objectReference: {fileID: 0}
    - target: {fileID: 1030975295, guid: abc00000000006504693431563510938, type: 3}
      propertyPath: m_LocalPosition.y
      value: -0.055999994
      objectReference: {fileID: 0}
    - target: {fileID: 1030975295, guid: abc00000000006504693431563510938, type: 3}
      propertyPath: m_LocalPosition.z
      value: -7.3440003
      objectReference: {fileID: 0}
    - target: {fileID: 1030975295, guid: abc00000000006504693431563510938, type: 3}
      propertyPath: m_LocalRotation.w
      value: 0.37639678
      objectReference: {fileID: 0}
    - target: {fileID: 1030975295, guid: abc00000000006504693431563510938, type: 3}
      propertyPath: m_LocalRotation.x
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 1030975295, guid: abc00000000006504693431563510938, type: 3}
      propertyPath: m_LocalRotation.y
      value: 0.9264586
      objectReference: {fileID: 0}
    - target: {fileID: 1030975295, guid: abc00000000006504693431563510938, type: 3}
      propertyPath: m_LocalRotation.z
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 1030975295, guid: abc00000000006504693431563510938, type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1030975295, guid: abc00000000006504693431563510938, type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 135.779
      objectReference: {fileID: 0}
    - target: {fileID: 1030975295, guid: abc00000000006504693431563510938, type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1030975295, guid: abc00000000006504693431563510938, type: 3}
      propertyPath: m_ConstrainProportionsScale
      value: 1
      objectReference: {fileID: 0}
    m_RemovedComponents: []
    m_RemovedGameObjects: []
    m_AddedGameObjects: []
    m_AddedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: abc00000000006504693431563510938, type: 3}
--- !u!4 &1990884367 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: 1030975295, guid: abc00000000006504693431563510938, type: 3}
  m_PrefabInstance: {fileID: 1990884366}
  m_PrefabAsset: {fileID: 0}
--- !u!1 &2002829528
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2002829529}
  m_Layer: 5
  m_Name: ForestMapContainer
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &2002829529
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2002829528}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1238260545}
  m_Father: {fileID: 189974504}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0.5, y: 0.5}
  m_AnchorMax: {x: 0.5, y: 0.5}
  m_AnchoredPosition: {x: 0, y: 96.66}
  m_SizeDelta: {x: 1920, y: 1080}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!1 &2013410473
GameObject:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2013410475}
  - component: {fileID: 2013410474}
  m_Layer: 0
  m_Name: StaticLightingSky
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!114 &2013410474
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2013410473}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 441482e8936e35048a1dffac814e3ef8, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Profile: {fileID: 11400000, guid: 8ba92e2dd7f884a0f88b98fa2d235fe7, type: 2}
  m_StaticLightingSkyUniqueID: 4
  m_StaticLightingCloudsUniqueID: 0
  m_StaticLightingVolumetricClouds: 0
  bounces: 1
--- !u!4 &2013410475
Transform:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2013410473}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &2043513898
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2043513899}
  - component: {fileID: 2043513901}
  - component: {fileID: 2043513900}
  m_Layer: 5
  m_Name: MusicVolumeSettingLabelMax
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &2043513899
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2043513898}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 2124822543}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0.5, y: 0.5}
  m_AnchorMax: {x: 0.5, y: 0.5}
  m_AnchoredPosition: {x: 148.3, y: 0}
  m_SizeDelta: {x: 100, y: 100}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &2043513900
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2043513898}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f4688fdb7df04437aeb418b961361dc5, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_text: MAX
  m_isRightToLeft: 0
  m_fontAsset: {fileID: 11400000, guid: 8f586378b4e144a9851e7b34d9b748ee, type: 2}
  m_sharedMaterial: {fileID: 2180264, guid: 8f586378b4e144a9851e7b34d9b748ee, type: 2}
  m_fontSharedMaterials: []
  m_fontMaterial: {fileID: 0}
  m_fontMaterials: []
  m_fontColor32:
    serializedVersion: 2
    rgba: 4294967295
  m_fontColor: {r: 1, g: 1, b: 1, a: 1}
  m_enableVertexGradient: 0
  m_colorMode: 3
  m_fontColorGradient:
    topLeft: {r: 1, g: 1, b: 1, a: 1}
    topRight: {r: 1, g: 1, b: 1, a: 1}
    bottomLeft: {r: 1, g: 1, b: 1, a: 1}
    bottomRight: {r: 1, g: 1, b: 1, a: 1}
  m_fontColorGradientPreset: {fileID: 0}
  m_spriteAsset: {fileID: 0}
  m_tintAllSprites: 0
  m_StyleSheet: {fileID: 0}
  m_TextStyleHashCode: -1183493901
  m_overrideHtmlColors: 0
  m_faceColor:
    serializedVersion: 2
    rgba: 4294967295
  m_fontSize: 20
  m_fontSizeBase: 20
  m_fontWeight: 400
  m_enableAutoSizing: 0
  m_fontSizeMin: 18
  m_fontSizeMax: 72
  m_fontStyle: 0
  m_HorizontalAlignment: 2
  m_VerticalAlignment: 512
  m_textAlignment: 65535
  m_characterSpacing: 0
  m_wordSpacing: 0
  m_lineSpacing: 0
  m_lineSpacingMax: 0
  m_paragraphSpacing: 0
  m_charWidthMaxAdj: 0
  m_TextWrappingMode: 0
  m_wordWrappingRatios: 0.4
  m_overflowMode: 0
  m_linkedTextComponent: {fileID: 0}
  parentLinkedComponent: {fileID: 0}
  m_enableKerning: 0
  m_ActiveFontFeatures: 6e72656b
  m_enableExtraPadding: 0
  checkPaddingRequired: 0
  m_isRichText: 1
  m_EmojiFallbackSupport: 1
  m_parseCtrlCharacters: 1
  m_isOrthographic: 1
  m_isCullingEnabled: 0
  m_horizontalMapping: 0
  m_verticalMapping: 0
  m_uvLineOffset: 0
  m_geometrySortingOrder: 0
  m_IsTextObjectScaleStatic: 0
  m_VertexBufferAutoSizeReduction: 0
  m_useMaxVisibleDescender: 1
  m_pageToDisplay: 1
  m_margin: {x: 0, y: 0, z: 0, w: 0}
  m_isUsingLegacyAnimationComponent: 0
  m_isVolumetricText: 0
  m_hasFontAssetChanged: 0
  m_baseMaterial: {fileID: 0}
  m_maskOffset: {x: 0, y: 0, z: 0, w: 0}
--- !u!222 &2043513901
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2043513898}
  m_CullTransparentMesh: 1
--- !u!1 &2063873616
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2063873617}
  - component: {fileID: 2063873618}
  - component: {fileID: 2063873619}
  m_Layer: 0
  m_Name: NetworkedHelicopterManager
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &2063873617
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2063873616}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 893.6125, y: 57.0569, z: 483.15158}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &2063873618
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2063873616}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: e296c6e9a3d718f4cb45f87a0be55ac5, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  syncDirection: 0
  syncMode: 0
  syncInterval: 0
  helicopterTransform: {fileID: 967290192}
--- !u!114 &2063873619
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2063873616}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 9b91ecbcc199f4492b9a91e820070131, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  sceneId: 316918354
  _assetId: 0
  serverOnly: 1
  visibility: 0
  hasSpawned: 0
--- !u!1 &2073337800
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2073337801}
  m_Layer: 0
  m_Name: HLetter
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &2073337801
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2073337800}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: -0.3, y: 0, z: 0.16}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 872894155}
  - {fileID: 808853747}
  - {fileID: 1309079194}
  m_Father: {fileID: 1228985242}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &2078784408
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2078784409}
  - component: {fileID: 2078784411}
  - component: {fileID: 2078784410}
  m_Layer: 5
  m_Name: Fill
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &2078784409
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2078784408}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 514748380}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 0, y: 0}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 10, y: 0}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &2078784410
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2078784408}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fe87c0e1cc204ed48ad3b37840f39efc, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_Sprite: {fileID: 10905, guid: 0000000000000000f000000000000000, type: 0}
  m_Type: 1
  m_PreserveAspect: 0
  m_FillCenter: 1
  m_FillMethod: 4
  m_FillAmount: 1
  m_FillClockwise: 1
  m_FillOrigin: 0
  m_UseSpriteMesh: 0
  m_PixelsPerUnitMultiplier: 1
--- !u!222 &2078784411
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2078784408}
  m_CullTransparentMesh: 1
--- !u!1001 &2080285412
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    serializedVersion: 3
    m_TransformParent: {fileID: 1228985242}
    m_Modifications:
    - target: {fileID: 1031059011, guid: abc00000000007020720680617858536, type: 3}
      propertyPath: m_Name
      value: SM_FFloorMiddle450x454_33
      objectReference: {fileID: 0}
    - target: {fileID: 1031059012, guid: abc00000000007020720680617858536, type: 3}
      propertyPath: m_LocalScale.x
      value: 2.5
      objectReference: {fileID: 0}
    - target: {fileID: 1031059012, guid: abc00000000007020720680617858536, type: 3}
      propertyPath: m_LocalScale.z
      value: 2.5
      objectReference: {fileID: 0}
    - target: {fileID: 1031059012, guid: abc00000000007020720680617858536, type: 3}
      propertyPath: m_LocalPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1031059012, guid: abc00000000007020720680617858536, type: 3}
      propertyPath: m_LocalPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1031059012, guid: abc00000000007020720680617858536, type: 3}
      propertyPath: m_LocalPosition.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1031059012, guid: abc00000000007020720680617858536, type: 3}
      propertyPath: m_LocalRotation.w
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 1031059012, guid: abc00000000007020720680617858536, type: 3}
      propertyPath: m_LocalRotation.x
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 1031059012, guid: abc00000000007020720680617858536, type: 3}
      propertyPath: m_LocalRotation.y
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 1031059012, guid: abc00000000007020720680617858536, type: 3}
      propertyPath: m_LocalRotation.z
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 1031059012, guid: abc00000000007020720680617858536, type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1031059012, guid: abc00000000007020720680617858536, type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1031059012, guid: abc00000000007020720680617858536, type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1031059012, guid: abc00000000007020720680617858536, type: 3}
      propertyPath: m_ConstrainProportionsScale
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1031059013, guid: abc00000000007020720680617858536, type: 3}
      propertyPath: m_Size.z
      value: 4.5
      objectReference: {fileID: 0}
    - target: {fileID: 1031059013, guid: abc00000000007020720680617858536, type: 3}
      propertyPath: m_Material
      value: 
      objectReference: {fileID: 1594040693}
    m_RemovedComponents: []
    m_RemovedGameObjects: []
    m_AddedGameObjects: []
    m_AddedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: abc00000000007020720680617858536, type: 3}
--- !u!4 &2080285413 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: 1031059012, guid: abc00000000007020720680617858536, type: 3}
  m_PrefabInstance: {fileID: 2080285412}
  m_PrefabAsset: {fileID: 0}
--- !u!1001 &2106630466
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    serializedVersion: 3
    m_TransformParent: {fileID: 708659531}
    m_Modifications:
    - target: {fileID: 4349889308364899038, guid: 6e55e6f93c1f7994a921018c2285a659, type: 3}
      propertyPath: m_LocalScale.x
      value: 2.3266804
      objectReference: {fileID: 0}
    - target: {fileID: 4349889308364899038, guid: 6e55e6f93c1f7994a921018c2285a659, type: 3}
      propertyPath: m_LocalScale.z
      value: 2.3266804
      objectReference: {fileID: 0}
    - target: {fileID: 4349889308364899038, guid: 6e55e6f93c1f7994a921018c2285a659, type: 3}
      propertyPath: m_LocalPosition.x
      value: -1.789978
      objectReference: {fileID: 0}
    - target: {fileID: 4349889308364899038, guid: 6e55e6f93c1f7994a921018c2285a659, type: 3}
      propertyPath: m_LocalPosition.y
      value: 0.022482634
      objectReference: {fileID: 0}
    - target: {fileID: 4349889308364899038, guid: 6e55e6f93c1f7994a921018c2285a659, type: 3}
      propertyPath: m_LocalPosition.z
      value: -4.7400208
      objectReference: {fileID: 0}
    - target: {fileID: 4349889308364899038, guid: 6e55e6f93c1f7994a921018c2285a659, type: 3}
      propertyPath: m_LocalRotation.w
      value: 0.08657855
      objectReference: {fileID: 0}
    - target: {fileID: 4349889308364899038, guid: 6e55e6f93c1f7994a921018c2285a659, type: 3}
      propertyPath: m_LocalRotation.x
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 4349889308364899038, guid: 6e55e6f93c1f7994a921018c2285a659, type: 3}
      propertyPath: m_LocalRotation.y
      value: 0.996245
      objectReference: {fileID: 0}
    - target: {fileID: 4349889308364899038, guid: 6e55e6f93c1f7994a921018c2285a659, type: 3}
      propertyPath: m_LocalRotation.z
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 4349889308364899038, guid: 6e55e6f93c1f7994a921018c2285a659, type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4349889308364899038, guid: 6e55e6f93c1f7994a921018c2285a659, type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 170.066
      objectReference: {fileID: 0}
    - target: {fileID: 4349889308364899038, guid: 6e55e6f93c1f7994a921018c2285a659, type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8982426684407628973, guid: 6e55e6f93c1f7994a921018c2285a659, type: 3}
      propertyPath: m_Name
      value: FoldableChairPrefab_2
      objectReference: {fileID: 0}
    m_RemovedComponents: []
    m_RemovedGameObjects: []
    m_AddedGameObjects: []
    m_AddedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: 6e55e6f93c1f7994a921018c2285a659, type: 3}
--- !u!4 &2106630467 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: 4349889308364899038, guid: 6e55e6f93c1f7994a921018c2285a659, type: 3}
  m_PrefabInstance: {fileID: 2106630466}
  m_PrefabAsset: {fileID: 0}
--- !u!1 &2112910343
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2112910344}
  - component: {fileID: 2112910346}
  - component: {fileID: 2112910345}
  m_Layer: 5
  m_Name: Handle
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &2112910344
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2112910343}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 868595561}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 0, y: 0}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 20, y: 0}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &2112910345
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2112910343}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fe87c0e1cc204ed48ad3b37840f39efc, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_Sprite: {fileID: 10913, guid: 0000000000000000f000000000000000, type: 0}
  m_Type: 0
  m_PreserveAspect: 0
  m_FillCenter: 1
  m_FillMethod: 4
  m_FillAmount: 1
  m_FillClockwise: 1
  m_FillOrigin: 0
  m_UseSpriteMesh: 0
  m_PixelsPerUnitMultiplier: 1
--- !u!222 &2112910346
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2112910343}
  m_CullTransparentMesh: 1
--- !u!1 &2120279514
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2120279515}
  - component: {fileID: 2120279516}
  m_Layer: 5
  m_Name: MasterVolumeSlider
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &2120279515
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2120279514}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0.0001495125}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1610641478}
  - {fileID: 514748380}
  - {fileID: 868595561}
  - {fileID: 1352050521}
  - {fileID: 99197033}
  m_Father: {fileID: 1428216459}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 0, y: 0}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 232.7, y: 20}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &2120279516
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2120279514}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 67db9e8f0e2ae9c40bc1e2b64352a6b4, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Navigation:
    m_Mode: 3
    m_WrapAround: 0
    m_SelectOnUp: {fileID: 0}
    m_SelectOnDown: {fileID: 0}
    m_SelectOnLeft: {fileID: 0}
    m_SelectOnRight: {fileID: 0}
  m_Transition: 1
  m_Colors:
    m_NormalColor: {r: 1, g: 1, b: 1, a: 1}
    m_HighlightedColor: {r: 0.9607843, g: 0.9607843, b: 0.9607843, a: 1}
    m_PressedColor: {r: 0.78431374, g: 0.78431374, b: 0.78431374, a: 1}
    m_SelectedColor: {r: 0.9607843, g: 0.9607843, b: 0.9607843, a: 1}
    m_DisabledColor: {r: 0.78431374, g: 0.78431374, b: 0.78431374, a: 0.5019608}
    m_ColorMultiplier: 1
    m_FadeDuration: 0.1
  m_SpriteState:
    m_HighlightedSprite: {fileID: 0}
    m_PressedSprite: {fileID: 0}
    m_SelectedSprite: {fileID: 0}
    m_DisabledSprite: {fileID: 0}
  m_AnimationTriggers:
    m_NormalTrigger: Normal
    m_HighlightedTrigger: Highlighted
    m_PressedTrigger: Pressed
    m_SelectedTrigger: Selected
    m_DisabledTrigger: Disabled
  m_Interactable: 1
  m_TargetGraphic: {fileID: 2112910345}
  m_FillRect: {fileID: 2078784409}
  m_HandleRect: {fileID: 2112910344}
  m_Direction: 0
  m_MinValue: 0
  m_MaxValue: 1
  m_WholeNumbers: 0
  m_Value: 0
  m_OnValueChanged:
    m_PersistentCalls:
      m_Calls: []
--- !u!1 &2124822542
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2124822543}
  - component: {fileID: 2124822544}
  m_Layer: 5
  m_Name: MusicVolumeSettingSlider
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &2124822543
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2124822542}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0.0001495125}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 309580895}
  - {fileID: 902296190}
  - {fileID: 1355259850}
  - {fileID: 305942180}
  - {fileID: 2043513899}
  m_Father: {fileID: 1176333024}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 0, y: 0}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 232.7, y: 20}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &2124822544
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2124822542}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 67db9e8f0e2ae9c40bc1e2b64352a6b4, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Navigation:
    m_Mode: 3
    m_WrapAround: 0
    m_SelectOnUp: {fileID: 0}
    m_SelectOnDown: {fileID: 0}
    m_SelectOnLeft: {fileID: 0}
    m_SelectOnRight: {fileID: 0}
  m_Transition: 1
  m_Colors:
    m_NormalColor: {r: 1, g: 1, b: 1, a: 1}
    m_HighlightedColor: {r: 0.9607843, g: 0.9607843, b: 0.9607843, a: 1}
    m_PressedColor: {r: 0.78431374, g: 0.78431374, b: 0.78431374, a: 1}
    m_SelectedColor: {r: 0.9607843, g: 0.9607843, b: 0.9607843, a: 1}
    m_DisabledColor: {r: 0.78431374, g: 0.78431374, b: 0.78431374, a: 0.5019608}
    m_ColorMultiplier: 1
    m_FadeDuration: 0.1
  m_SpriteState:
    m_HighlightedSprite: {fileID: 0}
    m_PressedSprite: {fileID: 0}
    m_SelectedSprite: {fileID: 0}
    m_DisabledSprite: {fileID: 0}
  m_AnimationTriggers:
    m_NormalTrigger: Normal
    m_HighlightedTrigger: Highlighted
    m_PressedTrigger: Pressed
    m_SelectedTrigger: Selected
    m_DisabledTrigger: Disabled
  m_Interactable: 1
  m_TargetGraphic: {fileID: 1879765802}
  m_FillRect: {fileID: 1165948200}
  m_HandleRect: {fileID: 1879765801}
  m_Direction: 0
  m_MinValue: 0
  m_MaxValue: 1
  m_WholeNumbers: 0
  m_Value: 0
  m_OnValueChanged:
    m_PersistentCalls:
      m_Calls: []
--- !u!1001 &2133852681
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    serializedVersion: 3
    m_TransformParent: {fileID: 1675261855}
    m_Modifications:
    - target: {fileID: 1030975294, guid: abc00000000006504693431563510938, type: 3}
      propertyPath: m_Name
      value: SM_Roadblock_9
      objectReference: {fileID: 0}
    - target: {fileID: 1030975295, guid: abc00000000006504693431563510938, type: 3}
      propertyPath: m_LocalScale.x
      value: 0.14657001
      objectReference: {fileID: 0}
    - target: {fileID: 1030975295, guid: abc00000000006504693431563510938, type: 3}
      propertyPath: m_LocalScale.y
      value: 0.14657001
      objectReference: {fileID: 0}
    - target: {fileID: 1030975295, guid: abc00000000006504693431563510938, type: 3}
      propertyPath: m_LocalScale.z
      value: 0.14657001
      objectReference: {fileID: 0}
    - target: {fileID: 1030975295, guid: abc00000000006504693431563510938, type: 3}
      propertyPath: m_LocalPosition.x
      value: 8.660001
      objectReference: {fileID: 0}
    - target: {fileID: 1030975295, guid: abc00000000006504693431563510938, type: 3}
      propertyPath: m_LocalPosition.y
      value: -0.07300001
      objectReference: {fileID: 0}
    - target: {fileID: 1030975295, guid: abc00000000006504693431563510938, type: 3}
      propertyPath: m_LocalPosition.z
      value: -9.475998
      objectReference: {fileID: 0}
    - target: {fileID: 1030975295, guid: abc00000000006504693431563510938, type: 3}
      propertyPath: m_LocalRotation.w
      value: 0.4787991
      objectReference: {fileID: 0}
    - target: {fileID: 1030975295, guid: abc00000000006504693431563510938, type: 3}
      propertyPath: m_LocalRotation.x
      value: -0.06108207
      objectReference: {fileID: 0}
    - target: {fileID: 1030975295, guid: abc00000000006504693431563510938, type: 3}
      propertyPath: m_LocalRotation.y
      value: 0.87515926
      objectReference: {fileID: 0}
    - target: {fileID: 1030975295, guid: abc00000000006504693431563510938, type: 3}
      propertyPath: m_LocalRotation.z
      value: 0.033417974
      objectReference: {fileID: 0}
    - target: {fileID: 1030975295, guid: abc00000000006504693431563510938, type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: -6.718
      objectReference: {fileID: 0}
    - target: {fileID: 1030975295, guid: abc00000000006504693431563510938, type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 122.888
      objectReference: {fileID: 0}
    - target: {fileID: 1030975295, guid: abc00000000006504693431563510938, type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: -4.326
      objectReference: {fileID: 0}
    - target: {fileID: 1030975295, guid: abc00000000006504693431563510938, type: 3}
      propertyPath: m_ConstrainProportionsScale
      value: 1
      objectReference: {fileID: 0}
    m_RemovedComponents: []
    m_RemovedGameObjects: []
    m_AddedGameObjects: []
    m_AddedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: abc00000000006504693431563510938, type: 3}
--- !u!4 &2133852682 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: 1030975295, guid: abc00000000006504693431563510938, type: 3}
  m_PrefabInstance: {fileID: 2133852681}
  m_PrefabAsset: {fileID: 0}
--- !u!1 &2147134074
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2147134075}
  - component: {fileID: 2147134077}
  - component: {fileID: 2147134076}
  m_Layer: 5
  m_Name: Background
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &2147134075
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2147134074}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 954295166}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0.25}
  m_AnchorMax: {x: 1, y: 0.75}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 0, y: 0}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &2147134076
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2147134074}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fe87c0e1cc204ed48ad3b37840f39efc, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_Sprite: {fileID: 10907, guid: 0000000000000000f000000000000000, type: 0}
  m_Type: 1
  m_PreserveAspect: 0
  m_FillCenter: 1
  m_FillMethod: 4
  m_FillAmount: 1
  m_FillClockwise: 1
  m_FillOrigin: 0
  m_UseSpriteMesh: 0
  m_PixelsPerUnitMultiplier: 1
--- !u!222 &2147134077
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2147134074}
  m_CullTransparentMesh: 1
--- !u!1 &2147199878
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2147199879}
  m_Layer: 0
  m_Name: HelicopterSittingPlayerPrefab
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &2147199879
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2147199878}
  serializedVersion: 2
  m_LocalRotation: {x: -0.69476634, y: -0.1315285, z: -0.1315285, w: -0.69476634}
  m_LocalPosition: {x: -0.0071255504, y: 0.009327089, z: -0.015219999}
  m_LocalScale: {x: 0.01, y: 0.010000003, z: 0.010000001}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 641934374}
  m_Father: {fileID: 1238138302}
  m_LocalEulerAnglesHint: {x: -291.44, y: 90, z: 90}
--- !u!4 &18187357157853537
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7251843551858790332}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0.00010672569, y: -0.0008868217, z: 0.006423683}
  m_LocalScale: {x: 0.9999999, y: 0.9999998, z: 0.99999994}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 3808606319589069117}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!23 &49273863488466898
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2556237416927818062}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 4f85681ea1f23504483c2d213f667336, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!1 &195472782334263993
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4730906692191587538}
  - component: {fileID: 7837563164122608636}
  - component: {fileID: 2837840224568529240}
  m_Layer: 0
  m_Name: WingB
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &311291866461585814
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2210681373319296660}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0.00089526176, y: -0.07297647, z: -0.0051310537}
  m_LocalScale: {x: 0.9999998, y: 1, z: 0.9999998}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 6601735085562921454}
  - {fileID: 4078469145186273968}
  - {fileID: 770353677568124210}
  m_Father: {fileID: 4952532225842315733}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!4 &543450653026896001
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 919132148755010107}
  serializedVersion: 2
  m_LocalRotation: {x: -0.7071068, y: -0, z: -0, w: 0.7071068}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 99.999985, y: 100.00004, z: 100}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 2031483244838330352}
  - {fileID: 4730906692191587538}
  - {fileID: 4952532225842315733}
  - {fileID: 1942974922478420828}
  - {fileID: 1238138302}
  - {fileID: 666247744}
  m_Father: {fileID: 967290192}
  m_LocalEulerAnglesHint: {x: -90, y: 0, z: 0}
--- !u!4 &715227660823663083
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7139868945967957891}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0.00010672569, y: -0.0008868599, z: 0.006423683}
  m_LocalScale: {x: 0.9999999, y: 0.99999976, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 3820912493046862927}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!4 &770353677568124210
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1777747667409433138}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0.00010676384, y: 0.0008868027, z: 0.0064236615}
  m_LocalScale: {x: 0.9999999, y: 0.9999998, z: 0.99999994}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 311291866461585814}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &843901521755570180
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1942974922478420828}
  - component: {fileID: 3128358271539064336}
  - component: {fileID: 2600916806034548722}
  m_Layer: 0
  m_Name: WingD
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!1 &919132148755010107
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 543450653026896001}
  - component: {fileID: 3469287836808660772}
  - component: {fileID: 1711813856302511086}
  m_Layer: 0
  m_Name: ForestIntroHelicopter
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!33 &1024087923893679818
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7251843551858790332}
  m_Mesh: {fileID: -2114036416050301415, guid: 35004f51827b4024d8fb0f75a47adf22, type: 3}
--- !u!33 &1138251024776196263
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2556237416927818062}
  m_Mesh: {fileID: -8925567509239597157, guid: 35004f51827b4024d8fb0f75a47adf22, type: 3}
--- !u!33 &1149206960175140427
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2485110299644159181}
  m_Mesh: {fileID: -7412186734910914858, guid: 35004f51827b4024d8fb0f75a47adf22, type: 3}
--- !u!33 &1245708121592517608
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3562810672529820705}
  m_Mesh: {fileID: -8849852334305699881, guid: 35004f51827b4024d8fb0f75a47adf22, type: 3}
--- !u!1 &1423675741381364475
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 6123530014722047012}
  - component: {fileID: 5362447150559658412}
  - component: {fileID: 6370094832143168675}
  m_Layer: 0
  m_Name: BladeD1
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!1 &1464604831135355973
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 3540722678556420416}
  - component: {fileID: 5797494552017835190}
  - component: {fileID: 5175498840124551027}
  m_Layer: 0
  m_Name: RotorB
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!33 &1633057199269811558
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5781313290272008852}
  m_Mesh: {fileID: -4509881544894615413, guid: 35004f51827b4024d8fb0f75a47adf22, type: 3}
--- !u!23 &1711813856302511086
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 919132148755010107}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 4f85681ea1f23504483c2d213f667336, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!1 &1749018465706188039
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2686071071863125669}
  - component: {fileID: 5150828902392138549}
  - component: {fileID: 8972539082230851163}
  m_Layer: 0
  m_Name: BladeB2
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!1 &1777747667409433138
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 770353677568124210}
  - component: {fileID: 8542659630834133825}
  - component: {fileID: 8441355557799133666}
  m_Layer: 0
  m_Name: BladeC3
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1942974922478420828
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 843901521755570180}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0.086143546, y: 0.013600346, z: 0.038174186}
  m_LocalScale: {x: 0.99999994, y: 1, z: 0.99999994}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 3808606319589069117}
  m_Father: {fileID: 543450653026896001}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!4 &2031483244838330352
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6966103899435944552}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: -0.08590623, y: -0.013600386, z: 0.038174093}
  m_LocalScale: {x: 0.99999994, y: 1, z: 0.99999994}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 3820912493046862927}
  m_Father: {fileID: 543450653026896001}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!33 &2188989135214061437
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5443061777182786332}
  m_Mesh: {fileID: -4324865346736749382, guid: 35004f51827b4024d8fb0f75a47adf22, type: 3}
--- !u!1 &2210681373319296660
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 311291866461585814}
  - component: {fileID: 8817318877485954223}
  - component: {fileID: 4916132239065749885}
  m_Layer: 0
  m_Name: RotorC
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!23 &2226242129698794328
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5781313290272008852}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 4f85681ea1f23504483c2d213f667336, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!23 &2306551136955327207
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4919659880836615074}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 4f85681ea1f23504483c2d213f667336, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!1 &2485110299644159181
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 7221286950016082867}
  - component: {fileID: 1149206960175140427}
  - component: {fileID: 4150529355898944158}
  m_Layer: 0
  m_Name: BladeB3
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!1 &2556237416927818062
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 8058630041540441963}
  - component: {fileID: 1138251024776196263}
  - component: {fileID: 49273863488466898}
  m_Layer: 0
  m_Name: BladeD2
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!23 &2600916806034548722
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 843901521755570180}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 4f85681ea1f23504483c2d213f667336, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!4 &2686071071863125669
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1749018465706188039}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0.000106773376, y: 0.0008868408, z: 0.006423664}
  m_LocalScale: {x: 0.9999999, y: 0.99999976, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 3540722678556420416}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!23 &2837840224568529240
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 195472782334263993}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 4f85681ea1f23504483c2d213f667336, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!23 &2986407039701571131
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6966103899435944552}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 4f85681ea1f23504483c2d213f667336, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!33 &3128358271539064336
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 843901521755570180}
  m_Mesh: {fileID: -3336150892328907877, guid: 35004f51827b4024d8fb0f75a47adf22, type: 3}
--- !u!33 &3469287836808660772
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 919132148755010107}
  m_Mesh: {fileID: -4175831326919835478, guid: 35004f51827b4024d8fb0f75a47adf22, type: 3}
--- !u!4 &3540722678556420416
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1464604831135355973}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0.0012650966, y: 0.072976515, z: -0.0051309704}
  m_LocalScale: {x: 0.9999998, y: 1, z: 0.9999998}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 8994154984624865550}
  - {fileID: 2686071071863125669}
  - {fileID: 7221286950016082867}
  m_Father: {fileID: 4730906692191587538}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &3562810672529820705
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4078469145186273968}
  - component: {fileID: 1245708121592517608}
  - component: {fileID: 4810412296370580880}
  m_Layer: 0
  m_Name: BladeC2
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!23 &3713956657550327052
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4818056565479925362}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 4f85681ea1f23504483c2d213f667336, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!4 &3808606319589069117
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5443061777182786332}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0.0008952522, y: 0.07297655, z: -0.0051310514}
  m_LocalScale: {x: 0.9999998, y: 1, z: 0.9999998}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 6123530014722047012}
  - {fileID: 8058630041540441963}
  - {fileID: 18187357157853537}
  m_Father: {fileID: 1942974922478420828}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!4 &3820912493046862927
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4680768391226952319}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0.0012651252, y: -0.072976515, z: -0.0051309704}
  m_LocalScale: {x: 0.9999998, y: 1, z: 0.9999998}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 7520303749728533331}
  - {fileID: 715227660823663083}
  - {fileID: 9061242129056996018}
  m_Father: {fileID: 2031483244838330352}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!33 &4049040857796848100
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4680768391226952319}
  m_Mesh: {fileID: 4598540313994886834, guid: 35004f51827b4024d8fb0f75a47adf22, type: 3}
--- !u!4 &4078469145186273968
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3562810672529820705}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0.00010676384, y: 0.0008868027, z: 0.0064236615}
  m_LocalScale: {x: 0.9999999, y: 0.99999976, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 311291866461585814}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!23 &4150529355898944158
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2485110299644159181}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 4f85681ea1f23504483c2d213f667336, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!23 &4398626600430722065
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8800336050031177046}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 4f85681ea1f23504483c2d213f667336, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!33 &4562787612932450800
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7139868945967957891}
  m_Mesh: {fileID: -7275850767700296598, guid: 35004f51827b4024d8fb0f75a47adf22, type: 3}
--- !u!33 &4583724106228377825
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4818056565479925362}
  m_Mesh: {fileID: 4231285092641381498, guid: 35004f51827b4024d8fb0f75a47adf22, type: 3}
--- !u!1 &4680768391226952319
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 3820912493046862927}
  - component: {fileID: 4049040857796848100}
  - component: {fileID: 4977783593030554666}
  m_Layer: 0
  m_Name: RotorA
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4730906692191587538
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 195472782334263993}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: -0.0859062, y: 0.013600383, z: 0.038174093}
  m_LocalScale: {x: 0.99999994, y: 1, z: 0.99999994}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 3540722678556420416}
  m_Father: {fileID: 543450653026896001}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!23 &4810412296370580880
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3562810672529820705}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 4f85681ea1f23504483c2d213f667336, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!1 &4818056565479925362
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4952532225842315733}
  - component: {fileID: 4583724106228377825}
  - component: {fileID: 3713956657550327052}
  m_Layer: 0
  m_Name: WingC
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!1001 &4904207304937004222
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    serializedVersion: 3
    m_TransformParent: {fileID: 708659531}
    m_Modifications:
    - target: {fileID: 4349889308364899038, guid: 6e55e6f93c1f7994a921018c2285a659, type: 3}
      propertyPath: m_LocalScale.x
      value: 2.3266804
      objectReference: {fileID: 0}
    - target: {fileID: 4349889308364899038, guid: 6e55e6f93c1f7994a921018c2285a659, type: 3}
      propertyPath: m_LocalScale.z
      value: 2.3266804
      objectReference: {fileID: 0}
    - target: {fileID: 4349889308364899038, guid: 6e55e6f93c1f7994a921018c2285a659, type: 3}
      propertyPath: m_LocalPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4349889308364899038, guid: 6e55e6f93c1f7994a921018c2285a659, type: 3}
      propertyPath: m_LocalPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4349889308364899038, guid: 6e55e6f93c1f7994a921018c2285a659, type: 3}
      propertyPath: m_LocalPosition.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4349889308364899038, guid: 6e55e6f93c1f7994a921018c2285a659, type: 3}
      propertyPath: m_LocalRotation.w
      value: 0.83959883
      objectReference: {fileID: 0}
    - target: {fileID: 4349889308364899038, guid: 6e55e6f93c1f7994a921018c2285a659, type: 3}
      propertyPath: m_LocalRotation.x
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 4349889308364899038, guid: 6e55e6f93c1f7994a921018c2285a659, type: 3}
      propertyPath: m_LocalRotation.y
      value: 0.54320705
      objectReference: {fileID: 0}
    - target: {fileID: 4349889308364899038, guid: 6e55e6f93c1f7994a921018c2285a659, type: 3}
      propertyPath: m_LocalRotation.z
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 4349889308364899038, guid: 6e55e6f93c1f7994a921018c2285a659, type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4349889308364899038, guid: 6e55e6f93c1f7994a921018c2285a659, type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 65.804
      objectReference: {fileID: 0}
    - target: {fileID: 4349889308364899038, guid: 6e55e6f93c1f7994a921018c2285a659, type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8982426684407628973, guid: 6e55e6f93c1f7994a921018c2285a659, type: 3}
      propertyPath: m_Name
      value: FoldableChairPrefab
      objectReference: {fileID: 0}
    m_RemovedComponents: []
    m_RemovedGameObjects: []
    m_AddedGameObjects: []
    m_AddedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: 6e55e6f93c1f7994a921018c2285a659, type: 3}
--- !u!4 &4904207304937004223 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: 4349889308364899038, guid: 6e55e6f93c1f7994a921018c2285a659, type: 3}
  m_PrefabInstance: {fileID: 4904207304937004222}
  m_PrefabAsset: {fileID: 0}
--- !u!23 &4916132239065749885
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2210681373319296660}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 4f85681ea1f23504483c2d213f667336, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!1 &4919659880836615074
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 7520303749728533331}
  - component: {fileID: 8865268440251327615}
  - component: {fileID: 2306551136955327207}
  m_Layer: 0
  m_Name: BladeA1
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4952532225842315733
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4818056565479925362}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0.08614354, y: -0.013600423, z: 0.038174193}
  m_LocalScale: {x: 0.99999994, y: 1, z: 0.99999994}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 311291866461585814}
  m_Father: {fileID: 543450653026896001}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!23 &4977783593030554666
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4680768391226952319}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 4f85681ea1f23504483c2d213f667336, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!33 &5150828902392138549
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1749018465706188039}
  m_Mesh: {fileID: 4850139463035063823, guid: 35004f51827b4024d8fb0f75a47adf22, type: 3}
--- !u!23 &5175498840124551027
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1464604831135355973}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 4f85681ea1f23504483c2d213f667336, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!33 &5362447150559658412
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1423675741381364475}
  m_Mesh: {fileID: -4763624299744866984, guid: 35004f51827b4024d8fb0f75a47adf22, type: 3}
--- !u!1 &5443061777182786332
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 3808606319589069117}
  - component: {fileID: 2188989135214061437}
  - component: {fileID: 8740787330201506528}
  m_Layer: 0
  m_Name: RotorD
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!23 &5620854155156043712
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7125321592669001106}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 4f85681ea1f23504483c2d213f667336, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!1 &5781313290272008852
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 6601735085562921454}
  - component: {fileID: 1633057199269811558}
  - component: {fileID: 2226242129698794328}
  m_Layer: 0
  m_Name: BladeC1
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!33 &5797494552017835190
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1464604831135355973}
  m_Mesh: {fileID: 2346327005120668371, guid: 35004f51827b4024d8fb0f75a47adf22, type: 3}
--- !u!4 &6123530014722047012
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1423675741381364475}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0.00010672569, y: -0.0008868217, z: 0.006423683}
  m_LocalScale: {x: 0.9999999, y: 1, z: 0.9999999}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 3808606319589069117}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!33 &6350984188867952551
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6966103899435944552}
  m_Mesh: {fileID: -7880688249173118740, guid: 35004f51827b4024d8fb0f75a47adf22, type: 3}
--- !u!23 &6370094832143168675
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1423675741381364475}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 4f85681ea1f23504483c2d213f667336, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!4 &6601735085562921454
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5781313290272008852}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0.00010676384, y: 0.0008868027, z: 0.0064236615}
  m_LocalScale: {x: 0.9999999, y: 1, z: 0.9999999}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 311291866461585814}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &6966103899435944552
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2031483244838330352}
  - component: {fileID: 6350984188867952551}
  - component: {fileID: 2986407039701571131}
  m_Layer: 0
  m_Name: WingA
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!33 &7104959469775956387
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8800336050031177046}
  m_Mesh: {fileID: 7309670794566548324, guid: 35004f51827b4024d8fb0f75a47adf22, type: 3}
--- !u!1 &7125321592669001106
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 8994154984624865550}
  - component: {fileID: 7561250259921259132}
  - component: {fileID: 5620854155156043712}
  m_Layer: 0
  m_Name: BladeB1
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!1 &7139868945967957891
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 715227660823663083}
  - component: {fileID: 4562787612932450800}
  - component: {fileID: 8495369183930242636}
  m_Layer: 0
  m_Name: BladeA2
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &7221286950016082867
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2485110299644159181}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0.000106773376, y: 0.0008868408, z: 0.006423664}
  m_LocalScale: {x: 0.9999999, y: 0.9999998, z: 0.99999994}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 3540722678556420416}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &7251843551858790332
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 18187357157853537}
  - component: {fileID: 1024087923893679818}
  - component: {fileID: 8461399784694124363}
  m_Layer: 0
  m_Name: BladeD3
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &7520303749728533331
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4919659880836615074}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0.00010672569, y: -0.0008868599, z: 0.006423683}
  m_LocalScale: {x: 0.9999999, y: 1, z: 0.9999999}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 3820912493046862927}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!33 &7561250259921259132
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7125321592669001106}
  m_Mesh: {fileID: -2212710542399411575, guid: 35004f51827b4024d8fb0f75a47adf22, type: 3}
--- !u!33 &7837563164122608636
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 195472782334263993}
  m_Mesh: {fileID: 6249380248009496557, guid: 35004f51827b4024d8fb0f75a47adf22, type: 3}
--- !u!4 &8058630041540441963
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2556237416927818062}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0.00010672569, y: -0.0008868217, z: 0.006423683}
  m_LocalScale: {x: 0.9999999, y: 0.99999976, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 3808606319589069117}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!23 &8441355557799133666
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1777747667409433138}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 4f85681ea1f23504483c2d213f667336, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!23 &8461399784694124363
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7251843551858790332}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 4f85681ea1f23504483c2d213f667336, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!23 &8495369183930242636
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7139868945967957891}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 4f85681ea1f23504483c2d213f667336, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!33 &8542659630834133825
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1777747667409433138}
  m_Mesh: {fileID: 6534168654369550785, guid: 35004f51827b4024d8fb0f75a47adf22, type: 3}
--- !u!23 &8740787330201506528
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5443061777182786332}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 4f85681ea1f23504483c2d213f667336, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!1 &8800336050031177046
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 9061242129056996018}
  - component: {fileID: 7104959469775956387}
  - component: {fileID: 4398626600430722065}
  m_Layer: 0
  m_Name: BladeA3
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!33 &8817318877485954223
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2210681373319296660}
  m_Mesh: {fileID: 5152543527611543846, guid: 35004f51827b4024d8fb0f75a47adf22, type: 3}
--- !u!33 &8865268440251327615
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4919659880836615074}
  m_Mesh: {fileID: 4278405082419172581, guid: 35004f51827b4024d8fb0f75a47adf22, type: 3}
--- !u!23 &8972539082230851163
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1749018465706188039}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 4f85681ea1f23504483c2d213f667336, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!4 &8994154984624865550
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7125321592669001106}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0.000106773376, y: 0.0008868408, z: 0.006423664}
  m_LocalScale: {x: 0.9999999, y: 1, z: 0.9999999}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 3540722678556420416}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!4 &9061242129056996018
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8800336050031177046}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0.00010672569, y: -0.0008868599, z: 0.006423683}
  m_LocalScale: {x: 0.9999999, y: 0.9999998, z: 0.99999994}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 3820912493046862927}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1660057539 &9223372036854775807
SceneRoots:
  m_ObjectHideFlags: 0
  m_Roots:
  - {fileID: 2013410475}
  - {fileID: 1675261855}
  - {fileID: 168872576}
  - {fileID: 1265990968}
  - {fileID: 1914000737}
  - {fileID: 1423917792}
  - {fileID: 94558008}
  - {fileID: 590927388}
  - {fileID: 1812692992}
  - {fileID: 1156203013}
  - {fileID: 173380679}
  - {fileID: 440133750}
  - {fileID: 519976997}
  - {fileID: 967290192}
  - {fileID: 1341548309}
  - {fileID: 2063873617}
  - {fileID: 179687903}
