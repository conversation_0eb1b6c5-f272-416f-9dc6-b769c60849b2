---
trigger: always_on
---

# Project Memory

This file stores project-specific knowledge, conventions, and user preferences for the AI assistant.

## User Preferences
- No code comments.
- Adhere to object-oriented principles and type-safety.
- Implement solutions directly without approval.

## AI Working Rules
- **Context First Principle**: Always check higher-level manager classes for context before making changes, especially for players (ForestPlayerManager, MyNetworkManager), game systems (SystemManager, GameManager), and UI/Camera (relevant managers). Investigate existing architecture to ensure proper integration and avoid duplicate work.

## Technical Decisions
- **Helicopter Navigation**: Helicopters face movement direction using simplified 3D rotation.
- **Player Positioning**: All players (local, replicated, networked) maintain `transform.localPosition = Vector3.zero` relative to spawn points and never change scale. Enforce every frame.
- **Player Rotation System**: Players spawn with `localRotation = Quaternion.Euler(0f, 0f, 0f)`. Player model Y-axis rotation handles visual direction via mouse look. Transform enforcer maintains constraints.
- **Multiplayer Camera System**: ForestMultiplayerCameraManager creates CamHolder at spawn point (Y offset 2.665), parents Camera.main to it. CamHolder handles vertical (X-axis) rotation; player transform handles horizontal (Y-axis) rotation.
- **Helicopter Attachment**: Players parented to helicopter during flight, detached on landing.
- **Multiplayer Helicopter System**: NetworkedHelicopterManager uses RPCs and SyncVars for ForestPlayer attachment across clients.
- **NetworkedHelicopterManager Setup**: Must be server-only NetworkIdentity. GameObject disabling on clients is normal for server-only objects.

## Project Conventions
- **Helicopter Classes**: `Helicopter.cs` handles all rotation/movement. Derived classes should not override.
- **Player Spawning**: Players spawn with zero local position/rotation, aligning with spawn points. No spawn rotation offsets.
- **Player Rotation**: Player model Y-axis (yaw) for visual direction. `ResetLocalTransform()` enforces `Quaternion.Euler(0f, 0f, 0f)`. ForestPlayer syncs Y rotation via SyncVar.
- **Camera Management**: ForestMultiplayerCameraManager creates CamHolder (Y offset 2.665) at spawn point, Camera.main parents to CamHolder. ForestPlayer.AssignMainCamera() finds CamHolder for mouse look. ProcessPlayerLook() uses CamHolder for vertical rotation and player transform for horizontal.
- **Replicated Players**: Use visual model rotation for network sync, maintaining local position/rotation constraints.
- **Helicopter Attachment System**: `AttachToHelicopter()` parents players; `DetachFromHelicopter()` returns them to original parent. `ResetLocalTransform()` enforces zero local position/identity rotation. Attachment state prevents mouse look/local transform updates.
- **Multiplayer Player Management**:
    - ForestPlayer: Actual multiplayer player (NetworkBehaviour).
    - ForestIntroPlayer/ForestIntroReplicatedPlayer: Intro sequence only.
    - NetworkedHelicopterManager: Handles ForestPlayer helicopter attachment via RPCs.
    - ForestMultiplayerCameraManager: Manages CamHolder/Camera.main.
    - MyNetworkManager: Assigns PlayerSlotId based on connection order.
    - Systems work in parallel for complete coverage.

## Critical Multiplayer Setup Issues Resolved
- **Missing NetworkManager**: Scene needs GameObject with MyNetworkManager, LobbyPlayerList, and Transport (KcpTransport).
- **Player Prefab Assignment**: NetworkManager.playerPrefab must be ClientPlayerPrefab.prefab.
- **Spawn Prefabs**: NetworkManager.spawnPrefabs must include ForestIntroHelicopter.
- **Helicopter Deactivation Issue**: Fixed NetworkedHelicopterManager singleton logic destroying duplicates by ensuring one instance and proper component references.
- **Player Parenting Issue**: SOLVED by creating standalone spawn points and modifying attachment system:
    1. Dedicated spawn points: SpawnPoint_LocalPlayer, SpawnPoint_ReplicatedPlayer_0/1/2.
    2. ForestPlayerManager updated to reference new spawn points.
    3. Player attachment scripts no longer change parenting during helicopter attachment.
    4. Players remain children of designated spawn points with `localPosition = Vector3.zero`.
    5. Local player parenting works; replicated players need scene restart.

## Solution Summary: Player Spawn Point Parenting
**Problem**: Players parented to helicopter's internal spawn points instead of dedicated scene spawn points.
**Root Cause**: ForestPlayerManager referenced helicopter's internal SpawnPoint transforms.
**Solution**:
1. Created standalone spawn point GameObjects in scene.
2. Updated ForestPlayerManager.localPlayerSpawnPoint and replicatedPlayerSpawnPoints.
3. Modified player attachment scripts to not change parenting.
4. Result: Players remain children of designated spawn points with `localPosition = Vector3.zero`.
**Status**: Local player ✅. Replicated players need fresh scene start.

## Transform Constraint System
**Critical Requirements**:
- ALL players: `transform.localPosition = Vector3.zero` at all times.
- ALL players: `transform.localRotation = Quaternion.Euler(0f, 0f, zRotation)` (only Z rotation allowed).
- Scale must NEVER be modified.
- Enforce constraints every frame via `ResetLocalTransform()`.
- Mouse look/player facing uses only Z-axis rotation.

## Issue Fixes
- **Helicopter Deactivation Issue**: Fixed by adding NetworkedHelicopterManager GameObject with NetworkIdentity, adding NetworkIdentity to ForestIntroHelicopter, configuring references in ForestGameManager, and adding both to NetworkManager spawn prefabs.

## Critical NetworkedHelicopterManager Setup
**CRITICAL**: NetworkedHelicopterManager must be a scene object, NOT a spawnable prefab:
1. **NetworkIdentity**: `serverOnly: true` checked.
2. **Spawn Prefabs**: Do NOT add to NetworkManager.spawnPrefabs.
3. **Scene Placement**: Must exist directly in scene at game start.