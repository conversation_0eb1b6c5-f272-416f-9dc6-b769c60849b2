using UnityEngine;

namespace TheLastStand
{
    public class ForestGameManager : GameManager
    {
        
        [SerializeField] private ForestIntroHelicopter forestIntroHelicopter;
        [SerializeField] private ForestMap_AudioManager forestMapAudioManager;
        [SerializeField] private NetworkedHelicopterManager networkedHelicopterManager;    public void Start()
        {
          // Verify that the NetworkManager persisted from MainMenu scene
          if (MyNetworkManager.instance != null)
          {
              Debug.Log("ForestGameManager: NetworkManager successfully persisted from MainMenu scene.");
          }
          else
          {
              Debug.LogWarning("ForestGameManager: NetworkManager instance is null. This may indicate the NetworkManager was not properly persisted from MainMenu scene.");
          }

          if (forestIntroHelicopter != null)
          {
            forestIntroHelicopter.Invoke("StartIntroSequence", 1f);
          }
          else
          {
            Debug.LogError("ForestGameManager: ForestIntroHelicopter not assigned in the Inspector.");
          }

          if (forestMapAudioManager != null)
          {
              forestMapAudioManager.PlayIntroStormSound();
              forestMapAudioManager.PlayIntroBackgroundMusic();
              forestMapAudioManager.Invoke("PlayNarratorIntro", 2f);
          }
          else
          {
              Debug.LogError("ForestGameManager: ForestMapAudioManager not assigned in the Inspector.");
          }

          if (networkedHelicopterManager == null)
          {
              networkedHelicopterManager = FindFirstObjectByType<NetworkedHelicopterManager>();
              if (networkedHelicopterManager == null)
              {
                  Debug.LogWarning("ForestGameManager: NetworkedHelicopterManager not found in scene. Multiplayer helicopter attachment may not work properly.");
              }
              else
              {
                  Debug.Log("ForestGameManager: Found NetworkedHelicopterManager in scene.");
              }
          }
        }

        public void OnHelicopterLanded()
        {
          // Fade out the helicopter sound
          AudioSettings.Instance.FadeOutSound(forestIntroHelicopter.GetComponent<AudioSource>(), 2f);

          
        }
        
    }
}
