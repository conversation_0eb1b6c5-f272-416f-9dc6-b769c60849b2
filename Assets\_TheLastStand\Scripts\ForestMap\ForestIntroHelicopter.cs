using UnityEngine;
using System.Collections;

namespace TheLastStand
{
    public class ForestIntroHelicopter : Helicopter
    {
        [Head<PERSON>("Forest Intro Specific Settings")]
        [SerializeField] private float specificLandingTime = 3f;

        public override void Start()
        {
            base.Start();
        }

        public void StartIntroSequence()
        {
            Debug.Log("ForestIntroHelicopter: StartIntroSequence called", this);
            
            if (this.waypoints == null || this.waypoints.Count == 0)
            {
                Debug.LogError("ForestIntroHelicopter: No waypoints assigned! Helicopter cannot start flying. Please assign waypoints in the Inspector.", this);
                enabled = false;
                return;
            }

            Debug.Log($"ForestIntroHelicopter: Starting intro sequence with {waypoints.Count} waypoints", this);
            
            enabled = true;
            
            BeginNavigation(this.waypoints, specificLandingTime);
            Debug.Log("ForestIntroHelicopter: Navigation started successfully", this);
        }

        protected override System.Collections.IEnumerator Execute_LandingState()
        {
            yield return StartCoroutine(base.Execute_LandingState());
            
            OnHelicopterLanded();
        }

        private void OnHelicopterLanded()
        {
            Debug.Log("ForestIntroHelicopter: Helicopter has landed, detaching players", this);
            
            NetworkedHelicopterManager helicopterManager = NetworkedHelicopterManager.Instance;
            if (helicopterManager != null && helicopterManager.isServer)
            {
                helicopterManager.Server_DetachAllPlayersFromHelicopter();
            }
            else if (helicopterManager == null)
            {
                Debug.LogWarning("ForestIntroHelicopter: NetworkedHelicopterManager not found, cannot detach players!");
            }

            ForestPlayerManager playerManager = FindFirstObjectByType<ForestPlayerManager>();
            if (playerManager != null)
            {
                playerManager.DetachAllPlayersFromHelicopter();
            }

            

            ForestGameManager gameManager = FindFirstObjectByType<ForestGameManager>();
            if (gameManager != null)
            {
                gameManager.OnHelicopterLanded();
            }
            else
            {
                Debug.LogWarning("ForestIntroHelicopter: ForestGameManager not found, cannot notify of landing!", this);
            }
        }

        public void OnNavigationCompleted() 
        {
            Debug.Log("ForestIntroHelicopter: Navigation completed", this);
        }

        protected void OnWaypointReached(Transform reachedWaypoint)
        {
            int reachedWaypointIndex = -1;
            if (this.waypoints != null)
            {
                reachedWaypointIndex = this.waypoints.IndexOf(reachedWaypoint);
            }

            if (reachedWaypointIndex != -1)
            {
                Debug.Log($"ForestIntroHelicopter: Reached waypoint '{reachedWaypoint.name}' (Index: {reachedWaypointIndex}). Base class will handle progression.", this);
            }
            else
            {
                Debug.LogWarning($"ForestIntroHelicopter: Reached waypoint '{reachedWaypoint.name}', but it was not found in the assigned waypoints list. This could indicate a configuration issue.", this);
            }
        }
    }
}
