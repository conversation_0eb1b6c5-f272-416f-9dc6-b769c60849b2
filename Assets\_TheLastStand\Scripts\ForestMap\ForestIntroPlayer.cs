using UnityEngine;

namespace TheLastStand
{   
    public class ForestIntroPlayer : MonoBehaviour
    {
        [Header("References")]
        [Tooltip("The transform of the player's visual model. Should be a child of this object.")]
        [SerializeField] private Transform playerModel;
        private Transform cameraTransform;
        private ForestMap_UIManager uiManager;

        [Head<PERSON>("Settings")]
        [SerializeField] private float mouseSensitivity = 200f;
        [SerializeField] private float verticalLookMinAngle = -85f;
        [SerializeField] private float verticalLookMaxAngle = 85f;

        [Header("Helicopter Attachment")]
        [SerializeField] private bool isAttachedToHelicopter = false;

        private float _currentCameraXRotation = 0f;
        private readonly Vector3 _cameraLocalOffset = new Vector3(0f, 1.693f, 0.174f);
        private readonly Quaternion _cameraLocalRotation = Quaternion.identity;
        private bool isSettingsPanelOpen = false;

        void Start()
        {
            Cursor.lockState = CursorLockMode.Locked;
            Cursor.visible = false;

            if (playerModel == null)
            {
                Debug.LogWarning("ForestIntroPlayer: 'playerModel' is not assigned. Visual rotation will not apply to a separate model.", this);
            }
            
            uiManager = FindFirstObjectByType<ForestMap_UIManager>();
            if (uiManager == null)
            {
                Debug.LogError("ForestIntroPlayer: ForestMap_UIManager not found in scene. ESC to toggle settings panel will not work.", this);
            }

            ResetLocalTransform();
        }

        private void ResetLocalTransform()
        {
            transform.localPosition = Vector3.zero;
            
            // Only reset rotation if we're attached to helicopter or if X/Z rotations are non-zero
            Vector3 currentRotation = transform.localRotation.eulerAngles;
            
            // Debug when this method is called
            if (Input.GetKey(KeyCode.LeftAlt) && Input.GetKeyDown(KeyCode.R))
            {
                Debug.Log($"ForestIntroPlayer: ResetLocalTransform called - Current rotation: {currentRotation}, IsAttached: {isAttachedToHelicopter}");
            }
            
            // Only enforce rotation constraints if attached to helicopter OR if X/Z are significantly off
            bool needsRotationReset = isAttachedToHelicopter || 
                                     Mathf.Abs(currentRotation.x) > 1f || 
                                     (Mathf.Abs(currentRotation.x - 360f) > 1f && currentRotation.x > 359f) ||
                                     Mathf.Abs(currentRotation.z) > 1f || 
                                     (Mathf.Abs(currentRotation.z - 360f) > 1f && currentRotation.z > 359f);
            
            if (needsRotationReset)
            {
                float preservedYRotation = currentRotation.y;
                transform.localRotation = Quaternion.Euler(0f, preservedYRotation, 0f);
                
                if (Input.GetKey(KeyCode.LeftAlt) && Input.GetKeyDown(KeyCode.R))
                {
                    Debug.Log($"ForestIntroPlayer: Reset rotation - Preserved Y: {preservedYRotation}, Final: {transform.localRotation.eulerAngles}");
                }
            }
        }

        public void InitializePlayerCamera(Camera cameraToUse)
        {
            if (cameraToUse != null)
            {
                Debug.Log($"ForestIntroPlayer: Configuring assigned camera '{cameraToUse.name}'.", this);
                
                // If ForestMultiplayerCameraManager exists, let it handle camera setup
                if (ForestMultiplayerCameraManager.Instance != null)
                {
                    Debug.Log($"ForestIntroPlayer: ForestMultiplayerCameraManager.Instance exists. Letting it handle camera setup.", this);
                    this.cameraTransform = cameraToUse.transform;
                    
                    // Ensure this camera is tagged as MainCamera
                    if (cameraToUse.tag != "MainCamera")
                    {
                        Debug.Log($"ForestIntroPlayer: Tagging camera '{cameraToUse.name}' as MainCamera.", this);
                        cameraToUse.tag = "MainCamera";
                    }
                    
                    return;
                }
                
                // Fallback: if ForestMultiplayerCameraManager doesn't exist, use our own setup
                Debug.LogWarning("ForestIntroPlayer: ForestMultiplayerCameraManager.Instance is null. Using fallback camera setup.", this);
                if (cameraToUse.transform.parent != this.transform)
                {
                    cameraToUse.transform.SetParent(this.transform);
                    cameraToUse.transform.localPosition = _cameraLocalOffset;
                    cameraToUse.transform.localRotation = _cameraLocalRotation;
                }
                
                // Ensure this camera is tagged as MainCamera
                if (cameraToUse.tag != "MainCamera")
                {
                    Debug.Log($"ForestIntroPlayer: Tagging camera '{cameraToUse.name}' as MainCamera.", this);
                    cameraToUse.tag = "MainCamera";
                }
                this.cameraTransform = cameraToUse.transform;
            }
            else
            {
                Debug.Log("ForestIntroPlayer: No specific camera assigned by Manager. Attempting to find and configure Camera.main.", this);
                Camera mainCam = Camera.main;
                if (mainCam != null)
                {
                    Debug.Log($"ForestIntroPlayer: Found and using existing Camera.main '{mainCam.name}'.", this);
                    
                    // If ForestMultiplayerCameraManager exists, let it handle camera setup
                    if (ForestMultiplayerCameraManager.Instance != null)
                    {
                        Debug.Log($"ForestIntroPlayer: ForestMultiplayerCameraManager.Instance exists. Letting it handle camera setup.", this);
                        this.cameraTransform = mainCam.transform;
                        return;
                    }
                    
                    // Fallback: if ForestMultiplayerCameraManager doesn't exist, use our own setup
                    Debug.LogWarning("ForestIntroPlayer: ForestMultiplayerCameraManager.Instance is null. Using fallback camera setup.", this);
                    if (mainCam.transform.parent != this.transform)
                    {
                        mainCam.transform.SetParent(this.transform);
                        mainCam.transform.localPosition = _cameraLocalOffset;
                        mainCam.transform.localRotation = _cameraLocalRotation;
                    }
                    
                    this.cameraTransform = mainCam.transform;
                }
                else
                {
                    Debug.LogError("ForestIntroPlayer: No camera provided by Manager, and Camera.main could not be found. Player will not have a camera managed by this script.", this);
                }
            }
        }

        void Update()
        {
            if (Input.GetKeyDown(KeyCode.Escape))
            {
                TogglePanelAndCursor();
            }

            if (!isSettingsPanelOpen)
            {
                HandleMouseLook();
            }
        }

        void LateUpdate()
        {
            // PlayerTransformEnforcer now handles the necessary constraints.
            // This LateUpdate can be simplified or even removed if PTE covers all needs.
            // For now, let's keep a basic local position check as a safeguard.
            if (transform.localPosition != Vector3.zero)
            {
                // Debug.LogWarning($"ForestIntroPlayer: Local position was changed to {transform.localPosition}, resetting to Vector3.zero", this);
                transform.localPosition = Vector3.zero;
            }
            
            // Debug Y rotation changes
            if (Input.GetKey(KeyCode.LeftShift) && Input.GetKeyDown(KeyCode.Y))
            {
                Debug.Log($"ForestIntroPlayer: Current Y rotation: {transform.localRotation.eulerAngles.y}");
                Debug.Log($"ForestIntroPlayer: IsAttachedToHelicopter: {isAttachedToHelicopter}");
            }
            
            // Rotation constraints (X and Z to 0, Y preserved) are handled by PlayerTransformEnforcer
            // when isAttachedToHelicopter is false.
        }

        public void AttachToHelicopter(Transform helicopterTransform)
        {
            if (helicopterTransform == null) return;

            Debug.Log($"ForestIntroPlayer: Attaching to helicopter {helicopterTransform.name}", this);
            
            isAttachedToHelicopter = true;
            ResetLocalTransform();
            
            PlayerTransformEnforcer enforcer = GetComponent<PlayerTransformEnforcer>();
            if (enforcer != null)
            {
                enforcer.SetHelicopterAttachment(true);
            }
            
            if (Camera.main != null)
            {
                ForestCameraController cameraController = Camera.main.GetComponent<ForestCameraController>();
            if (cameraController != null)
            {
                cameraController.OnPlayerAttachedToHelicopter();
            }
        }
    }

    public void DetachFromHelicopter()
    {
        Debug.Log("ForestIntroPlayer: Detaching from helicopter", this);
        
        isAttachedToHelicopter = false;
        ResetLocalTransform();
        
        PlayerTransformEnforcer enforcer = GetComponent<PlayerTransformEnforcer>();
        if (enforcer != null)
        {
            enforcer.SetHelicopterAttachment(false);
        }
        
        if (Camera.main != null)
        {
            ForestCameraController cameraController = Camera.main.GetComponent<ForestCameraController>();
            if (cameraController != null)
            {
                cameraController.OnPlayerDetachedFromHelicopter();
            }
        }
    }

    private void TogglePanelAndCursor()
    {
        if (uiManager != null)
        {
            uiManager.ToggleSettingsPanel();
            isSettingsPanelOpen = !isSettingsPanelOpen;

            if (isSettingsPanelOpen)
            {
                Cursor.lockState = CursorLockMode.None;
                Cursor.visible = true;
            }
            else
            {
                Cursor.lockState = CursorLockMode.Locked;
                Cursor.visible = false;
            }
        }
        else
        {
            Debug.LogWarning("ForestIntroPlayer: uiManager reference is null. Cannot toggle settings panel.", this);
        }
    }


    
    private void HandleMouseLook()
    {
        float mouseX = Input.GetAxis("Mouse X") * mouseSensitivity * Time.deltaTime;
        float mouseY = Input.GetAxis("Mouse Y") * mouseSensitivity * Time.deltaTime;

        // HORIZONTAL LOOK: Rotate the player body on Y-axis
        // This rotates the entire player (and camera with it) left/right
        Vector3 currentRotation = transform.localRotation.eulerAngles;
        float newYRotation = currentRotation.y + mouseX;
        transform.localRotation = Quaternion.Euler(0f, newYRotation, 0f);

        // VERTICAL LOOK: Rotate the camera on X-axis only (pitch)
        // Camera should always face forward relative to the player
        if (cameraTransform != null)
        {
            // Calculate pitch
            _currentCameraXRotation -= mouseY;
            _currentCameraXRotation = Mathf.Clamp(_currentCameraXRotation, verticalLookMinAngle, verticalLookMaxAngle);

            Transform camParent = cameraTransform.parent;
            if (camParent != null)
            {
                // We're now using the spawn point (camera's parent) to handle pitch
                // We set both pitch (X) and match the player's yaw (Y)
                camParent.localRotation = Quaternion.Euler(_currentCameraXRotation, transform.localEulerAngles.y, 0f);
                // Camera itself should have no local rotation relative to parent
                cameraTransform.localRotation = Quaternion.identity;
            }
            else
            {
                // Fallback: if camera has no parent, directly rotate the camera
                cameraTransform.localRotation = Quaternion.Euler(_currentCameraXRotation, 0f, 0f);
            }
        }
        else
        {
            // If we somehow lost our camera reference, try to find it
            Camera mainCam = Camera.main;
            if (mainCam != null)
            {
                cameraTransform = mainCam.transform;
            }
        }
    }

    // UpdatePlayerModelRotation is no longer needed as HandleMouseLook directly rotates the playerModel.

    public float GetWorldYRotation()
    {
        return transform.localRotation.eulerAngles.y;
    }

    public Quaternion GetWorldOrientation()
    {
        return transform.rotation;
    }

    public bool IsAttachedToHelicopter()
    {
        return isAttachedToHelicopter;
    }
}
}