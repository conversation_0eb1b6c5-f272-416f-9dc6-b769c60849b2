using UnityEngine;

namespace TheLastStand
{
    public class ForestIntroReplicatedPlayer : MonoBehaviour
    {
        [<PERSON><PERSON>("References")]
        [Tooltip("The transform of the replicated player's visual model. This is what will be rotated.")]
        [SerializeField] private Transform playerModel;

        [<PERSON><PERSON>("Helicopter Attachment")]
        [SerializeField] private bool isAttachedToHelicopter = false;

        private float initialYRotationOffset = 0f;

        void Awake()
        {
            if (playerModel == null)
            {
                // If not assigned, assume the model is this transform or a child named "Model"
                Transform modelChild = transform.Find("Model");
                if (modelChild != null)
                {
                    playerModel = modelChild;
                }
                else
                {
                    playerModel = transform; // Default to this object's transform
                }
                Debug.LogWarning($"ForestIntroReplicatedPlayer on {gameObject.name}: 'playerModel' was not explicitly assigned. Defaulted to {(playerModel == transform ? "this object\'s transform" : "child \'Model\'.")}", this);
            }
        }

        void Start()
        {
            ResetLocalTransform();
        }

        void LateUpdate()
        {
            if (transform.localPosition != Vector3.zero)
            {
                Debug.LogWarning($"ForestIntroReplicatedPlayer: Local position was changed to {transform.localPosition}, resetting to Vector3.zero", this);
            }
            ResetLocalTransform();
        }

        private void ResetLocalTransform()
        {
            transform.localPosition = Vector3.zero;
            transform.localRotation = Quaternion.Euler(0f, 0f, 0f);
        }

        public void AttachToHelicopter(Transform helicopterTransform)
        {
            if (helicopterTransform == null) return;

            Debug.Log($"ForestIntroReplicatedPlayer: Attaching to helicopter {helicopterTransform.name}", this);
            
            isAttachedToHelicopter = true;
            ResetLocalTransform();
            
            PlayerTransformEnforcer enforcer = GetComponent<PlayerTransformEnforcer>();
            if (enforcer != null)
            {
                enforcer.SetHelicopterAttachment(true);
            }
        }

        public void DetachFromHelicopter()
        {
            Debug.Log("ForestIntroReplicatedPlayer: Detaching from helicopter", this);
            
            isAttachedToHelicopter = false;
            ResetLocalTransform();
            
            PlayerTransformEnforcer enforcer = GetComponent<PlayerTransformEnforcer>();
            if (enforcer != null)
            {
                enforcer.SetHelicopterAttachment(false);
            }
        }

        public void SetInitialYRotationOffset(float offset)
        {
            this.initialYRotationOffset = offset;
        }

        /// <summary>
        /// Updates the replicated player's visual model to face a new world Y rotation,
        /// while keeping its world X and Z rotations unchanged.
        /// </summary>
        /// <param name="newWorldAngle">The target world Y rotation in degrees.</param>
        public void SetVisualWorldYRotation(float newWorldAngle)
        {
            if (playerModel != null)
            {
                playerModel.localRotation = Quaternion.Euler(0f, newWorldAngle, 0f);
            }
            else
            {
                Debug.LogError("ForestIntroReplicatedPlayer: 'playerModel' is null. Cannot update rotation.", this);
            }
        }

        /// <summary>
        /// Updates the replicated player's visual model based on a full world orientation,
        /// but only applies the Y component to its rotation, keeping world X and Z unchanged.
        /// </summary>
        /// <param name="newWorldOrientation">The target full world orientation.</param>
        public void SetVisualWorldYRotation(Quaternion newWorldOrientation)
        {
            SetVisualWorldYRotation(newWorldOrientation.eulerAngles.y);
        }

        public bool IsAttachedToHelicopter()
        {
            return isAttachedToHelicopter;
        }
    }
}
