using UnityEngine;
using UnityEngine.UI;

namespace TheLastStand
{
    public class ForestMap_AudioManager : MonoBehaviour
    {
        public static ForestMap_AudioManager instance;

        [Header("Audio Sources")]
        public AudioSource musicAudioSource;
        public AudioSource uiSfxAudioSource;
        public AudioSource ambienceAudioSource;

        [Head<PERSON>("UI Sliders (Optional)")]
        public Slider masterVolumeSlider;
        public Slider musicVolumeSlider;
        public Slider soundFxVolumeSlider;
        public Slider ambienceVolumeSlider;

        [Header("Music Tracks")]
        public AudioClip introBackgroundMusic;
        public AudioClip narrator_intro_0;
        public AudioClip HelicopterFlyingLoop;

        [Header("Ambience Tracks")]
        public AudioClip introStormSound;

        [Header("Audio Settings")]
        public float narratorSpeedMultiplier = 1.5f;

        private void Awake()
        {
            if (instance == null)
            {
                instance = this;
            }
            else
            {
                Destroy(gameObject);
                return;
            }
        }

        void Start()
        {
            if (AudioSettings.Instance != null)
            {
                ApplyVolumeSettings();
                SetupSliderListeners();
                UpdateSliderValues();
            }
            else
            {
                Debug.LogError("ForestMap_AudioManager: AudioSettings.Instance is not found. Make sure an AudioSettings GameObject exists in the scene.");
            }
        }

        public void PlayIntroBackgroundMusic()
        {
            if (musicAudioSource != null && introBackgroundMusic != null)
            {
                musicAudioSource.clip = introBackgroundMusic;
                musicAudioSource.loop = true;
                musicAudioSource.Play();
            }
            else
            {
                if (musicAudioSource == null)
                    Debug.LogWarning("ForestMap_AudioManager: MusicAudioSource is not assigned.");
                if (introBackgroundMusic == null)
                    Debug.LogWarning("ForestMap_AudioManager: introBackgroundMusic AudioClip is not assigned.");
            }
        }

        public void PlayIntroStormSound()
        {
            if (ambienceAudioSource != null && introStormSound != null)
            {
                ambienceAudioSource.clip = introStormSound;
                ambienceAudioSource.loop = true;
                ambienceAudioSource.Play();
            }
        }

        public void PlayNarratorIntro()
        {
            if (uiSfxAudioSource != null && narrator_intro_0 != null)
            {
                uiSfxAudioSource.pitch = narratorSpeedMultiplier;
                uiSfxAudioSource.PlayOneShot(narrator_intro_0);
            }
        }

        public void PlayHelicopterLoop()
        {
            if (ambienceAudioSource != null && HelicopterFlyingLoop != null)
            {
                ambienceAudioSource.clip = HelicopterFlyingLoop;
                ambienceAudioSource.loop = true;
                ambienceAudioSource.Play();
            }
        }

        public void StopHelicopterLoop()
        {
            if (ambienceAudioSource != null && ambienceAudioSource.isPlaying)
            {
                ambienceAudioSource.Stop();
            }
        }

        private void ApplyVolumeSettings()
        {
            float masterVolume = AudioSettings.Instance.masterVolume / 100f;
            if (musicAudioSource != null) musicAudioSource.volume = (AudioSettings.Instance.musicVolume / 100f) * masterVolume;
            if (uiSfxAudioSource != null) uiSfxAudioSource.volume = (AudioSettings.Instance.soundFxVolume / 100f) * masterVolume;
            if (ambienceAudioSource != null) ambienceAudioSource.volume = (AudioSettings.Instance.ambienceVolume / 100f) * masterVolume;
        }

        private void SetupSliderListeners()
        {
            if (masterVolumeSlider != null) masterVolumeSlider.onValueChanged.AddListener(SetMasterVolume);
            if (musicVolumeSlider != null) musicVolumeSlider.onValueChanged.AddListener(SetMusicVolume);
            if (soundFxVolumeSlider != null) soundFxVolumeSlider.onValueChanged.AddListener(SetSoundFxVolume);
            if (ambienceVolumeSlider != null) ambienceVolumeSlider.onValueChanged.AddListener(SetAmbienceVolume);
        }

        public void UpdateSliderValues()
        {
            if (AudioSettings.Instance == null) return;

            if (masterVolumeSlider != null)
            {
                masterVolumeSlider.value = AudioSettings.Instance.masterVolume / 100f;
            }
            if (musicVolumeSlider != null)
            {
                musicVolumeSlider.value = AudioSettings.Instance.musicVolume / 100f;
            }
            if (soundFxVolumeSlider != null)
            {
                soundFxVolumeSlider.value = AudioSettings.Instance.soundFxVolume / 100f;
            }
            if (ambienceVolumeSlider != null)
            {
                ambienceVolumeSlider.value = AudioSettings.Instance.ambienceVolume / 100f;
            }
        }

        public void SetMasterVolume(float volumeSliderValue)
        {
            if (AudioSettings.Instance != null)
            {
                AudioSettings.Instance.SetMasterVolume(volumeSliderValue * 100f);
                ApplyVolumeSettings();
            }
        }

        public void SetMusicVolume(float volumeSliderValue)
        {
            if (AudioSettings.Instance != null)
            {
                AudioSettings.Instance.SetMusicVolume(volumeSliderValue * 100f);
                ApplyVolumeSettings();
            }
        }

        public void SetSoundFxVolume(float volumeSliderValue)
        {
            if (AudioSettings.Instance != null)
            {
                AudioSettings.Instance.SetSoundFxVolume(volumeSliderValue * 100f);
                ApplyVolumeSettings();
            }
        }
        
        public void SetAmbienceVolume(float volumeSliderValue)
        {
            if (AudioSettings.Instance != null)
            {
                AudioSettings.Instance.SetAmbienceVolume(volumeSliderValue * 100f);
                ApplyVolumeSettings();
            }
        }

        public void PlayUISound(AudioClip clip)
        {
            if (uiSfxAudioSource != null && clip != null)
            {
                uiSfxAudioSource.pitch = 1f;
                uiSfxAudioSource.PlayOneShot(clip);
            }
        }

        void OnEnable()
        {
            if (AudioSettings.Instance != null)
            {
                ApplyVolumeSettings();
                UpdateSliderValues();
            }
        }
    }
}
