using Mirror;
using System.Collections.Generic;
using UnityEngine;
using TheLastStand;

public class ForestMultiplayerCameraManager : NetworkBehaviour
{
    public static ForestMultiplayerCameraManager Instance { get; private set; }
    
    [Header("Camera Settings")]
    [Tooltip("The primary camera for the game, to be assigned in the Inspector.")]
    [SerializeField] private Camera sceneMainCamera;
    [SerializeField] private Vector3 cameraOffset = new Vector3(0f, 5f, -10f);
    [SerializeField] private bool enableDynamicFollow = true;
    
    [Header("Spawn Points")]
    [SerializeField] private List<Transform> playerSpawnPoints = new List<Transform>();
    
    private Dictionary<int, ForestPlayer> playersBySlotId = new Dictionary<int, ForestPlayer>();
    private Dictionary<int, Transform> camHolderBySlotId = new Dictionary<int, Transform>();
    public static Camera MainPlayerCamera { get; private set; }

        void Awake()
    {
        bool IsFirstInstance = Instance == null;
        if (IsFirstInstance)
        {
            Instance = this;
            
            bool HasParent = transform.parent != null;
            if (HasParent)
            {
                transform.SetParent(null);
            }
            
            DontDestroyOnLoad(gameObject);

            if (sceneMainCamera == null)
            {
                Debug.LogError("ForestMultiplayerCameraManager: 'sceneMainCamera' is not assigned in the Inspector!", this);
            }
            else
            {
                MainPlayerCamera = sceneMainCamera;
                if (MainPlayerCamera.tag != "MainCamera")
                {
                    Debug.LogWarning($"ForestMultiplayerCameraManager: Assigned 'sceneMainCamera' ({MainPlayerCamera.name}) was not tagged 'MainCamera'. Tagging it now.", this);
                    MainPlayerCamera.tag = "MainCamera";
                }
            }
            
            // Validate spawn points
            if (playerSpawnPoints.Count == 0)
            {
                Debug.LogError("ForestMultiplayerCameraManager: No player spawn points assigned!");
                
                // Try to find spawn points by tag
                GameObject[] spawnPointObjects = GameObject.FindGameObjectsWithTag("PlayerSpawnPoint");
                if (spawnPointObjects.Length > 0)
                {
                    foreach (GameObject spawnPoint in spawnPointObjects)
                    {
                        playerSpawnPoints.Add(spawnPoint.transform);
                    }
                    Debug.Log($"ForestMultiplayerCameraManager: Auto-found {playerSpawnPoints.Count} spawn points");
                }
            }
        }
        else
        {
            Destroy(gameObject);
        }
    }

    public override void OnStartClient()
    {
        base.OnStartClient();
        
        // Only setup cameras for local client
        if (NetworkClient.localPlayer != null)
        {
            StartCoroutine(DelayedCameraSetup());
        }
    }

    private System.Collections.IEnumerator DelayedCameraSetup()
    {
        yield return new WaitForSeconds(1f); // Initial delay to allow other systems to initialize

        if (MainPlayerCamera == null)
        {
            Debug.LogError("ForestMultiplayerCameraManager: MainPlayerCamera (assigned from sceneMainCamera) is null in DelayedCameraSetup! Ensure it's set in Inspector.", this);
            yield break; // Stop further execution if camera is not found
        }
        
        // Wait until we have local player spawned
        int attempts = 0;
        while (NetworkClient.localPlayer == null && attempts < 10)
        {
            yield return new WaitForSeconds(0.5f);
            attempts++;
        }

        if (NetworkClient.localPlayer == null)
        {
            Debug.LogError("ForestMultiplayerCameraManager: Local player not found after waiting.");
            yield break;
        }
        
        AssignCamerasToPlayers();
    }

        private void AssignCamerasToPlayers()
    {
        if (MainPlayerCamera == null)
        {
            Debug.LogError("ForestMultiplayerCameraManager: MainPlayerCamera is null during assignment");
            return;
        }
        
        ForestPlayer[] allPlayers = FindObjectsByType<ForestPlayer>(FindObjectsSortMode.None);
        
        // We only need to set up cameras for the local player on this client
        foreach (ForestPlayer player in allPlayers)
        {
            if (player == null || !player.isActiveAndEnabled) continue;
            
            PlayerIdentity identity = player.GetComponent<PlayerIdentity>();
            if (identity == null) continue;
            
            int slotId = identity.PlayerSlotId;
            playersBySlotId[slotId] = player;
            
            // Only handle spawn points and camera setup for owned (local) player
            if (player.isOwned)
            {
                if (slotId < playerSpawnPoints.Count && playerSpawnPoints[slotId] != null)
                {
                    Transform spawnPoint = playerSpawnPoints[slotId];
                    
                    // Ensure player is properly parented to spawn point
                    if (player.transform.parent != spawnPoint)
                    {
                        player.transform.SetParent(spawnPoint);
                    }
                    
                    // Enforce transform constraints
                    player.transform.localPosition = Vector3.zero;
                    player.transform.localRotation = Quaternion.Euler(0f, 0f, 0f);
                    
                    // Setup camera for local player
                    SetupMainCameraForPlayer(spawnPoint, slotId);
                }
                else
                {
                    Debug.LogError($"ForestMultiplayerCameraManager: No spawn point for player slot {slotId}");
                }
            }
        }
    }    private void SetupMainCameraForPlayer(Transform spawnPoint, int slotId)
    {
        if (MainPlayerCamera == null)
        {
            Debug.LogError("FMCM: MainPlayerCamera is NULL during SetupMainCameraForPlayer.");
            return;
        }
        if (spawnPoint == null)
        {
            Debug.LogError($"FMCM: spawnPoint is NULL for slotId {slotId} during SetupMainCameraForPlayer.");
            return;
        }
        if (!spawnPoint.gameObject.activeInHierarchy)
        {
            Debug.LogWarning($"FMCM: spawnPoint '{spawnPoint.name}' is INACTIVE during SetupMainCameraForPlayer. Camera parenting might be affected if spawnPoint needs to be active for camera to render properly, but parenting itself should still occur.");
        }

        Debug.Log($"FMCM: Preparing to parent {MainPlayerCamera.name} (current parent: {MainPlayerCamera.transform.parent?.name}, current scene: {MainPlayerCamera.gameObject.scene.name}) to {spawnPoint.name} (scene: {spawnPoint.gameObject.scene.name}).");

        // Detach from current parent first, if any, to be clean.
        // This step ensures it's free before attempting to attach to the new parent.
        // If MainPlayerCamera is in DDOL, SetParent(null) keeps it in DDOL at root.
        // If MainPlayerCamera is in a scene, SetParent(null) moves it to that scene's root.
        MainPlayerCamera.transform.SetParent(null); 
        Debug.Log($"FMCM: After SetParent(null), {MainPlayerCamera.name} parent is: {MainPlayerCamera.transform.parent?.name}, scene: {MainPlayerCamera.gameObject.scene.name}");
        
        // Now set the new parent.
        MainPlayerCamera.transform.SetParent(spawnPoint);
        
        if (MainPlayerCamera.transform.parent == spawnPoint)
        {
            Debug.Log($"FMCM: SUCCESSFULLY parented {MainPlayerCamera.name} to {spawnPoint.name} (scene: {MainPlayerCamera.gameObject.scene.name}). Setting local transform.");
            MainPlayerCamera.transform.localPosition = cameraOffset;
            MainPlayerCamera.transform.localRotation = Quaternion.identity;
            MainPlayerCamera.transform.localScale = Vector3.one;
            
            if (enableDynamicFollow)
            {
                SetupCameraController(spawnPoint);
            }
        }
        else
        {
            Debug.LogError($"FMCM: FAILED to parent {MainPlayerCamera.name} to {spawnPoint.name}. Current parent is: {MainPlayerCamera.transform.parent?.name}, scene: {MainPlayerCamera.gameObject.scene.name}");
        }
    }

        public void RegisterPlayer(ForestPlayer player)
    {
        if (player == null) return;
        
        // Only handle camera setup on the client that owns this player
        if (!player.isOwned) return;
        
        PlayerIdentity identity = player.GetComponent<PlayerIdentity>();
        if (identity == null) return;
        
        int slotId = identity.PlayerSlotId;
        playersBySlotId[slotId] = player;
        
        if (slotId < playerSpawnPoints.Count && playerSpawnPoints[slotId] != null)
        {
            Transform spawnPoint = playerSpawnPoints[slotId];
            
            // Ensure player is properly parented to spawn point
            if (player.transform.parent != spawnPoint)
            {
                player.transform.SetParent(spawnPoint);
            }
            
            // Enforce transform constraints
            player.transform.localPosition = Vector3.zero;
            player.transform.localRotation = Quaternion.Euler(0f, 0f, 0f);
            
            // Only set up camera for the local player
            if (MainPlayerCamera != null)
            {
                SetupMainCameraForPlayer(spawnPoint, slotId);
                
                // Make sure the player knows about the camera
                player.AssignMainCamera(); // ForestPlayer.AssignMainCamera() will now use ForestMultiplayerCameraManager.MainPlayerCamera
                
                Debug.Log($"ForestMultiplayerCameraManager: Registered local player at spawn point {slotId}");
            }
        }
        else
        {
            Debug.LogError($"ForestMultiplayerCameraManager: No spawn point for player slot {slotId}");
        }
    }

        public void UnregisterPlayer(ForestPlayer player)
    {
        if (player == null) return;
        
        PlayerIdentity identity = player.GetComponent<PlayerIdentity>();
        if (identity == null) return;
        
        int slotId = identity.PlayerSlotId;
        
        // Remove from player dictionary
        if (playersBySlotId.ContainsKey(slotId))
        {
            playersBySlotId.Remove(slotId);
        }
        
        // Clean up camera holder if this was the local player
        if (player.isOwned && camHolderBySlotId.TryGetValue(slotId, out Transform camHolder))
        {
            // Unparent camera before destroying holder
            if (MainPlayerCamera != null && MainPlayerCamera.transform.parent == camHolder)
            {
                MainPlayerCamera.transform.SetParent(null);
            }
            
            camHolderBySlotId.Remove(slotId);
            
            // Destroy cam holder gameobject
            if (camHolder != null)
            {
                Destroy(camHolder.gameObject);
            }
        }
    }
    
    // Utility method to get the CamHolder for a specific player
    public Transform GetCamHolderForPlayerSlot(int slotId)
    {
        if (camHolderBySlotId.TryGetValue(slotId, out Transform camHolder))
        {
            return camHolder;
        }
        
        // If no CamHolder exists yet, create one
        if (slotId < playerSpawnPoints.Count && playerSpawnPoints[slotId] != null)
        {
            Transform spawnPoint = playerSpawnPoints[slotId];
            SetupMainCameraForPlayer(spawnPoint, slotId);
            
            if (camHolderBySlotId.TryGetValue(slotId, out Transform newCamHolder))
            {
                return newCamHolder;
            }
        }
        
        Debug.LogWarning($"ForestMultiplayerCameraManager: No CamHolder found for player slot {slotId}");
        return null;
    }
    
    private void SetupCameraController(Transform spawnPoint)
    {
        if (MainPlayerCamera == null) return;
        
        ForestCameraController cameraController = MainPlayerCamera.GetComponent<ForestCameraController>();
        if (cameraController == null)
        {
            cameraController = MainPlayerCamera.gameObject.AddComponent<ForestCameraController>();
        }
        
        ForestPlayer localPlayer = FindLocalPlayer();
        if (localPlayer != null)
        {
            cameraController.SetPlayerTransform(localPlayer.transform);
            cameraController.SetOffset(cameraOffset);
            cameraController.EnableCameraControl(true);
            
            MainPlayerCamera.transform.SetParent(null);
            
            Debug.Log($"FMCM: Setup ForestCameraController to follow player at spawn point {spawnPoint.name}");
        }
        else
        {
            Debug.LogWarning("FMCM: Could not find local player for ForestCameraController setup");
        }
    }
    
    private ForestPlayer FindLocalPlayer()
    {
        ForestPlayer[] players = FindObjectsOfType<ForestPlayer>();
        foreach (ForestPlayer player in players)
        {
            if (player.isOwned)
            {
                return player;
            }
        }
        return null;
    }
    
    public void SetCameraForLocalPlayerSpawnPoint(Transform spawnPoint)
    {
        if (MainPlayerCamera == null)
        {
            Debug.LogError("FMCM: MainPlayerCamera is null in SetCameraForLocalPlayerSpawnPoint");
            return;
        }
        
        if (spawnPoint == null)
        {
            Debug.LogError("FMCM: spawnPoint is null in SetCameraForLocalPlayerSpawnPoint");
            return;
        }
        
        Debug.Log($"FMCM: SetCameraForLocalPlayerSpawnPoint called with spawnPoint: {spawnPoint.name}");
        
        MainPlayerCamera.transform.SetParent(null);
        MainPlayerCamera.transform.SetParent(spawnPoint);
        
        if (MainPlayerCamera.transform.parent == spawnPoint)
        {
            MainPlayerCamera.transform.localPosition = cameraOffset;
            MainPlayerCamera.transform.localRotation = Quaternion.Euler(10f, 0f, 0f);
            MainPlayerCamera.transform.localScale = Vector3.one;
            
            Debug.Log($"FMCM: Successfully set camera for spawn point {spawnPoint.name} with offset {cameraOffset} and rotation Euler(10, 0, 0)");
            
            if (enableDynamicFollow)
            {
                SetupCameraControllerForSpawnPoint(spawnPoint);
            }
        }
        else
        {
            Debug.LogError($"FMCM: Failed to parent camera to spawn point {spawnPoint.name}");
        }
    }
    
    private void SetupCameraControllerForSpawnPoint(Transform spawnPoint)
    {
        if (MainPlayerCamera == null) return;
        
        ForestCameraController cameraController = MainPlayerCamera.GetComponent<ForestCameraController>();
        if (cameraController == null)
        {
            cameraController = MainPlayerCamera.gameObject.AddComponent<ForestCameraController>();
            Debug.Log("FMCM: Added ForestCameraController component to MainPlayerCamera");
        }
        
        Transform playerModel = FindPlayerModelUnderSpawnPoint(spawnPoint);
        if (playerModel != null)
        {
            cameraController.SetPlayerTransform(playerModel);
            cameraController.SetOffset(cameraOffset);
            cameraController.EnableCameraControl(true);
            
            MainPlayerCamera.transform.SetParent(null);
            
            Debug.Log($"FMCM: Setup ForestCameraController to follow player model {playerModel.name} under spawn point {spawnPoint.name}");
        }
        else
        {
            Debug.LogWarning($"FMCM: Could not find player model under spawn point {spawnPoint.name} for ForestCameraController setup");
        }
    }
    
    private Transform FindPlayerModelUnderSpawnPoint(Transform spawnPoint)
    {
        ForestPlayer forestPlayer = spawnPoint.GetComponentInChildren<ForestPlayer>();
        if (forestPlayer != null && forestPlayer.isOwned)
        {
            return forestPlayer.transform;
        }
        
        ForestIntroPlayer introPlayer = spawnPoint.GetComponentInChildren<ForestIntroPlayer>();
        if (introPlayer != null)
        {
            return introPlayer.transform;
        }
        
        return null;
    }
} 