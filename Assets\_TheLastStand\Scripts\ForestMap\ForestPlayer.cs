using Mirror;
using Steamworks;
using System;
using UnityEngine;
using UnityEngine.AI;
using TheLastStand;

public class ForestPlayer : NetworkBehaviour
{
    [SerializeField] private Camera playerCamera;
    [SerializeField] private GameObject playerModel;
    [SerializeField] private Animator playerAnimator;
    [SerializeField] private CharacterController characterController;
    [SerializeField] private Transform camHolder;
    [SerializeField] private float lookSpeed = 2.0f;
    [SerializeField] private float lookXLimit = 45.0f;
    [SerializeField] private float walkingSpeed = 7.5f;
    [SerializeField] private float runningSpeed = 11.5f;
    [SerializeField] private float jumpHeight = 8.0f;
    [SerializeField] private float gravity = 20.0f;
    [SerializeField] private LayerMask groundMask;
    [SerializeField] private Transform groundedCheck;
    [SerializeField] private float groundedDistance = 2;

    [Header("Helicopter Attachment")]
    [SyncVar(hook = nameof(OnHelicopterAttachmentChanged))]
    public bool isAttachedToHelicopter = false;
    private Transform helicopterParent;
    private Transform originalParent;

    private Vector3 velocity;
    private float rotationX = 0;
    private Vector2 inputDir;
    
    [SyncVar(hook = nameof(OnYRotationChanged))]
    private float syncedYRotation = 0f;

    [Header("AI Navigation")]
    private NavMeshAgent navMeshAgent;
    [SyncVar(hook = nameof(OnAiControlledChanged))]
    public bool isAiControlled = false;
    private bool hasReportedArrival = false;

    private Camera originalPlayerCamera;

    public override void OnStartAuthority()
    {
        base.OnStartAuthority();
        
        if (playerCamera != null && playerCamera.gameObject.activeSelf == false)
        {
            
        }

        Cursor.lockState = CursorLockMode.Locked;
        Cursor.visible = false;
    }

    void Awake()
    {
        navMeshAgent = GetComponent<NavMeshAgent>();
        if (navMeshAgent == null)
        {
            Debug.LogError("NavMeshAgent component not found on ForestPlayer! Please add one.", this);
        }
        if (characterController != null) characterController.enabled = true;
        if (navMeshAgent != null) navMeshAgent.enabled = false;

        if (playerCamera != null) 
        {
            originalPlayerCamera = playerCamera;
        }
    }

    void Start()
    {
        originalParent = transform.parent;
        
        if (!isOwned)
        {
            if (playerCamera != null) playerCamera.gameObject.SetActive(false);
        }
        else
        {
            AssignMainCamera();
        }
        
        OnAiControlledChanged(isAiControlled, isAiControlled);
        OnHelicopterAttachmentChanged(isAttachedToHelicopter, isAttachedToHelicopter);
        
        if (ForestMultiplayerCameraManager.Instance != null)
        {
            ForestMultiplayerCameraManager.Instance.RegisterPlayer(this);
        }
    }

    void Update()
    {
        if (!isAttachedToHelicopter)
        {
            ResetLocalTransform();
        }

        if (!isOwned)
        {
            return;
        }

        if (isAiControlled)
        {
            if (navMeshAgent != null && navMeshAgent.enabled && !hasReportedArrival)
            {
                if (!navMeshAgent.pathPending && navMeshAgent.remainingDistance <= navMeshAgent.stoppingDistance)
                {
                    if (navMeshAgent.remainingDistance == 0 || navMeshAgent.velocity.sqrMagnitude == 0f)
                    {
                        Debug.Log($"Player {netId} reached AI destination.");
                        CmdReachedExitWaypoint();
                        hasReportedArrival = true;
                    }
                }
            }
            if (playerAnimator != null && navMeshAgent != null && navMeshAgent.enabled)
            {
                float speed = navMeshAgent.velocity.magnitude / navMeshAgent.speed;
                playerAnimator.SetFloat("ForwardSpeed", speed);
            }
        }
        else
        {
            if (!isAttachedToHelicopter)
            {
                ProcessPlayerMovement();
            }
            
            ProcessPlayerLook();
        }
    }

    private void ResetLocalTransform()
    {
        transform.localPosition = Vector3.zero;
        
        float currentYRotation = transform.localRotation.eulerAngles.y;
        transform.localRotation = Quaternion.Euler(0f, currentYRotation, 0f);
    }

    [Command]
    public void CmdAttachToHelicopter(NetworkIdentity helicopterNetId)
    {
        if (helicopterNetId != null)
        {
            RpcAttachToHelicopter(helicopterNetId);
        }
    }

    [ClientRpc]
    private void RpcAttachToHelicopter(NetworkIdentity helicopterNetId)
    {
        if (helicopterNetId != null)
        {
            Transform helicopterTransform = helicopterNetId.transform;
            AttachToHelicopter(helicopterTransform);
        }
    }

    public void AttachToHelicopter(Transform helicopterTransform)
    {
        if (helicopterTransform == null) return;

        Debug.Log($"ForestPlayer {netId}: Attaching to helicopter {helicopterTransform.name}", this);
        
        helicopterParent = helicopterTransform;
        transform.SetParent(helicopterTransform);
        ResetLocalTransform();
        
        if (isServer)
        {
            isAttachedToHelicopter = true;
        }
        
        if (isOwned && Camera.main != null)
        {
            ForestCameraController cameraController = Camera.main.GetComponent<ForestCameraController>();
            if (cameraController != null)
            {
                cameraController.OnPlayerAttachedToHelicopter();
            }
        }
    }

    [Command]
    public void CmdDetachFromHelicopter()
    {
        RpcDetachFromHelicopter();
    }

    [ClientRpc]
    private void RpcDetachFromHelicopter()
    {
        DetachFromHelicopter();
    }

    public void DetachFromHelicopter()
    {
        Debug.Log($"ForestPlayer {netId}: Detaching from helicopter", this);
        
        helicopterParent = null;
        transform.SetParent(originalParent);
        ResetLocalTransform();
        
        if (isServer)
        {
            isAttachedToHelicopter = false;
        }
        
        if (isOwned && Camera.main != null)
        {
            ForestCameraController cameraController = Camera.main.GetComponent<ForestCameraController>();
            if (cameraController != null)
            {
                cameraController.OnPlayerDetachedFromHelicopter();
            }
        }
    }

    private void OnHelicopterAttachmentChanged(bool oldValue, bool newValue)
    {
        if (!isOwned) return;
        
        if (newValue && !oldValue)
        {
            Debug.Log($"ForestPlayer {netId}: Helicopter attachment enabled via SyncVar");
        }
        else if (!newValue && oldValue)
        {
            Debug.Log($"ForestPlayer {netId}: Helicopter attachment disabled via SyncVar");
        }
    }

    void OnAiControlledChanged(bool oldState, bool newState)
    {
        if (characterController == null || navMeshAgent == null)
        {
            Debug.LogError("CharacterController or NavMeshAgent is null in OnAiControlledChanged. Ensure they are assigned.", this);
            if (navMeshAgent == null) navMeshAgent = GetComponent<NavMeshAgent>();
            if (characterController == null) characterController = GetComponent<CharacterController>();
            if (characterController == null || navMeshAgent == null) return;
        }

        Vector3 currentPosition = transform.position;
        Quaternion currentRotation = transform.rotation;

        if (newState == true)
        {
            characterController.enabled = false;
            navMeshAgent.enabled = true;
            if (isOwned) 
            {
                navMeshAgent.Warp(currentPosition);
            }

            Debug.Log($"Player {netId} AI Control ENABLED. NavAgent: {navMeshAgent.enabled}, CharCtrl: {characterController.enabled}");
        }
        else
        {
            if (navMeshAgent.enabled)
            {
                currentPosition = navMeshAgent.transform.position;
                currentRotation = navMeshAgent.transform.rotation;
            }
            navMeshAgent.enabled = false;
            
            transform.position = currentPosition;
            transform.rotation = currentRotation;
            
            characterController.enabled = true;
            hasReportedArrival = false;

            Debug.Log($"Player {netId} AI Control DISABLED. NavAgent: {navMeshAgent.enabled}, CharCtrl: {characterController.enabled}");

            if (isOwned)
            {
                Cursor.lockState = CursorLockMode.Locked;
                Cursor.visible = false;
            }
            if (playerAnimator != null)
            {
                playerAnimator.SetFloat("ForwardSpeed", 0);
            }
        }
    }

    [TargetRpc]
    public void TargetRpc_StartAiExit(NetworkConnection target, Vector3 exitPosition)
    {
        if (navMeshAgent == null) 
        {
            Debug.LogError($"NavMeshAgent is null on {gameObject.name} during TargetRpc_StartAiExit. Attempting to get component.");
            navMeshAgent = GetComponent<NavMeshAgent>();
            if (navMeshAgent == null) 
            {
                Debug.LogError($"Failed to get NavMeshAgent on {gameObject.name}. AI cannot start.");
                return;
            }
        }

        if (!isAiControlled) 
        {
            Debug.LogWarning($"TargetRpc_StartAiExit called on {gameObject.name}, but isAiControlled is false. Server should set this first.");
        }
        
        if (navMeshAgent.enabled)
        {
            navMeshAgent.SetDestination(exitPosition);
            hasReportedArrival = false;
            Debug.Log($"NavMeshAgent destination set to {exitPosition} for {gameObject.name}. Agent enabled: {navMeshAgent.enabled}");
        } 
        else 
        {
            Debug.LogError($"TargetRpc_StartAiExit: NavMeshAgent for {gameObject.name} is NOT enabled. Destination not set. isAiControlled: {isAiControlled}");
            if(isAiControlled) 
            {
                navMeshAgent.enabled = true;
                if(navMeshAgent.enabled) 
                {
                    navMeshAgent.SetDestination(exitPosition);
                    hasReportedArrival = false;
                    Debug.Log($"NavMeshAgent for {gameObject.name} enabled and destination set after retry.");
                } 
                else 
                {
                     Debug.LogError($"Still failed to enable NavMeshAgent for {gameObject.name}.");
                }
            }
        }
    }

    [TargetRpc]
    public void TargetRpc_EndAiExit(NetworkConnection target)
    {
        Debug.Log($"TargetRpc_EndAiExit called for {gameObject.name}. Control should be returned via isAiControlled SyncVar.");
    }

    [Command]
    private void CmdReachedExitWaypoint()
    {
        Debug.Log($"Player {netId} (CmdReachedExitWaypoint) reporting arrival to server.");
        HelicopterExitManager.Instance?.Server_PlayerReportedArrival(this);
    }

    [Command]
    public void CmdSetPlayerSlotId(int slotId)
    {
        PlayerIdentity identity = GetComponent<PlayerIdentity>();
        if (identity != null)
        {
            identity.PlayerSlotId = slotId;
            Debug.Log($"Player {netId} (CmdSetPlayerSlotId) requested to set Slot ID to {slotId}. Actual value on identity: {identity.PlayerSlotId}");
        }
        else
        {
            Debug.LogError($"Player {netId} (CmdSetPlayerSlotId) cannot set Slot ID - PlayerIdentity component missing!");
        }
    }

    private void ProcessPlayerMovement()
    {
        if (characterController == null || !characterController.enabled) return;

        bool isGrounded = Physics.CheckSphere(groundedCheck.position, groundedDistance, groundMask);
        if (isGrounded && velocity.y < 0)
        {
            velocity.y = -2f;
        }

        inputDir = new Vector2(Input.GetAxisRaw("Horizontal"), Input.GetAxisRaw("Vertical"));
        Vector3 moveDir = new Vector3(inputDir.x, 0f, inputDir.y).normalized;
        
        Vector3 worldMoveDir = transform.TransformDirection(moveDir);

        float currentSpeed = Input.GetKey(KeyCode.LeftShift) ? runningSpeed : walkingSpeed;
        characterController.Move(worldMoveDir * currentSpeed * Time.deltaTime);

        if (Input.GetButtonDown("Jump") && isGrounded)
        {
            velocity.y = Mathf.Sqrt(jumpHeight * -2f * (Physics.gravity.y));
        }

        velocity.y += Physics.gravity.y * Time.deltaTime;
        characterController.Move(velocity * Time.deltaTime);

        if (playerAnimator != null)
        {
            float animationSpeed = inputDir.magnitude * (currentSpeed / walkingSpeed);
            playerAnimator.SetFloat("ForwardSpeed", animationSpeed);
        }
    }

    private void ProcessPlayerLook()
    {
        if (camHolder == null || playerCamera == null || !playerCamera.gameObject.activeInHierarchy) return;
        
        float mouseX = Input.GetAxis("Mouse X") * lookSpeed;
        float mouseY = Input.GetAxis("Mouse Y") * lookSpeed;
        
        rotationX -= mouseY;
        rotationX = Mathf.Clamp(rotationX, -lookXLimit, lookXLimit);
        
        Vector3 currentRotation = transform.localRotation.eulerAngles;
        float newYRotation = currentRotation.y + mouseX;
        transform.localRotation = Quaternion.Euler(0f, newYRotation, 0f);

        camHolder.localRotation = Quaternion.Euler(rotationX, newYRotation, 0f);
        
        if (isOwned)
        {
            CmdUpdateYRotation(newYRotation);
        }
    }

    [Command]
    private void CmdUpdateYRotation(float yRotation)
    {
        syncedYRotation = yRotation;
    }

    private void OnYRotationChanged(float oldValue, float newValue)
    {
        if (!isOwned)
        {
            Vector3 currentRotation = transform.localRotation.eulerAngles;
            transform.localRotation = Quaternion.Euler(0f, newValue, 0f);
            
            if (playerModel != null)
            {
                playerModel.transform.localRotation = Quaternion.Euler(0f, newValue, 0f);
            }
        }
    }

    public void AssignMainCamera()
    {
        if (!isOwned) return;
        
        playerCamera = Camera.main;
        
        if (playerCamera != null)
        {
            Transform foundCamHolder = playerCamera.transform.parent;
            if (foundCamHolder != null)
            {
                // The spawn point is now acting as our camera holder
                camHolder = foundCamHolder;
                Debug.Log($"Player {netId} assigned Camera.main and found camera parent '{camHolder.name}' for camera controls");
            }
            else
            {
                Debug.LogWarning($"Player {netId}: Camera.main found but has no parent transform!");
                
                // Try to get the proper CamHolder from ForestMultiplayerCameraManager
                if (ForestMultiplayerCameraManager.Instance != null)
                {
                    PlayerIdentity identity = GetComponent<PlayerIdentity>();
                    if (identity != null)
                    {
                        int slotId = identity.PlayerSlotId;
                        Transform spawnPoint = ForestMultiplayerCameraManager.Instance.GetCamHolderForPlayerSlot(slotId);
                        if (spawnPoint != null)
                        {
                            camHolder = spawnPoint;
                            Debug.Log($"Player {netId} retrieved spawn point '{camHolder.name}' from ForestMultiplayerCameraManager");
                        }
                    }
                }
            }
        }
        else
        {
            Debug.LogWarning($"Player {netId}: Camera.main not found!");
        }
    }

    public bool IsAttachedToHelicopter()
    {
        return isAttachedToHelicopter;
    }

    void OnDestroy()
    {
        if (ForestMultiplayerCameraManager.Instance != null)
        {
            ForestMultiplayerCameraManager.Instance.UnregisterPlayer(this);
        }
    }
}