using UnityEngine;
using System.Collections.Generic;
using System.Collections;
using TheLastStand;

public class ForestPlayerManager : MonoBehaviour
{
    [Header("Prefabs")]
    [SerializeField] private GameObject clientPlayerPrefab;
    [SerializeField] private GameObject replicatedPlayerPrefabOne;
    [SerializeField] private GameObject replicatedPlayerPrefabTwo;
    [SerializeField] private GameObject replicatedPlayerPrefabThree;
    // playerCameraToAssign field removed, will use ForestMultiplayerCameraManager.MainPlayerCamera

    [Header("Spawn Points")]
    [SerializeField] private Transform localPlayerSpawnPoint;
    [SerializeField] private List<Transform> replicatedPlayerSpawnPoints = new List<Transform>();

    [Header("Helicopter References")]
    [SerializeField] private Transform helicopterTransform;

    private ForestIntroPlayer _localPlayerInstance;
    private List<ForestIntroReplicatedPlayer> _replicatedPlayerInstances = new List<ForestIntroReplicatedPlayer>();

    void Start()
    {
        SpawnLocalPlayer();
        SpawnReplicatedPlayers();
        
        if (helicopterTransform != null)
        {
            AttachAllPlayersToHelicopter();
        }
        else
        {
            Debug.LogWarning("ForestPlayerManager: helicopterTransform not assigned. Players will not be attached to helicopter.", this);
        }
    }

    void SpawnLocalPlayer()
    {
        if (clientPlayerPrefab == null)
        {
            Debug.LogError("ForestIntroPlayerManager: 'clientPlayerPrefab' is not assigned.", this);
            return;
        }
        if (localPlayerSpawnPoint == null)
        {
            Debug.LogError("ForestIntroPlayerManager: 'localPlayerSpawnPoint' is not assigned. Cannot spawn local player.", this);
            return;
        }

        GameObject localPlayerGO = Instantiate(clientPlayerPrefab, localPlayerSpawnPoint);
        localPlayerGO.transform.localPosition = Vector3.zero;
        localPlayerGO.transform.localRotation = Quaternion.Euler(0f, 0f, 0f);
        
        _localPlayerInstance = localPlayerGO.GetComponent<ForestIntroPlayer>();

        if (_localPlayerInstance == null)
        {
            Debug.LogError("ForestIntroPlayerManager: The 'clientPlayerPrefab' does not have a ForestIntroPlayer component.", localPlayerGO);
            Destroy(localPlayerGO);
        }
        else
        {
            localPlayerGO.name = "Local_ForestIntroPlayer";
            
            // Start a coroutine to delay camera initialization
            StartCoroutine(DelayedCameraInitialization());
        }
    }
    
    private System.Collections.IEnumerator DelayedCameraInitialization()
    {
        // Wait for ForestMultiplayerCameraManager to finish its setup
        yield return new WaitForSeconds(0.5f);
        
        if (_localPlayerInstance != null)
        {
            if (ForestMultiplayerCameraManager.MainPlayerCamera != null)
            {
                Debug.Log("ForestPlayerManager: Initializing player camera after delay", this);
                _localPlayerInstance.InitializePlayerCamera(ForestMultiplayerCameraManager.MainPlayerCamera);
            }
            else
            {
                Debug.LogError("ForestPlayerManager: ForestMultiplayerCameraManager.MainPlayerCamera is null after delay. Cannot assign camera to local player. Ensure 'sceneMainCamera' is assigned in ForestMultiplayerCameraManager Inspector.", this);
                // Optionally, could still try to pass null or let ForestIntroPlayer handle Camera.main as a fallback
                _localPlayerInstance.InitializePlayerCamera(null); 
            }
        }
    }

    void SpawnReplicatedPlayers()
    {
        List<GameObject> availableReplicatedPrefabs = new List<GameObject>();
        if (replicatedPlayerPrefabOne != null) availableReplicatedPrefabs.Add(replicatedPlayerPrefabOne);
        if (replicatedPlayerPrefabTwo != null) availableReplicatedPrefabs.Add(replicatedPlayerPrefabTwo);
        if (replicatedPlayerPrefabThree != null) availableReplicatedPrefabs.Add(replicatedPlayerPrefabThree);

        if (availableReplicatedPrefabs.Count == 0)
        {
            Debug.LogError("ForestPlayerManager: No replicated player prefabs assigned (One, Two, or Three). Cannot spawn replicated players.", this);
            return;
        }

        if (replicatedPlayerSpawnPoints.Count == 0)
        {
            Debug.LogWarning("ForestPlayerManager: No spawn points provided for replicated players.", this);
            return;
        }

        int numberOfPlayersToSpawn = Mathf.Min(replicatedPlayerSpawnPoints.Count, availableReplicatedPrefabs.Count);

        if (replicatedPlayerSpawnPoints.Count > availableReplicatedPrefabs.Count)
        {
            Debug.LogWarning($"ForestPlayerManager: More spawn points ({replicatedPlayerSpawnPoints.Count}) than available unique replicated prefabs ({availableReplicatedPrefabs.Count}). Only spawning {availableReplicatedPrefabs.Count} replicated players.", this);
        }

        for (int i = 0; i < numberOfPlayersToSpawn; i++)
        {
            Transform spawnPoint = replicatedPlayerSpawnPoints[i];
            GameObject prefabToSpawn = availableReplicatedPrefabs[i]; 

            if (spawnPoint == null)
            {
                Debug.LogWarning($"ForestPlayerManager: Replicated player spawn point at index {i} is null. Skipping.", this);
                continue;
            }

            if (prefabToSpawn == null) 
            {
                Debug.LogWarning($"ForestPlayerManager: Replicated player prefab for index {i} is unexpectedly null. Skipping.", this);
                continue;
            }

                        GameObject replicatedPlayerGO = Instantiate(prefabToSpawn, spawnPoint);            replicatedPlayerGO.transform.localPosition = Vector3.zero;            replicatedPlayerGO.transform.localRotation = Quaternion.Euler(0f, 0f, 0f);
            
            ForestIntroReplicatedPlayer replicatedPlayer = replicatedPlayerGO.GetComponent<ForestIntroReplicatedPlayer>();

            if (replicatedPlayer == null)
            {
                Debug.LogError($"ForestPlayerManager: The prefab \"{prefabToSpawn.name}\" does not have a ForestIntroReplicatedPlayer component. Spawned for index {i}.", replicatedPlayerGO);
                Destroy(replicatedPlayerGO);
            }
            else
            {
                                replicatedPlayerGO.name = $"Replicated_ForestIntroPlayer_{i}_{prefabToSpawn.name}";                _replicatedPlayerInstances.Add(replicatedPlayer);
            }
        }
    }

    public void AttachAllPlayersToHelicopter()
    {
        if (helicopterTransform == null)
        {
            Debug.LogError("ForestPlayerManager: Cannot attach players - helicopterTransform is null!", this);
            return;
        }

        Debug.Log("ForestPlayerManager: Attaching all players to helicopter", this);

        if (_localPlayerInstance != null)
        {
            _localPlayerInstance.AttachToHelicopter(helicopterTransform);
        }

        foreach (ForestIntroReplicatedPlayer replicatedPlayer in _replicatedPlayerInstances)
        {
            if (replicatedPlayer != null)
            {
                replicatedPlayer.AttachToHelicopter(helicopterTransform);
            }
        }
    }

    public void DetachAllPlayersFromHelicopter()
    {
        Debug.Log("ForestPlayerManager: Detaching all players from helicopter", this);

        if (_localPlayerInstance != null)
        {
            _localPlayerInstance.DetachFromHelicopter();
        }

        foreach (ForestIntroReplicatedPlayer replicatedPlayer in _replicatedPlayerInstances)
        {
            if (replicatedPlayer != null)
            {
                replicatedPlayer.DetachFromHelicopter();
            }
        }
    }

        void Update()    {        if (_localPlayerInstance != null && _replicatedPlayerInstances.Count > 0)        {            float localPlayerWorldYRotation = _localPlayerInstance.GetWorldYRotation();            foreach (ForestIntroReplicatedPlayer replicatedPlayer in _replicatedPlayerInstances)            {                if (replicatedPlayer != null)                {                    replicatedPlayer.SetVisualWorldYRotation(localPlayerWorldYRotation);                }            }        }    }
}
