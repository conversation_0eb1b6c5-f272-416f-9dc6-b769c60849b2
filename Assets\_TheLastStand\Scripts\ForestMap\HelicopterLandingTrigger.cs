using UnityEngine;
using Mirror;

public class HelicopterLandingTrigger : NetworkBehaviour
{
    [SerializeField] private Transform helicopterTransform;
    [SerializeField] private float landingThresholdHeight = 0.5f;
    [SerializeField] private bool triggerOnlyOnce = true;
    [SerializeField] private float exitSequenceDelay = 1.5f;

    private bool hasTriggeredExit = false;
    private bool isNearGround = false;
    private float timeSinceLanded = 0f;

    [Server]
    private void Update()
    {
        if (!isServer)
            return;

        if (hasTriggeredExit && triggerOnlyOnce)
            return;

        if (helicopterTransform == null)
        {
            Debug.LogWarning("Helicopter transform not assigned to HelicopterLandingTrigger!");
            return;
        }

        isNearGround = Physics.Raycast(helicopterTransform.position, Vector3.down, landingThresholdHeight, Physics.DefaultRaycastLayers, QueryTriggerInteraction.Ignore);

        if (isNearGround)
        {
            timeSinceLanded += Time.deltaTime;

            if (timeSinceLanded >= exitSequenceDelay && !hasTriggeredExit)
            {
                TriggerExitSequence();
            }
        }
        else
        {
            timeSinceLanded = 0f;
        }
    }

    [Server]
    public void TriggerExitSequence()
    {
        if (!isServer)
            return;

        if (hasTriggeredExit && triggerOnlyOnce)
            return;

        Debug.Log("Helicopter has landed, triggering exit sequence and detaching players");
        
        NetworkedHelicopterManager helicopterManager = NetworkedHelicopterManager.Instance;
        if (helicopterManager != null)
        {
            helicopterManager.Server_DetachAllPlayersFromHelicopter();
        }
        else
        {
            Debug.LogWarning("HelicopterLandingTrigger: NetworkedHelicopterManager not found, cannot detach players!");
        }

        ForestPlayerManager playerManager = FindFirstObjectByType<ForestPlayerManager>();
        if (playerManager != null)
        {
            playerManager.DetachAllPlayersFromHelicopter();
        }

        HelicopterExitManager exitManager = HelicopterExitManager.Instance;
        if (exitManager != null)
        {
            exitManager.BeginHelicopterExitSequence();
            hasTriggeredExit = true;
        }
        else
        {
            Debug.LogError("HelicopterExitManager not found in scene!");
        }
    }

    [Server]
    private void OnTriggerEnter(Collider other)
    {
        if (!isServer)
            return;

        if (helicopterTransform != null && other.transform == helicopterTransform)
        {
            Debug.Log("Helicopter entered landing zone");
        }
    }

    [Server]
    public void ManualTrigger()
    {
        if (!isServer)
            return;

        TriggerExitSequence();
    }
} 