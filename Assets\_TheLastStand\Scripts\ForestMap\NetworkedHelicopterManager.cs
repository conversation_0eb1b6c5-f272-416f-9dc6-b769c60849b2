using Mirror;
using System.Collections.Generic;
using UnityEngine;

public class NetworkedHelicopterManager : NetworkBehaviour
{
    public static NetworkedHelicopterManager Instance { get; private set; }

    [Header("Helicopter References")]
    [SerializeField] private Transform helicopterTransform;

    private List<ForestPlayer> allForestPlayers = new List<ForestPlayer>();
    private bool playersAttached = false;

    private void Awake()
    {
        bool IsFirstInstance = Instance == null;
        if (IsFirstInstance)
        {
            Instance = this;
            
            bool HasParent = transform.parent != null;
            if (HasParent)
            {
                transform.SetParent(null);
            }
            
            DontDestroyOnLoad(gameObject);
        }
        else if (Instance != this)
        {
            Destroy(gameObject);
            return;
        }

        if (helicopterTransform == null)
        {
            GameObject helicopter = GameObject.FindGameObjectWithTag("Helicopter");
            if (helicopter != null)
            {
                helicopterTransform = helicopter.transform;
            }
        }
    }

    public override void OnStartServer()
    {
        base.OnStartServer();
        
        if (helicopterTransform == null)
        {
            Debug.LogError("NetworkedHelicopterManager: helicopterTransform not assigned!");
            GameObject helicopter = GameObject.FindGameObjectWithTag("Helicopter");
            if (helicopter != null)
            {
                helicopterTransform = helicopter.transform;
            }
            else
            {
                return;
            }
        }

        StartCoroutine(DelayedAttachPlayers());
    }
    
    public override void OnStartClient()
    {
        base.OnStartClient();
        
        if (!isServer && helicopterTransform == null)
        {
            GameObject helicopter = GameObject.FindGameObjectWithTag("Helicopter");
            if (helicopter != null)
            {
                helicopterTransform = helicopter.transform;
            }
        }
    }

    private System.Collections.IEnumerator DelayedAttachPlayers()
    {
        yield return new WaitForSeconds(2f); // Allow time for players to connect and register
        
        // Refresh player list before attachment
        RefreshPlayerList();
        
        // Only attach if we have players and they aren't already attached
        if (allForestPlayers.Count > 0 && !playersAttached)
        {
            Server_AttachAllPlayersToHelicopter();
        }
        else
        {
            // Try again in a moment if no players found
            StartCoroutine(RetryPlayerAttachment());
        }
    }
    
    private System.Collections.IEnumerator RetryPlayerAttachment()
    {
        yield return new WaitForSeconds(1f);
        RefreshPlayerList();
        
        if (allForestPlayers.Count > 0 && !playersAttached)
        {
            Server_AttachAllPlayersToHelicopter();
        }
    }

    [Server]
    public void Server_AttachAllPlayersToHelicopter()
    {
        if (!isServer) return;
        if (helicopterTransform == null)
        {
            Debug.LogError("Cannot attach players: helicopter transform is null");
            return;
        }

        RefreshPlayerList();

        Debug.Log($"NetworkedHelicopterManager: Attaching {allForestPlayers.Count} players to helicopter");

        foreach (ForestPlayer player in allForestPlayers)
        {
            if (player != null && player.isActiveAndEnabled)
            {
                // Use Command from server to clients
                player.CmdAttachToHelicopter(helicopterTransform.GetComponent<NetworkIdentity>());
                Debug.Log($"NetworkedHelicopterManager: Requested player {player.netId} to attach to helicopter");
            }
        }

        // Set state flag
        playersAttached = true;
        
        // Inform clients about the attachment
        RpcNotifyClientsOfAttachment();
    }

    [Server]
    public void Server_DetachAllPlayersFromHelicopter()
    {
        if (!isServer) return;

        RefreshPlayerList();
        
        Debug.Log($"NetworkedHelicopterManager: Detaching {allForestPlayers.Count} players from helicopter");

        foreach (ForestPlayer player in allForestPlayers)
        {
            if (player != null && player.isActiveAndEnabled)
            {
                // Use Command to communicate to clients
                player.CmdDetachFromHelicopter();
                Debug.Log($"NetworkedHelicopterManager: Requested player {player.netId} to detach from helicopter");
            }
        }

        // Update state flag
        playersAttached = false;
        
        // Inform clients about the detachment
        RpcNotifyClientsOfDetachment();
    }

    [ClientRpc]
    private void RpcNotifyClientsOfAttachment()
    {
        if (isServer) return; // Host client already processed this in Server_AttachAllPlayersToHelicopter
        if (helicopterTransform == null)
        {
            GameObject helicopter = GameObject.FindGameObjectWithTag("Helicopter");
            if (helicopter != null)
            {
                helicopterTransform = helicopter.transform;
            }
            else
            {
                Debug.LogError("RpcNotifyClientsOfAttachment: Cannot find helicopter transform on client");
                return;
            }
        }

        RefreshPlayerList(); // Ensure client has the latest player list
        
        // For non-server clients, we need to handle their local players
        foreach (ForestPlayer player in allForestPlayers)
        {
            if (player != null && player.isActiveAndEnabled && player.isOwned)
            {
                // This applies only to the local player on this client
                player.AttachToHelicopter(helicopterTransform);
                Debug.Log($"Client: Attached local player {player.netId} to helicopter");
            }
        }
    }

    [ClientRpc]
    private void RpcNotifyClientsOfDetachment()
    {
        if (isServer) return; // Host client already processed this

        RefreshPlayerList();
        
        // For non-server clients, only detach their locally owned player
        foreach (ForestPlayer player in allForestPlayers)
        {
            if (player != null && player.isActiveAndEnabled && player.isOwned)
            {
                player.DetachFromHelicopter();
                Debug.Log($"Client: Detached local player {player.netId} from helicopter");
            }
        }
    }

    // Called to refresh player list
    private void RefreshPlayerList()
    {
        allForestPlayers.Clear();
        ForestPlayer[] foundPlayers = FindObjectsByType<ForestPlayer>(FindObjectsSortMode.None);
        
        foreach (ForestPlayer player in foundPlayers)
        {
            if (player != null && player.isActiveAndEnabled)
            {
                allForestPlayers.Add(player);
            }
        }
    }

    public void RegisterPlayer(ForestPlayer player)
    {
        if (player == null) return;
        
        if (!allForestPlayers.Contains(player))
        {
            allForestPlayers.Add(player);
            
            // If server has already attached players, attach this new player too
            if (isServer && playersAttached && helicopterTransform != null)
            {
                player.CmdAttachToHelicopter(helicopterTransform.GetComponent<NetworkIdentity>());
            }
        }
    }

    public void UnregisterPlayer(ForestPlayer player)
    {
        if (allForestPlayers.Contains(player))
        {
            allForestPlayers.Remove(player);
            Debug.Log($"NetworkedHelicopterManager: Unregistered player {player.netId}");
        }
    }
}