using UnityEngine;

public class PlayerTransformEnforcer : MonoBehaviour
{
    [Header("Transform Constraints")]
    [SerializeField] private bool enforceLocalPosition = true;
    [SerializeField] private bool enforceRotationConstraints = true;
    [SerializeField] private bool preventScaleChanges = true;
    [SerializeField] private float rotationTolerance = 0.99f;
    
    private Vector3 originalScale;
    private bool isAttachedToHelicopter = false;

    void Awake()
    {
        originalScale = Vector3.one;
        transform.localScale = originalScale;
    }

    void Start()
    {
        EnforceTransformConstraints();
    }

    void LateUpdate()
    {
        EnforceTransformConstraints();
    }

    public void SetHelicopterAttachment(bool attached)
    {
        isAttachedToHelicopter = attached;
    }

    private void EnforceTransformConstraints()
    {
        if (enforceLocalPosition)
        {
            if (transform.localPosition != Vector3.zero)
            {
                transform.localPosition = Vector3.zero;
            }
        }

        // if (enforceRotationConstraints)
        // {
        //     Vector3 currentRotationEuler = transform.localRotation.eulerAngles;
            
        //     // Use tolerance-based comparison and preserve Y rotation for mouse look
        //     bool needsXCorrection = Mathf.Abs(currentRotationEuler.x) > rotationTolerance && Mathf.Abs(currentRotationEuler.x - 360f) > rotationTolerance;
        //     bool needsZCorrection = Mathf.Abs(currentRotationEuler.z) > rotationTolerance && Mathf.Abs(currentRotationEuler.z - 360f) > rotationTolerance;
            
        //     if (needsXCorrection || needsZCorrection)
        //     {
        //         // Always preserve Y rotation (horizontal mouse look)
        //         transform.localRotation = Quaternion.Euler(0f, currentRotationEuler.y, 0f);
        //     }
        // }

        if (preventScaleChanges)
        {
            if (transform.localScale != originalScale)
            {
                transform.localScale = originalScale;
            }
        }
    }

    public void ForceEnforcement()
    {
        EnforceTransformConstraints();
    }
} 