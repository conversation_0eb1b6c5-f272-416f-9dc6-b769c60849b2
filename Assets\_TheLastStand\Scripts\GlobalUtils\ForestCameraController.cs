using UnityEngine;

namespace TheLastStand
{
    public class ForestCameraController : MonoBehaviour
    {
        [Header("Follow Settings")]
        [SerializeField] private Transform playerTransform;
        [SerializeField] private Vector3 offset = new Vector3(0f, 5f, -10f);
        [SerializeField] private float smoothSpeed = 2.0f;
        
        [Header("Look Settings")]
        [SerializeField] private bool enableMouseLook = false;
        [SerializeField] private float mouseSensitivity = 2.0f;
        [SerializeField] private float verticalAngleLimit = 80f;
        
        private Vector3 currentVelocity;
        private float verticalRotation = 0f;
        private bool isControllingCamera = false;
        private bool followWhenAttachedToHelicopter = false;
        
        void Start()
        {
            if (playerTransform == null)
            {
                FindLocalPlayer();
            }
        }
        
        void LateUpdate()
        {
            if (playerTransform == null || !isControllingCamera) return;
            
            if (ShouldFollowPlayer())
            {
                FollowPlayer();
            }
            
            if (enableMouseLook)
            {
                HandleMouseLook();
            }
        }
        
        private void FollowPlayer()
        {
            Vector3 targetPosition = playerTransform.position + playerTransform.TransformDirection(offset);
            transform.position = Vector3.SmoothDamp(transform.position, targetPosition, ref currentVelocity, 1f / smoothSpeed);
            
            Vector3 lookDirection = playerTransform.position - transform.position;
            if (lookDirection.sqrMagnitude > 0.001f)
            {
                Quaternion targetRotation = Quaternion.LookRotation(lookDirection);
                transform.rotation = Quaternion.Slerp(transform.rotation, targetRotation, smoothSpeed * Time.deltaTime);
            }
        }
        
        private void HandleMouseLook()
        {
            float mouseX = Input.GetAxis("Mouse X") * mouseSensitivity;
            float mouseY = Input.GetAxis("Mouse Y") * mouseSensitivity;
            
            verticalRotation -= mouseY;
            verticalRotation = Mathf.Clamp(verticalRotation, -verticalAngleLimit, verticalAngleLimit);
            
            Vector3 eulerAngles = transform.eulerAngles;
            eulerAngles.x = verticalRotation;
            eulerAngles.y += mouseX;
            transform.eulerAngles = eulerAngles;
        }
        
        public void SetPlayerTransform(Transform target)
        {
            playerTransform = target;
            if (target != null)
            {
                isControllingCamera = true;
            }
        }
        
        public void SetOffset(Vector3 newOffset)
        {
            offset = newOffset;
        }
        
        public void SetSmoothSpeed(float speed)
        {
            smoothSpeed = Mathf.Max(0.1f, speed);
        }
        
        public void EnableCameraControl(bool enable)
        {
            isControllingCamera = enable;
        }
        
        public void SetFollowWhenAttachedToHelicopter(bool follow)
        {
            followWhenAttachedToHelicopter = follow;
        }
        
        public void OnPlayerAttachedToHelicopter()
        {
            if (!followWhenAttachedToHelicopter)
            {
                EnableCameraControl(false);
                Debug.Log("CameraController: Disabled due to helicopter attachment");
            }
        }
        
        public void OnPlayerDetachedFromHelicopter()
        {
            EnableCameraControl(true);
            Debug.Log("CameraController: Re-enabled after helicopter detachment");
        }
        
        private bool ShouldFollowPlayer()
        {
            if (!followWhenAttachedToHelicopter)
            {
                ForestPlayer forestPlayer = playerTransform?.GetComponent<ForestPlayer>();
                if (forestPlayer != null && forestPlayer.IsAttachedToHelicopter())
                {
                    return false;
                }
                
                ForestIntroPlayer introPlayer = playerTransform?.GetComponent<ForestIntroPlayer>();
                if (introPlayer != null && introPlayer.IsAttachedToHelicopter())
                {
                    return false;
                }
            }
            
            return true;
        }
        
        private void FindLocalPlayer()
        {
            ForestPlayer[] players = FindObjectsByType<ForestPlayer>(FindObjectsSortMode.None);
            foreach (ForestPlayer player in players)
            {
                if (player.isOwned)
                {
                    SetPlayerTransform(player.transform);
                    break;
                }
            }
            
            if (playerTransform == null)
            {
                ForestIntroPlayer introPlayer = FindFirstObjectByType<ForestIntroPlayer>();
                if (introPlayer != null)
                {
                    SetPlayerTransform(introPlayer.transform);
                }
            }
        }
    }
}