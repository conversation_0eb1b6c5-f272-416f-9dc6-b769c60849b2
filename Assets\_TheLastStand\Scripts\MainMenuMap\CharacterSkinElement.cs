using UnityEngine;
using UnityEngine.UI;
using TMPro;
using TheLastStand;

public class CharacterSkinElement : MonoBehaviour
{
    [Header("UI References")]
    [SerializeField] private Image avatarImage;
    [SerializeField] private TMP_Text playerNameText;
    [SerializeField] private GameObject readyIndicator;
    [SerializeField] private GameObject notReadyIndicator;

    public MyClient client { get; private set; }
    private bool isReady;

    private void Awake()
    {
        FindUIElements();
    }

    private void FindUIElements()
    {
        if (avatarImage == null)
        {
            avatarImage = GetComponentInChildren<Image>();
        }

        if (playerNameText == null)
        {
            playerNameText = GetComponentInChildren<TMP_Text>();
        }
    }

    public void Initialize(MyClient client, bool _isReady)
    {
        this.client = client;
        this.isReady = _isReady;

        if (client != null)
        {
            UpdatePlayerInfo();
            UpdateAvatar();
            UpdateReadyState(_isReady);

            client.OnClientUpdated += HandleClientUpdate;
        }
        else
        {
            Debug.LogError("CharacterSkinElement: Cannot initialize with null client!");
        }
    }

    private void HandleClientUpdate()
    {
        if (client != null)
        {
            UpdatePlayerInfo();
            UpdateAvatar();
            UpdateReadyState(client.IsReady);
        }
    }

    private void UpdatePlayerInfo()
    {
        if (playerNameText != null && client != null)
        {
            playerNameText.text = client.playerInfo.username;
        }
    }

    private void UpdateAvatar()
    {
        if (avatarImage != null && client != null)
        {
            if (client.icon != null)
            {
                avatarImage.sprite = client.icon;
                avatarImage.color = Color.white;
            }
            else
            {
                avatarImage.sprite = null;
                avatarImage.color = Color.gray;
            }
        }
    }

    public void UpdateReadyState(bool newReadyState)
    {
        isReady = newReadyState;

        if (readyIndicator != null)
        {
            readyIndicator.SetActive(isReady);
        }

        if (notReadyIndicator != null)
        {
            notReadyIndicator.SetActive(!isReady);
        }
    }

    private void OnDestroy()
    {
        if (client != null)
        {
            client.OnClientUpdated -= HandleClientUpdate;
        }
    }
} 