using System.Collections.Generic;
using UnityEngine;
using TheLastStand;

public class CharacterSkinHandler : MonoBehaviour
{
    public static CharacterSkinHandler instance;

    [<PERSON><PERSON>("Character Platforms")]
    [SerializeField] private List<Transform> characterPlatforms = new List<Transform>();

    [Header("Character Prefabs")]
    [SerializeField] private List<GameObject> characterSkinPrefabs = new List<GameObject>();

    private Dictionary<MyClient, CharacterSkinElement> clientCharacterElements = new Dictionary<MyClient, CharacterSkinElement>();

    private void Awake()
    {
        if (instance == null)
        {
            instance = this;
        }
        else
        {
            Destroy(gameObject);
            return;
        }
    }

    private void Start()
    {
        ValidateSetup();
    }

    private void ValidateSetup()
    {
        if (characterPlatforms.Count == 0)
        {
            Debug.LogWarning("CharacterSkinHandler: No character platforms assigned!");
        }

        if (characterSkinPrefabs.Count == 0)
        {
            Debug.LogWarning("CharacterSkinHandler: No character skin prefabs assigned!");
        }
    }

    public void SpawnCharacterMesh(MyClient client)
    {
        if (client == null)
        {
            Debug.LogError("CharacterSkinHandler: Cannot spawn character mesh - client is null!");
            return;
        }

        if (clientCharacterElements.ContainsKey(client))
        {
            Debug.LogWarning($"CharacterSkinHandler: Character mesh already exists for client {client.playerInfo.username}");
            return;
        }

        Transform availablePlatform = GetAvailablePlatform();
        if (availablePlatform == null)
        {
            Debug.LogWarning("CharacterSkinHandler: No available platforms for character spawn!");
            return;
        }

        GameObject characterPrefab = GetCharacterPrefab(client.characterSkinIndex);
        if (characterPrefab == null)
        {
            Debug.LogWarning($"CharacterSkinHandler: No character prefab found for skin index {client.characterSkinIndex}");
            return;
        }

        GameObject characterInstance = Instantiate(characterPrefab, availablePlatform);
        CharacterSkinElement skinElement = characterInstance.GetComponent<CharacterSkinElement>();
        
        if (skinElement == null)
        {
            skinElement = characterInstance.AddComponent<CharacterSkinElement>();
        }

        skinElement.Initialize(client, client.IsReady);
        clientCharacterElements[client] = skinElement;

        Debug.Log($"CharacterSkinHandler: Spawned character for {client.playerInfo.username} at platform {availablePlatform.name}");
    }

    public int GetNextPlatformIndex(MyClient client)
    {
        if (characterSkinPrefabs.Count == 0) return 0;
        
        int currentIndex = client.characterSkinIndex;
        int nextIndex = (currentIndex + 1) % characterSkinPrefabs.Count;
        return nextIndex;
    }

    public void DestroyCharacterMesh(MyClient client)
    {
        if (client == null) return;

        if (clientCharacterElements.TryGetValue(client, out CharacterSkinElement skinElement))
        {
            if (skinElement != null)
            {
                Destroy(skinElement.gameObject);
            }
            clientCharacterElements.Remove(client);
            Debug.Log($"CharacterSkinHandler: Destroyed character mesh for {client.playerInfo.username}");
        }
    }

    private Transform GetAvailablePlatform()
    {
        foreach (Transform platform in characterPlatforms)
        {
            if (platform.childCount == 0)
            {
                return platform;
            }
        }
        return null;
    }

    private GameObject GetCharacterPrefab(int skinIndex)
    {
        if (characterSkinPrefabs.Count == 0) return null;
        
        int clampedIndex = Mathf.Clamp(skinIndex, 0, characterSkinPrefabs.Count - 1);
        return characterSkinPrefabs[clampedIndex];
    }

    public void UpdateCharacterReadyState(MyClient client, bool isReady)
    {
        if (clientCharacterElements.TryGetValue(client, out CharacterSkinElement skinElement))
        {
            skinElement.UpdateReadyState(isReady);
        }
    }

    public void UpdateCharacterSkin(MyClient client, int newSkinIndex)
    {
        DestroyCharacterMesh(client);
        SpawnCharacterMesh(client);
    }
} 