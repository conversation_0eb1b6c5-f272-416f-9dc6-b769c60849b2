using Mirror;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using TMPro;
using UnityEngine;
using UnityEngine.UI;
using UnityEngine.SceneManagement;
using Steamworks;
using TheLastStand;

public enum MenuState { Home, InParty }

public class MainMenu : MonoBehaviour
{
    public static MainMenu instance;

    public MenuState state = MenuState.Home;
    [SerializeField] private CanvasGroup mainMenuUIGroup, partyUIGroup, settingsUIGroup;

    [Header("Ready Button")]
    [SerializeField] private Image readyButton_Image;
    public Color readyColor, notReadyColor;

    [Header("Player Slot 0")]
    [SerializeField] private Image playerPFP0_Image;
    [SerializeField] private TMP_Text playerName0_Text;
    [SerializeField] private TMP_Text readyPlayerIndicator0_Text;
    [SerializeField] private Image playerReadyVisual0_Image;
    [SerializeField] private Button playerSlot0_Button;

    [Header("Player Slot 1")]
    [SerializeField] private Image playerPFP1_Image;
    [SerializeField] private TMP_Text playerName1_Text;
    [SerializeField] private TMP_Text readyPlayerIndicator1_Text;
    [SerializeField] private Image playerReadyVisual1_Image;
    [SerializeField] private Button playerSlot1_Button;
    [Header("Player Slot 2")]
    [SerializeField] private Image playerPFP2_Image;
    [SerializeField] private TMP_Text playerName2_Text;
    [SerializeField] private TMP_Text readyPlayerIndicator2_Text;
    [SerializeField] private Image playerReadyVisual2_Image;
    [SerializeField] private Button playerSlot2_Button;

    [Header("Player Slot 3")]
    [SerializeField] private Image playerPFP3_Image;
    [SerializeField] private TMP_Text playerName3_Text;
    [SerializeField] private TMP_Text readyPlayerIndicator3_Text;
    [SerializeField] private Image playerReadyVisual3_Image;
    [SerializeField] private Button playerSlot3_Button;

    [Header("Host UI")]
    [SerializeField] private CanvasGroup friendsListCanvasGroup;
    [SerializeField] private Button startGameButton;


    private struct PlayerSlotDisplay
    {
        public Image pfpImage;
        public TMP_Text nameText;
        public TMP_Text statusText;
        public Image readyVisualImage;
        public Button slotButton;

        public PlayerSlotDisplay(Image pfp, TMP_Text name, TMP_Text status, Image readyVisual, Button button)
        {
            pfpImage = pfp;
            nameText = name;
            statusText = status;
            readyVisualImage = readyVisual;
            slotButton = button;
        }

        public void SetActiveElements(bool pfpAndNameActive, bool statusAndVisualActive)
        {
            if (pfpImage != null) pfpImage.gameObject.SetActive(pfpAndNameActive);
            if (nameText != null) nameText.gameObject.SetActive(pfpAndNameActive);
            if (statusText != null) statusText.gameObject.SetActive(statusAndVisualActive);
            if (readyVisualImage != null) readyVisualImage.gameObject.SetActive(statusAndVisualActive);
        }
    }

    private List<PlayerSlotDisplay> playerSlotsUI;
    private MyClient[] displayedClientsInSlots;

    private void Awake()
    {
        instance = this;

        playerSlotsUI = new List<PlayerSlotDisplay>
        {
            new PlayerSlotDisplay(playerPFP0_Image, playerName0_Text, readyPlayerIndicator0_Text, playerReadyVisual0_Image, playerSlot0_Button),
            new PlayerSlotDisplay(playerPFP1_Image, playerName1_Text, readyPlayerIndicator1_Text, playerReadyVisual1_Image, playerSlot1_Button),
            new PlayerSlotDisplay(playerPFP2_Image, playerName2_Text, readyPlayerIndicator2_Text, playerReadyVisual2_Image, playerSlot2_Button),
            new PlayerSlotDisplay(playerPFP3_Image, playerName3_Text, readyPlayerIndicator3_Text, playerReadyVisual3_Image, playerSlot3_Button)
        };
        displayedClientsInSlots = new MyClient[playerSlotsUI.Count];
        ConfigurePlayerSlotButtons();
        DisableAllPlayerSlotButtons();
    }

    private void OnEnable()
    {
        // Wait for instances to be ready, especially LobbyPlayerList
        StartCoroutine(DelayedSubscription());
    }

    private IEnumerator DelayedSubscription()
    {
        // Wait until both MyNetworkManager and LobbyPlayerList instances are available
        // This is a common pattern if scripts might enable before singletons are set.
        yield return new WaitUntil(() => MyNetworkManager.instance != null && LobbyPlayerList.instance != null);

        // Check if still enabled in case object was disabled during wait
        if (this.enabled)
        {
            LobbyPlayerList.instance.allClients.Callback += OnPlayerListChanged;
            if (state == MenuState.InParty)
            {
                UpdateLobbyUI();
            }
        }
    }

    private void OnDisable()
    {
        if (LobbyPlayerList.instance != null)
        {
            LobbyPlayerList.instance.allClients.Callback -= OnPlayerListChanged;
        }
        UnsubscribeFromAllClientUpdates();
    }
    
    private void UnsubscribeFromAllClientUpdates()
    {
        if (displayedClientsInSlots == null) return;
        for (int i = 0; i < displayedClientsInSlots.Length; i++)
        {
            if (displayedClientsInSlots[i] != null)
            {
                displayedClientsInSlots[i].OnClientUpdated -= HandleSpecificClientUpdate;
                displayedClientsInSlots[i] = null; 
            }
        }
    }

    private void ConfigurePlayerSlotButtons()
    {
        foreach (var slot in playerSlotsUI)
        {
            if (slot.slotButton != null)
            {
                slot.slotButton.onClick.RemoveAllListeners();
            }
        }
    }

    private void DisableAllPlayerSlotButtons()
    {
        foreach (var slot in playerSlotsUI)
        {
            if(slot.slotButton != null) slot.slotButton.interactable = false;
        }
    }
      private void OnPlayerListChanged(SyncList<NetworkBehaviour>.Operation op, int itemIndex, NetworkBehaviour oldItem, NetworkBehaviour newItem)
    {
        if (op == SyncList<NetworkBehaviour>.Operation.OP_REMOVEAT || op == SyncList<NetworkBehaviour>.Operation.OP_CLEAR)
        {
            if (oldItem != null && oldItem is MyClient myClient)
            {
                myClient.OnClientUpdated -= HandleSpecificClientUpdate;
            }
            if (op == SyncList<NetworkBehaviour>.Operation.OP_CLEAR)
            {
                UnsubscribeFromAllClientUpdates();
            }
        }
        UpdateLobbyUI();
    }

    private void HandleSpecificClientUpdate()
    {
        UpdateLobbyUI();
    }


    public void SetMenuState(MenuState newState)
    {
        state = newState;

        if (state == MenuState.Home)
        {
            ShowCanvasGroup(mainMenuUIGroup);
            HideCanvasGroup(partyUIGroup);
            if (friendsListCanvasGroup != null) HideCanvasGroup(friendsListCanvasGroup);
            if (startGameButton != null) startGameButton.gameObject.SetActive(false);
            UnsubscribeFromAllClientUpdates();
        }
        else
        {
            HideCanvasGroup(mainMenuUIGroup);
            ShowCanvasGroup(partyUIGroup);
            bool isCurrentlyHost = NetworkClient.localPlayer != null && NetworkClient.localPlayer.isServer;
            if (startGameButton != null) startGameButton.gameObject.SetActive(isCurrentlyHost);


            if (friendsListCanvasGroup != null)
            {
                if (isCurrentlyHost) ShowCanvasGroup(friendsListCanvasGroup);
                else HideCanvasGroup(friendsListCanvasGroup);
            }
            
            UpdateLobbyUI();
        }
    }
    
    private void ShowCanvasGroup(CanvasGroup canvasGroup)
    {
        if (canvasGroup == null) return;
        canvasGroup.alpha = 1f;
        canvasGroup.interactable = true;
        canvasGroup.blocksRaycasts = true;
    }

    private void HideCanvasGroup(CanvasGroup canvasGroup)
    {
        if (canvasGroup == null) return;
        canvasGroup.alpha = 0f;
        canvasGroup.interactable = false;
        canvasGroup.blocksRaycasts = false;
    }

    public void CreateParty()
    {
        PopupManager.instance.Popup_Show("Creating Party");
        ((MyNetworkManager)NetworkManager.singleton).SetMultiplayer(true);
        SteamLobby.instance.CreateLobby();
    }

    public void StartSinglePlayer()
    {
        LobbyController.instance.StartGameSolo();
    }

    public void LeaveParty()
    {
        if (!NetworkClient.active) return;

        if (NetworkClient.localPlayer != null && NetworkClient.localPlayer.isServer)
            NetworkManager.singleton.StopHost();
        else
            NetworkManager.singleton.StopClient();

        SteamLobby.instance.Leave();
    }

    public void FindMatch()
    {
        SteamLobby.instance.FindMatch();
    }

    public void OnSettingsButtonClicked()
    {
        HideCanvasGroup(mainMenuUIGroup);
        ShowCanvasGroup(settingsUIGroup);
    }

    public void OnSettingsBackButtonClicked()
    {
        HideCanvasGroup(settingsUIGroup);
        ShowCanvasGroup(mainMenuUIGroup);
    }

    public void StartGame()
    {
        LobbyController.instance.StartGameWithParty();
    }

    public void StartLocalClient()
    {
        ((MyNetworkManager)NetworkManager.singleton).SetMultiplayer(true);
        NetworkManager.singleton.StartClient();
    }

    public void StartLocalHost()
    {
        ((MyNetworkManager)NetworkManager.singleton).SetMultiplayer(true);
        NetworkManager.singleton.StartHost();
    }

    public void ToggleReady()
    {
        if (!NetworkClient.active || NetworkClient.localPlayer == null) return;
        NetworkClient.localPlayer.GetComponent<MyClient>().ToggleReady();
    }

    public void UpdateReadyButton(bool value)
    {
        if (readyButton_Image != null)
        {
            readyButton_Image.color = value ? readyColor : notReadyColor;
        }
    }

    public void UpdateLobbyUI()
    {
        // Added LobbyPlayerList.instance null check here as well for safety
        if (!MyNetworkManager.isMulitplayer || state != MenuState.InParty || NetworkManager.singleton == null || MyNetworkManager.instance == null || LobbyPlayerList.instance == null)
        {
            ClearAllPlayerSlots();
            if (friendsListCanvasGroup != null) HideCanvasGroup(friendsListCanvasGroup);
            if (startGameButton != null) startGameButton.interactable = false;
            return;
        }        // MyNetworkManager networkManager = MyNetworkManager.instance; // No longer need networkManager for allClients
        List<MyClient> displayOrderClients = GetOrderedClientList(LobbyPlayerList.instance); // Use LobbyPlayerList
        
        bool isHost = NetworkClient.localPlayer != null && NetworkClient.localPlayer.isServer;

        if (friendsListCanvasGroup != null)
        {
            if (isHost) ShowCanvasGroup(friendsListCanvasGroup);
            else HideCanvasGroup(friendsListCanvasGroup);
        }
        if (startGameButton != null) startGameButton.gameObject.SetActive(isHost); 

        // Convert NetworkBehaviour list to MyClient list for display
        List<MyClient> allClientsInLobby = new List<MyClient>();
        foreach (NetworkBehaviour networkBehaviour in LobbyPlayerList.instance.allClients)
        {
            if (networkBehaviour is MyClient myClient)
            {
                allClientsInLobby.Add(myClient);
            }
        }

        UpdatePlayerSlotDisplays(displayOrderClients, isHost);
        UpdateStartGameButtonState(isHost, allClientsInLobby); // Use converted list instead of LobbyPlayerList
    }
    
    private void UpdateStartGameButtonState(bool isHost, IReadOnlyList<MyClient> allClients)
    {
        if (startGameButton == null) return;

        if (!isHost)
        {
            startGameButton.interactable = false;
            return;
        }

        bool allReady = true;
        if (allClients.Count == 0)
        {
            allReady = false;
        }
        else
        {
            foreach (MyClient client in allClients)
            {
                if (client == null || !client.IsReady)
                {
                    allReady = false;
                    break;
                }
            }
        }
        startGameButton.interactable = allReady;
    }


    private void ClearAllPlayerSlots()
    {
        for(int i = 0; i < playerSlotsUI.Count; i++)
        {
            playerSlotsUI[i].SetActiveElements(false, false);
            if (displayedClientsInSlots[i] != null)
            {
                displayedClientsInSlots[i].OnClientUpdated -= HandleSpecificClientUpdate;
                displayedClientsInSlots[i] = null;
            }
             if(playerSlotsUI[i].slotButton != null) playerSlotsUI[i].slotButton.interactable = false;
        }
    }    private List<MyClient> GetOrderedClientList(LobbyPlayerList playerListProvider) // Changed parameter
    {
        List<MyClient> allClientsInLobby = new List<MyClient>();
        
        // Convert NetworkBehaviour to MyClient with proper casting
        foreach (NetworkBehaviour networkBehaviour in playerListProvider.allClients)
        {
            if (networkBehaviour is MyClient myClient)
            {
                allClientsInLobby.Add(myClient);
            }
        }
        
        List<MyClient> displayOrderClients = new List<MyClient>();

        MyClient hostClient = allClientsInLobby.FirstOrDefault(c => c != null && c.isServer);
        if (hostClient != null)
        {
            displayOrderClients.Add(hostClient);
        }

        foreach (MyClient client in allClientsInLobby)
        {
            if (client != null && client != hostClient)
            {
                displayOrderClients.Add(client);
            }
        }
        return displayOrderClients;
    }

    private void UpdatePlayerSlotDisplays(List<MyClient> displayOrderClients, bool localPlayerIsHost)
    {
        MyClient localClient = null;
        if (NetworkClient.localPlayer != null)
        {
            localClient = NetworkClient.localPlayer.GetComponent<MyClient>();
        }

        for (int i = 0; i < playerSlotsUI.Count; i++)
        {
            PlayerSlotDisplay currentSlotUI = playerSlotsUI[i];
            MyClient clientToDisplay = (i < displayOrderClients.Count) ? displayOrderClients[i] : null;
            MyClient previouslyDisplayedClient = displayedClientsInSlots[i];

            if(currentSlotUI.slotButton != null) currentSlotUI.slotButton.onClick.RemoveAllListeners();

            if (previouslyDisplayedClient != null && previouslyDisplayedClient != clientToDisplay)
            {
                previouslyDisplayedClient.OnClientUpdated -= HandleSpecificClientUpdate;
            }
            
            if (clientToDisplay != null && clientToDisplay != previouslyDisplayedClient)
            {
                clientToDisplay.OnClientUpdated += HandleSpecificClientUpdate;
            }
            displayedClientsInSlots[i] = clientToDisplay;

            if (clientToDisplay != null)
            {
                UpdateFilledSlot(currentSlotUI, clientToDisplay);
                if(currentSlotUI.slotButton != null) 
                {
                    bool isLocalPlayerSlot = (clientToDisplay == localClient);
                    currentSlotUI.slotButton.interactable = isLocalPlayerSlot;
                    if(isLocalPlayerSlot)
                    {
                        currentSlotUI.slotButton.onClick.AddListener(() => ToggleReady());
                    }
                }
            }
            else
            {
                UpdateEmptySlot(currentSlotUI, localPlayerIsHost);
                if(currentSlotUI.slotButton != null) 
                {
                    currentSlotUI.slotButton.interactable = localPlayerIsHost;
                    if(localPlayerIsHost)
                    {
                        currentSlotUI.slotButton.onClick.AddListener(() => OnInviteButtonClicked());
                    }
                }
            }
        }
    }

    private void UpdateFilledSlot(PlayerSlotDisplay slotUI, MyClient client)
    {
        slotUI.SetActiveElements(true, true);
        UpdateProfilePicture(slotUI, client);
        if(slotUI.nameText != null)
        {
            slotUI.nameText.text = client.playerInfo.username;
        }
        UpdateReadyStatus(slotUI, client);
    }

    private void UpdateProfilePicture(PlayerSlotDisplay slotUI, MyClient client)
    {
        if (slotUI.pfpImage != null)
        {
            if (client.icon != null)
            {
                slotUI.pfpImage.sprite = client.icon;
                slotUI.pfpImage.color = Color.white;
                slotUI.pfpImage.gameObject.SetActive(true);
            }
            else
            {
                slotUI.pfpImage.gameObject.SetActive(false);
            }
        }
    }

    private void UpdateReadyStatus(PlayerSlotDisplay slotUI, MyClient client)
    {
        if (slotUI.statusText != null)
        {
            slotUI.statusText.text = client.IsReady ? "READY" : "NOT READY";
        }
        if (slotUI.readyVisualImage != null)
        {
            slotUI.readyVisualImage.color = client.IsReady ? readyColor : notReadyColor;
        }
    }

    private void UpdateEmptySlot(PlayerSlotDisplay slotUI, bool localPlayerIsHost)
    {
        slotUI.SetActiveElements(false, true);
        if (slotUI.statusText != null)
        {
            slotUI.statusText.text = localPlayerIsHost ? "INVITE" : "NO PLAYER";
        }
        if (slotUI.readyVisualImage != null)
        {
            slotUI.readyVisualImage.color = notReadyColor;
        }
        if (slotUI.pfpImage != null) slotUI.pfpImage.gameObject.SetActive(false);
        if (slotUI.nameText != null) slotUI.nameText.gameObject.SetActive(false);
    }

    private void OnInviteButtonClicked()
    {
        if (SteamLobby.LobbyID.IsValid() && SteamLobby.LobbyID.m_SteamID != 0)
        {
            SteamFriends.ActivateGameOverlayInviteDialog(SteamLobby.LobbyID);
        }
        else
        {
            Debug.LogWarning("MainMenu: Attempted to open Steam invite dialog but not in a valid Steam Lobby.");
            // Optionally, show a popup to the host: "Steam Invite not available: Not in a Steam Lobby."
        }
    }
}
