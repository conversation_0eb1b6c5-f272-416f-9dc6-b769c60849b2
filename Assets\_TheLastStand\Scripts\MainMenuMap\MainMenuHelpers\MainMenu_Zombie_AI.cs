using UnityEngine;
using System.Collections;
using System.Collections.Generic;

using UnityEngine.AI;

public class MainMenu_Zombie_AI : MonoBehaviour
{
    [SerializeField] private float walkSpeed = 5f;
    [SerializeField] private float runSpeed = 8f;
    [SerializeField] private Animator animator;
    [SerializeField] private ZombieType zombieType = ZombieType.Default;
    [SerializeField] private float walkAnimationScale = 5f;
    [SerializeField] private float runAnimationScale = 5f;
    private ZombieState currentState;
    private ZombieState previousState;
    private Coroutine currentStateCoroutine;
    private NavMeshAgent ZombieAgent;
    private bool canWalk = false;
    private Transform targetWaypoint;
    private bool waypointSet = false;
    private float speedMultiplier = 1f;
    private bool neverRun = false;

    // New public property to check if the zombie should be destroyed
    public bool ShouldBeDestroyed { get; private set; } = false;

    private enum ZombieState
    {
        Initialize,
        Idle,
        Walking,
        Running
    }

    private enum ZombieType
    {
        Default,
        BigZombie
    }
    
    public void Start()
    {
        ChangeZombieState(ZombieState.Initialize);
    }

    public void SetNeverRun(bool value)
    {
        neverRun = value;
    }

    private void ChangeZombieState(ZombieState newState)
    {
        if (currentStateCoroutine != null)
        {
            StopCoroutine(currentStateCoroutine);
        }

        previousState = currentState;
        currentState = newState;

        if (animator != null)
        {
            animator.SetBool("isWalking", newState == ZombieState.Walking);
            animator.SetBool("isRunning", newState == ZombieState.Running);
            
            UpdateAnimationSpeed();
        }

        switch (currentState)
        {
            case ZombieState.Initialize:
                currentStateCoroutine = StartCoroutine(InitializeState());
                break;
            case ZombieState.Idle:
                currentStateCoroutine = StartCoroutine(IdleState());
                break;
            case ZombieState.Walking:
                currentStateCoroutine = StartCoroutine(WalkingState());
                break;
            case ZombieState.Running:
                currentStateCoroutine = StartCoroutine(RunningState());
                break;
        }
    }

    public void SetSpeedMultiplier(float multiplier)
    {
        speedMultiplier = multiplier;
        
        if (ZombieAgentIsReady())
        {
            if (currentState == ZombieState.Walking)
            {
                ZombieAgent.speed = walkSpeed * speedMultiplier;
            }
            else if (currentState == ZombieState.Running)
            {
                ZombieAgent.speed = runSpeed * speedMultiplier;
            }
        }
        
        UpdateAnimationSpeed();
    }

    private void UpdateAnimationSpeed()
    {
        if (animator == null) return;

        float speedMultiplierValue = 1f;
        if (currentState == ZombieState.Walking)
        {
            speedMultiplierValue = (walkSpeed * speedMultiplier) / walkAnimationScale;
        }
        else if (currentState == ZombieState.Running)
        {
            speedMultiplierValue = (runSpeed * speedMultiplier) / runAnimationScale;
        }
        
        animator.SetFloat("MovementSpeed", speedMultiplierValue);
    }

    private IEnumerator InitializeState()
    {
        yield return new WaitUntil(ZombieAgentIsReady);
        yield return new WaitUntil(() => waypointSet);
        canWalk = true;
        ChangeZombieState(ZombieState.Walking);
    }

    private bool ZombieAgentIsReady()
    {
        if (ZombieAgent == null)
        {
            ZombieAgent = GetComponent<NavMeshAgent>();
            if (ZombieAgent != null)
            {
                ZombieAgent.autoRepath = true;
                ZombieAgent.updatePosition = true;
                ZombieAgent.updateRotation = true;
            }
        }
        return ZombieAgent != null && ZombieAgent.isActiveAndEnabled;
    }

    private IEnumerator WalkingState()
    {
        if (ZombieAgentIsReady())
        {
            ZombieAgent.speed = walkSpeed * speedMultiplier;
            UpdateAnimationSpeed();
        }

        if (ZombieAgentIsReady() && WaypointExists())
        {
            ZombieAgent.SetDestination(targetWaypoint.position);
        }
        else
        {
            ChangeZombieState(ZombieState.Idle);
            yield break;
        }

        // Initial delay before considering to run or continue walking
        float initialWaitTime = 0f;
        if (!neverRun && zombieType == ZombieType.Default)
        {
            initialWaitTime = Random.Range(4f, 10f);
        }
        else if (!neverRun && zombieType == ZombieType.BigZombie)
        {
            // Big zombies have a different path to potentially running (via Idle state)
            // This initial wait is just for their walking phase before potentially idling.
            initialWaitTime = Random.Range(6f, 10f);
        }
        
        if (initialWaitTime > 0)
        {
            yield return new WaitForSeconds(initialWaitTime);
        }

        // After initial wait, decide if Default zombie should try to run or continue walking
        if (!neverRun && zombieType == ZombieType.Default)
        {
            if (canWalk && currentState == ZombieState.Walking) // Still in a valid state to transition
            {
                if (Random.Range(0, 100) < 15) // 15% chance to run
                {
                    ChangeZombieState(ZombieState.Running);
                    yield break; // Exit WalkingState as we are now Running
                }
                // Else, it remains in WalkingState, and the loop below will continue
            }
        }
        else if (!neverRun && zombieType == ZombieType.BigZombie)
        {
            // BigZombie logic: after initial walk, transitions to Idle, then Idle decides to run.
            // This part is for its initial walking duration before idling.
            if (canWalk && currentState == ZombieState.Walking)
            {
                ChangeZombieState(ZombieState.Idle);
                yield break;
            }
        }

        // Main walking loop: continue walking until destination or other state change
        while (canWalk && currentState == ZombieState.Walking) // Check currentState again in case it changed above
        {
            if (ZombieAgentIsReady() && WaypointExists() && !ZombieAgent.pathPending)
            {
                if (ZombieAgent.remainingDistance <= ZombieAgent.stoppingDistance)
                {
                    if (!ZombieAgent.hasPath || ZombieAgent.velocity.sqrMagnitude == 0f)
                    {
                        ChangeZombieState(ZombieState.Idle);
                        yield break;
                    }
                }
            }
            yield return null;
        }
    }

    private IEnumerator IdleState()
    {
        if (!neverRun && zombieType == ZombieType.BigZombie && previousState == ZombieState.Walking)
        {
            yield return new WaitForSeconds(2f);
            if (canWalk && currentState == ZombieState.Idle)
            {
                ChangeZombieState(ZombieState.Running);
                yield break;
            }
        }
        yield return null;
    }

    private IEnumerator RunningState()
    {
        if (ZombieAgentIsReady())
        {
            ZombieAgent.speed = runSpeed * speedMultiplier;
            UpdateAnimationSpeed();
        }

        if (ZombieAgentIsReady() && WaypointExists())
        {
            ZombieAgent.SetDestination(targetWaypoint.position);
        }
        else
        {
            ChangeZombieState(ZombieState.Idle);
            yield break;
        }

        while (canWalk)
        {
            if (ZombieAgentIsReady() && WaypointExists() && !ZombieAgent.pathPending)
            {
                if (ZombieAgent.remainingDistance <= ZombieAgent.stoppingDistance)
                {
                    if (!ZombieAgent.hasPath || ZombieAgent.velocity.sqrMagnitude == 0f)
                    {
                        ChangeZombieState(ZombieState.Idle);
                        yield break;
                    }
                }
            }
            yield return null;
        }
    }

    public void SetWaypoint(Transform waypoint)
    {
        targetWaypoint = waypoint;
        waypointSet = true;

        if (ZombieAgentIsReady() && WaypointExists())
        {
            if (currentState == ZombieState.Idle || currentState == ZombieState.Walking)
            {
                ChangeZombieState(ZombieState.Walking);
            }
            ZombieAgent.SetDestination(targetWaypoint.position);
        }
    }

    private bool WaypointExists()
    {
      return targetWaypoint != null;
    }

    public void CheckAndDestroyIfOutOfBounds(float xThreshold)
    {
        if (ShouldBeDestroyed) return; // Already marked for destruction

        if (transform.position.x > xThreshold)
        {
            Debug.Log($"Zombie {gameObject.name} at {transform.position.x} is beyond threshold {xThreshold}. Destroying.");
            ShouldBeDestroyed = true; // Mark for destruction
            canWalk = false; // Stop AI processing
            if (currentStateCoroutine != null)
            {
                StopCoroutine(currentStateCoroutine);
            }
            if (ZombieAgent != null && ZombieAgent.isOnNavMesh)
            {
                ZombieAgent.isStopped = true;
                ZombieAgent.ResetPath();
            }
            Destroy(gameObject); // Destroy the GameObject
        }
    }
}
