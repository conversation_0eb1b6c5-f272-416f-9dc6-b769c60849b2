using UnityEngine;
using System.Collections;
using System.Collections.Generic;

public class MainMenu_Zombie_Spawner : MonoBehaviour
{
    [SerializeField] private List<GameObject> zombiePrefabs;
    [SerializeField] private List<Transform> waypointStartPositions;
    [SerializeField] private List<Transform> waypointEndPositions;
    [SerializeField] private int spawnDelayMin = 1;
    [SerializeField] private int spawnDelayMax = 3;
    [SerializeField] private float scaleMin = 0.8f;
    [SerializeField] private float scaleMax = 1.2f;
    private List<MainMenu_Zombie_AI> spawnedZombies;
    private Coroutine spawnCoroutine;

    private enum SpawnerState
    {
        InitialDelay,
        Spawning
    }
    private SpawnerState currentState;
    private const float X_THRESHOLD = 460f;
    private const float CHECK_INTERVAL = 2f;

    private void Start()
    {
        spawnedZombies = new List<MainMenu_Zombie_AI>();
        ChangeSpawnerState(SpawnerState.InitialDelay);
    }

    private void ChangeSpawnerState(SpawnerState newState)
    {
        if (spawnCoroutine != null)
        {
            StopCoroutine(spawnCoroutine);
        }

        currentState = newState;

        switch (currentState)
        {
            case SpawnerState.InitialDelay:
                spawnCoroutine = StartCoroutine(InitialDelayState());
                break;
            case SpawnerState.Spawning:
                spawnCoroutine = StartCoroutine(SpawningState());
                break;
        }
    }

    private IEnumerator InitialDelayState()
    {
        yield return new WaitForSeconds(3f);
        ChangeSpawnerState(SpawnerState.Spawning);
    }

    private IEnumerator SpawningState()
    {
        while (true)
        {
            SpawnRandomZombie();
            yield return new WaitForSeconds(Random.Range(spawnDelayMin, spawnDelayMax));
        }
    }

    // New coroutine to handle periodic checks
    private IEnumerator PeriodicCheckState()
    {
        while(true)
        {
            yield return new WaitForSeconds(CHECK_INTERVAL);
            CheckAndCleanupZombies();
        }
    }

    private void OnEnable() // Or Start, if this object is always active
    {
        if (spawnedZombies == null) // Should be initialized in Start
        {
            spawnedZombies = new List<MainMenu_Zombie_AI>();
        }
        ChangeSpawnerState(SpawnerState.InitialDelay); // Start the main spawning logic
        StartCoroutine(PeriodicCheckState()); // Start the independent check coroutine
    }

    private void OnDisable()
    {
        StopAllCoroutines(); // Stop both spawning and checking when disabled
    }

    private void CheckAndCleanupZombies()
    {
        if (spawnedZombies == null || spawnedZombies.Count == 0) return;

        // Iterate backwards because we're removing items from the list
        for (int i = spawnedZombies.Count - 1; i >= 0; i--)
        {
            MainMenu_Zombie_AI zombie = spawnedZombies[i];
            if (zombie == null || zombie.ShouldBeDestroyed) // Zombie might have been destroyed by itself or already marked
            {
                spawnedZombies.RemoveAt(i);
                continue;
            }
            zombie.CheckAndDestroyIfOutOfBounds(X_THRESHOLD);
            // If CheckAndDestroyIfOutOfBounds marked it for destruction and destroyed it,
            // it might become null immediately or its ShouldBeDestroyed flag will be true on next iteration.
            // To be absolutely safe and remove immediately:
            if (zombie.ShouldBeDestroyed) // Check again after calling, as it sets the flag before Destroy()
            {
                // The zombie's Destroy() call will happen, Unity cleans it up.
                // We just need to remove it from our tracking list.
                spawnedZombies.RemoveAt(i);
            }
        }
    }

    private void SpawnRandomZombie()
    {
        if (zombiePrefabs.Count == 0 || waypointStartPositions.Count == 0 || waypointEndPositions.Count == 0) return;

        GameObject randomPrefab = zombiePrefabs[Random.Range(0, zombiePrefabs.Count)];
        Transform randomStart = waypointStartPositions[Random.Range(0, waypointStartPositions.Count)];
        Transform randomEnd = waypointEndPositions[Random.Range(0, waypointEndPositions.Count)];

        float scaleMultiplier = Random.Range(scaleMin, scaleMax);
        
        GameObject zombieInstance = Instantiate(randomPrefab, randomStart.position, randomStart.rotation);
        
        Vector3 originalScale = randomPrefab.transform.localScale;
        zombieInstance.transform.localScale = originalScale * scaleMultiplier;

        MainMenu_Zombie_AI zombieAI = zombieInstance.GetComponent<MainMenu_Zombie_AI>();
        
        if (zombieAI != null)
        {
            zombieAI.SetSpeedMultiplier(scaleMultiplier);
            
            bool neverRun = Random.Range(0, 100) < 25;
            if (neverRun)
            {
                zombieAI.SetNeverRun(true);
            }
            
            zombieAI.SetWaypoint(randomEnd);
            spawnedZombies.Add(zombieAI);
        }
    }
}
