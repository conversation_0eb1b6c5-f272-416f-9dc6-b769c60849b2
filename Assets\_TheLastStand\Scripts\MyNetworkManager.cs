using Mirror;
using Steamworks;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;

namespace TheLastStand
{
    [RequireComponent(typeof(LobbyPlayerList))]
    public class MyNetworkManager : NetworkManager
    {
        public static bool isMulitplayer;
        public static MyNetworkManager instance; // Static instance
        
        [Header("Lobby Setup")]
        [SerializeField] private LobbyPlayerList lobbyPlayerListReference; // Drag the LobbyPlayerList GameObject here

        public override void Awake()
        {
            base.Awake();
            
            bool IsFirstInstance = instance == null;
            if (IsFirstInstance)
            {
                instance = this;
                
                bool HasParent = transform.parent != null;
                if (HasParent)
                {
                    transform.SetParent(null);
                }
                
                DontDestroyOnLoad(gameObject);
            }
            else 
            {
                Destroy(gameObject); 
                return;
            }
            // EnsureLobbyPlayerListExists(); // Removed: LobbyPlayerList is now a required component and handles its own instance.
        }

        public override void OnStartServer()
        {
            base.OnStartServer();
            // EnsureLobbyPlayerListExists(); // Removed: LobbyPlayerList should be ready.
        }

        private void EnsureLobbyPlayerListExists()
        {
            if (LobbyPlayerList.instance == null)
            {
                LobbyPlayerList listComponent = GetComponent<LobbyPlayerList>();
                if (listComponent == null)
                {
                    Debug.LogWarning("MyNetworkManager: LobbyPlayerList component not found on NetworkManager GameObject. Adding it automatically...");
                    
                    // Automatically add the missing component
                    listComponent = gameObject.AddComponent<LobbyPlayerList>();
                    Debug.Log("MyNetworkManager: LobbyPlayerList component added successfully.");
                }
                
                // The LobbyPlayerList handles its own singleton instance management in Awake()
                // No need to manually set the instance here
                
                if (LobbyPlayerList.instance != null)
                {
                    Debug.Log("MyNetworkManager: LobbyPlayerList.instance is now available.");
                }
                else
                {
                    Debug.LogError("MyNetworkManager: Failed to ensure LobbyPlayerList.instance exists. Critical error.");
                }
            }
            else
            {
                Debug.Log("MyNetworkManager: LobbyPlayerList.instance already exists.");
            }
        }

        public override void OnServerAddPlayer(NetworkConnectionToClient conn)
        {
            // Get start position from NetworkManager
            Transform startPos = GetStartPosition();
            GameObject player;

            if (startPos != null)
            {
                player = Instantiate(playerPrefab, startPos);
            }
            else
            {
                player = Instantiate(playerPrefab);
                Debug.LogWarning("MyNetworkManager: Player start position not found. Player spawned at world origin.");
            }

            // Assign the player slot ID before spawning
            NetworkBehaviour client = player.GetComponent<NetworkBehaviour>(); // Keep as NetworkBehaviour for now
            if (client == null)
            {
                Debug.LogError("MyNetworkManager: playerPrefab does not have a NetworkBehaviour component!");
                Destroy(player); // Destroy the invalid player object
                return;
            }
            {
                int slotId = LobbyPlayerList.instance.GetNextSlotId();
                
                // Assign slot ID to PlayerIdentity component
                NetworkBehaviour playerIdentity = player.GetComponent<NetworkBehaviour>();
                if (playerIdentity != null)
                {
                    // TODO: Restore when MyClient/PlayerIdentity compilation is fixed
                    // playerIdentity.Server_SetSlotId(slotId);
                    Debug.Log($"MyNetworkManager: Would set slot ID {slotId} for player");
                }
                else
                {
                    Debug.LogError("MyNetworkManager: PlayerIdentity component not found on player prefab!");
                }
                
                // Try to add the client to the lobby immediately
                if (LobbyPlayerList.instance != null)
                {
                    if (NetworkServer.active && LobbyPlayerList.instance.netIdentity != null && LobbyPlayerList.instance.isServer)
                    {
                        if (LobbyPlayerList.instance.allClients != null)
                        {
                            LobbyPlayerList.instance.allClients.Add(client); // client is already MyClient, implicit cast to NetworkBehaviour
                            Debug.Log($"MyNetworkManager: Added client to lobby list. Total clients: {LobbyPlayerList.instance.allClients.Count}");
                        }
                        else
                        {
                            Debug.LogError("MyNetworkManager: LobbyPlayerList.instance.allClients is null despite netIdentity and isServer being valid. Deferring.");
                            StartCoroutine(DeferredAddClientToLobby(client));
                        }
                    }
                    else
                    {
                        Debug.LogError("LobbyPlayerList.instance is not fully ready to add client. Deferring operation.");
                        StartCoroutine(DeferredAddClientToLobby(client));
                    }
                }
                else
                {
                    Debug.LogError("MyNetworkManager: LobbyPlayerList.instance is null. Cannot add client to list.");
                }
            }

            // Set player information and avatar
            if (conn.authenticationData is ulong authUlongSteamIdValue && authUlongSteamIdValue != 0)
            {
                CSteamID steamId = new CSteamID(authUlongSteamIdValue);
                SetPlayerInfoAndAvatar(client, steamId, conn);
            }

            // Spawn the player for all clients
            NetworkServer.AddPlayerForConnection(conn, player);
        }

        private IEnumerator DeferredAddClientToLobby(NetworkBehaviour client)
        {
            yield return new WaitForSeconds(0.1f);
            
            if (LobbyPlayerList.instance != null && client != null)
            {
                if (NetworkServer.active && LobbyPlayerList.instance.netIdentity != null && LobbyPlayerList.instance.isServer)
                {
                    if (LobbyPlayerList.instance.allClients != null)
                    {
                        LobbyPlayerList.instance.allClients.Add(client);
                        Debug.Log($"MyNetworkManager: Deferred add client to lobby successful. Total clients: {LobbyPlayerList.instance.allClients.Count}");
                    }
                    else
                    {
                        Debug.LogError("MyNetworkManager: Deferred add failed - LobbyPlayerList.instance.allClients is null");
                    }
                }
                else
                {
                    Debug.LogError("MyNetworkManager: Deferred add failed - LobbyPlayerList not ready for server operations");
                }
            }
            else
            {
                Debug.LogError("MyNetworkManager: Deferred add failed - LobbyPlayerList.instance or client is null");
            }
        }

        private void SetPlayerInfoAndAvatar(NetworkBehaviour client, CSteamID steamId, NetworkConnectionToClient conn)
        {
            if (client != null)
            {
                // TODO: Cast NetworkBehaviour to MyClient and set player info/avatar
                // Temporarily disabled due to assembly compilation ordering issues with MyClient type
                Debug.Log("MyNetworkManager: SetPlayerInfoAndAvatar temporarily disabled due to compilation issues");
            }
        }

        public override void OnServerDisconnect(NetworkConnectionToClient conn)
        {
            // TEMPORARY: Simplified disconnect handling - will restore full functionality after compilation issues resolved
            if (conn.identity != null)
            {
                Debug.Log($"MyNetworkManager: Player disconnected - simplified version");
                // TODO: Restore MyClient removal from LobbyPlayerList after compilation issues resolved
            }
            else
            {
                Debug.Log("MyNetworkManager.OnServerDisconnect: Connection identity is null. This is normal during shutdown.");
            }

            base.OnServerDisconnect(conn);
        }

        public override void OnStartClient()
        {
            if (isMulitplayer) 
            {
                // MainMenu.instance.SetMenuState(MenuState.InParty);
                // PopupManager.instance.Popup_Close();
            }

            base.OnStartClient();
        }

        public override void OnStopClient()
        {
            if (isMulitplayer)
            {
                if (LobbyPlayerList.instance != null && NetworkClient.active)
                {
                    Debug.Log("MyNetworkManager: OnStopClient - Clearing LobbyPlayerList.instance");
                    LobbyPlayerList.instance.allClients.Clear();
                }
                // MainMenu.instance.SetMenuState(MenuState.Home);
            }

            base.OnStopClient();
        }

        public override void OnStopServer()
        {
            if (isMulitplayer)
            {
                // Safely clear the lobby player list
                if (LobbyPlayerList.instance != null && 
                    LobbyPlayerList.instance.netIdentity != null && 
                    LobbyPlayerList.instance.allClients != null)
                {
                    try
                    {
                        // Only clear if the NetworkBehaviour is still valid and server-side
                        if (LobbyPlayerList.instance.isServer)
                        {
                            LobbyPlayerList.instance.allClients.Clear();
                            Debug.Log("MyNetworkManager: Cleared LobbyPlayerList.allClients during OnStopServer.");
                        }
                    }
                    catch (System.Exception e)
                    {
                        Debug.LogWarning($"MyNetworkManager: Exception while clearing LobbyPlayerList during OnStopServer: {e.Message}");
                    }
                }
                else
                {
                    Debug.Log("MyNetworkManager: LobbyPlayerList not available or not ready during OnStopServer - skipping clear.");
                }
            }
            
            base.OnStopServer();
        }

        public void SetMultiplayer(bool value)
        {
            isMulitplayer = value;

            if (isMulitplayer) 
                NetworkServer.dontListen = false;
            else 
                NetworkServer.dontListen = true;
        }
    }
}
