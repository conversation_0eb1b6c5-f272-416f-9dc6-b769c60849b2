using Mirror;
using Steamworks;
using System;
using UnityEngine;

namespace TheLastStand
{
    [System.Serializable]
    public struct PlayerInfoData
    {
        public string username;
        public ulong steamId;

        public PlayerInfoData(string username, ulong steamId)
        {
            this.username = username;
            this.steamId = steamId;
        }
    }

    public class MyClient : NetworkBehaviour
    {
        public static MyClient localClient;

        [SyncVar(hook = nameof(OnPlayerInfoChanged))]
        public PlayerInfoData playerInfo = new PlayerInfoData("", 0);

        [SyncVar(hook = nameof(OnReadyChanged))]
        public bool isReady = false;

        [SyncVar(hook = nameof(OnAvatarDataChanged))]
        public byte[] avatarData;

        [SyncVar]
        public int characterSkinIndex = 0;

        public Sprite icon;

        public event Action OnClientUpdated;

        public bool IsReady => isReady;

        public override void OnStartLocalPlayer()
        {
            base.OnStartLocalPlayer();
            localClient = this;
            Debug.Log($"MyClient: Local player started. Username: {playerInfo.username}");
        }

        public override void OnStartClient()
        {
            base.OnStartClient();
            
            if (isLocalPlayer)
            {
                UpdateReadyButtonUI();
            }

            if (CharacterSkinHandler.instance) 
            {
                CharacterSkinHandler.instance.SpawnCharacterMesh(this);
            }
        }

        public override void OnStopClient()
        {
            base.OnStopClient();
            
            if (localClient == this)
            {
                localClient = null;
            }

            if (CharacterSkinHandler.instance)
            {
                CharacterSkinHandler.instance.DestroyCharacterMesh(this);
            }
        }

        [Command]
        public void CmdToggleReady()
        {
            isReady = !isReady;
            Debug.Log($"MyClient: Player {playerInfo.username} is now {(isReady ? "ready" : "not ready")}");
        }

        [Command]
        public void CmdSetCharacterSkin(int skinIndex)
        {
            characterSkinIndex = skinIndex;
        }

        public void ToggleReady()
        {
            if (!isLocalPlayer) return;
            CmdToggleReady();
        }

        public void SetCharacterSkin(int skinIndex)
        {
            if (!isLocalPlayer) return;
            CmdSetCharacterSkin(skinIndex);
        }

        public void NextCharacterSkin()
        {
            if (!isLocalPlayer) return;
            
            if (CharacterSkinHandler.instance)
            {
                int nextIndex = CharacterSkinHandler.instance.GetNextPlatformIndex(this);
                SetCharacterSkin(nextIndex);
            }
        }

        private void OnPlayerInfoChanged(PlayerInfoData oldInfo, PlayerInfoData newInfo)
        {
            Debug.Log($"MyClient: Player info changed from '{oldInfo.username}' to '{newInfo.username}'");
            OnClientUpdated?.Invoke();
        }

        private void OnReadyChanged(bool oldReady, bool newReady)
        {
            Debug.Log($"MyClient: Ready state changed from {oldReady} to {newReady} for player {playerInfo.username}");
            
            if (isLocalPlayer)
            {
                UpdateReadyButtonUI();
            }
            
            OnClientUpdated?.Invoke();
        }

        private void OnAvatarDataChanged(byte[] oldData, byte[] newData)
        {
            if (newData != null && newData.Length > 0)
            {
                Texture2D avatarTexture = new Texture2D(1, 1);
                if (avatarTexture.LoadImage(newData))
                {
                    icon = Sprite.Create(avatarTexture, new Rect(0, 0, avatarTexture.width, avatarTexture.height), Vector2.zero);
                }
            }
            else
            {
                icon = null;
            }
            
            OnClientUpdated?.Invoke();
        }

        private void UpdateReadyButtonUI()
        {
            if (MainMenu.instance != null)
            {
                MainMenu.instance.UpdateReadyButton(isReady);
            }
        }

        public void SetPlayerInfo(string username, ulong steamId)
        {
            if (isServer)
            {
                playerInfo = new PlayerInfoData(username, steamId);
            }
        }

        public void SetAvatarData(byte[] data)
        {
            if (isServer)
            {
                avatarData = data;
            }
        }
    }
}