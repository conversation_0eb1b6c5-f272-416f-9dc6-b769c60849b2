using Mirror;
using UnityEngine;

namespace TheLastStand
{
    public class PlayerIdentity : NetworkBehaviour
    {
        [SyncVar(hook = nameof(OnSlotIdChanged))]
        private int playerSlotId = -1;

        public int PlayerSlotId 
        { 
            get { return playerSlotId; } 
            set 
            { 
                if (isServer) 
                {
                    playerSlotId = value; 
                }
            } 
        }

        [Server]
        public void Server_SetSlotId(int slotId)
        {
            if (!isServer) return;
            
            PlayerSlotId = slotId;
            Debug.Log($"PlayerIdentity: Set slot ID {slotId} for player {netId}");
        }

        private void OnSlotIdChanged(int oldSlotId, int newSlotId)
        {
            Debug.Log($"PlayerIdentity: Player {netId} slot ID changed from {oldSlotId} to {newSlotId}");
        }

        public override void OnStartClient()
        {
            base.OnStartClient();
            Debug.Log($"PlayerIdentity: Client started for player {netId} with slot ID {PlayerSlotId}");
        }
    }
}
