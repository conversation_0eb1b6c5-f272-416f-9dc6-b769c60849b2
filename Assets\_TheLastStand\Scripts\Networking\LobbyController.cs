using Mirror;
using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;

namespace TheLastStand
{
    public class LobbyController : MonoBehaviour
    {
        public static LobbyController instance;

        private void Awake()
        {
            instance = this;
        }


        public void StartGameWithParty() 
        {
            if (!NetworkServer.active) 
            {
                return;
            }

            if (AllPlayersReady()) 
            {
                StartGame();
            }
           
        }


        public void StartGameSolo()
        {
            StartCoroutine(StartSinglePlayer());
        }
        IEnumerator StartSinglePlayer() 
        {
            NetworkManager.singleton.StartHost();

            while(NetworkClient.localPlayer == null)
                yield return new WaitForEndOfFrame();

            ((MyNetworkManager)NetworkManager.singleton).SetMultiplayer(false);
            StartGame();
        }

        private void StartGame()
        {
            if (NetworkServer.active)
            {
                if (MainMenu.instance != null)
                {
                    Destroy(MainMenu.instance.gameObject);
                }
                
                NetworkManager.singleton.ServerChangeScene("Map_01");
            }
        }

        private bool AllPlayersReady() 
        {
            if (LobbyPlayerList.instance == null)
            {
                Debug.LogError("LobbyController: LobbyPlayerList.instance is null. Cannot check if all players are ready.");
                return false; 
            }
            
            if (LobbyPlayerList.instance.allClients.Count == 0)
            {
                return false; 
            }

            foreach (MyClient client in LobbyPlayerList.instance.allClients)
            {
                if (client == null || !client.IsReady)
                {
                    return false;
                }
            }
            return true;
        }
    }
}
