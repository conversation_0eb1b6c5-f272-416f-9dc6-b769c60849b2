using System.Collections.Generic;
using Mirror;
using UnityEngine;

namespace TheLastStand
{
    public class LobbyPlayerList : NetworkBehaviour
    {
        public static LobbyPlayerList instance { get; private set; }

        // TEMPORARY: Using NetworkBehaviour instead of MyClient to resolve compilation issues
        // Will restore MyClient after compilation order is resolved
        public readonly SyncList<NetworkBehaviour> allClients = new SyncList<NetworkBehaviour>();

        private void Awake()
        {
            if (instance == null)
            {
                instance = this;
                Debug.Log($"LobbyPlayerList: Instance set on {gameObject.name}");
                // If this LobbyPlayerList is on the NetworkManager GameObject,
                // and NetworkManager is DontDestroyOnLoad, this component will also persist.
            }
            else if (instance != this)
            {
                // An instance already exists. Determine which one should survive.
                // The one on the NetworkManager GameObject should be the definitive singleton.
                bool thisIsOnNetworkManager = 
                    NetworkManager.singleton != null &&
                    NetworkManager.singleton.gameObject == this.gameObject &&
                    NetworkManager.singleton is MyNetworkManager;

                if (thisIsOnNetworkManager)
                {
                    // This LPL is on the NetworkManager GameObject. It should be the singleton.
                    // If the existing 'instance' is not this one, destroy the GameObject of the old 'instance'.
                    if (instance.gameObject != this.gameObject) // Check to avoid destroying self if instance somehow pointed to self
                    {
                        Debug.LogWarning($"LobbyPlayerList: Existing instance on {instance.gameObject.name} is being replaced by instance on NetworkManager ({this.gameObject.name}). Destroying old instance's GameObject.");
                        Destroy(instance.gameObject); // Destroy the GameObject of the OLD singleton.
                    }
                    instance = this; // Ensure 'this' (on NM) is the singleton.
                    Debug.Log(
                        $"LobbyPlayerList: Instance confirmed/set on NetworkManager GameObject {gameObject.name}"
                    );
                }
                else
                {
                    // 'this' is not on the NetworkManager GameObject, and an 'instance' already exists.
                    // This 'this' is a duplicate. Destroy its GameObject.
                    Debug.LogWarning($"LobbyPlayerList: Duplicate instance on {gameObject.name} found. Existing instance is on {instance.gameObject.name}. Destroying GameObject of this duplicate ({this.gameObject.name}).");
                    Destroy(gameObject);
                }
            }
            // If instance == this, do nothing, already correctly set.
        }

        public override void OnStartServer()
        {
            base.OnStartServer();
            // The SyncList is now active on the server.
            // You could perform any server-specific list initialization here if needed.
            Debug.Log("LobbyPlayerList.OnStartServer: List is active.");
        }

        public override void OnStartClient()
        {
            base.OnStartClient();
            // Subscribe to SyncList changes to update UI or other client-side logic
            if (allClients != null)
            {
                allClients.Callback += OnAllClientsChanged;
                // Initial UI update based on current list content
                RefreshPlayerDisplay();
            }
            Debug.Log("LobbyPlayerList.OnStartClient: Subscribed to allClients changes.");
        }

        public override void OnStopServer()
        {
            base.OnStopServer();
            // This check is important. If the object is destroyed before Mirror cleans up,
            // 'allClients' might be null or its internal state invalid.
            if (allClients != null)
            {
                allClients.Clear(); // Ensure list is cleared if server stops
            }
            Debug.Log("LobbyPlayerList.OnStopServer: Cleared allClients.");
        }

        public override void OnStopClient()
        {
            base.OnStopClient();
            // Unsubscribe when the client stops or the object is destroyed
            if (allClients != null)
            {
                allClients.Callback -= OnAllClientsChanged;
            }
            Debug.Log("LobbyPlayerList.OnStopClient: Unsubscribed from allClients changes.");
        }

        // TEMPORARY: Updated signature for NetworkBehaviour instead of MyClient
        private void OnAllClientsChanged(
            SyncList<NetworkBehaviour>.Operation op,
            int itemIndex,
            NetworkBehaviour oldItem,
            NetworkBehaviour newItem
        )
        {
            Debug.Log(
                $"LobbyPlayerList.OnAllClientsChanged - Operation: {op}, Index: {itemIndex}, NewItem: {newItem?.name ?? "N/A"}"
            );
            RefreshPlayerDisplay();
        }

        // Example method to update UI (you'll need your actual UI logic here)
        public void RefreshPlayerDisplay()
        {
            if (!isClient) 
                return; // UI updates are typically client-side

            // Debug.Log("LobbyPlayerList: Refreshing player display. Current player count: " + allClients.Count);
            // --- Your UI update logic goes here ---
            // For example, iterate through 'allClients' and update UI elements for each player.
            // foreach (MyClient client in allClients)
            // {
            //    // Access client.PlayerName, client.IsReady, etc. to update UI
            // }
        }

        /// <summary>
        /// Get the next available slot ID for a new player
        /// </summary>
        public int GetNextSlotId()
        {
            // Return the count of current clients as the next slot ID
            // This ensures sequential slot assignment (0, 1, 2, 3...)
            int nextSlotId = allClients.Count;
            Debug.Log($"LobbyPlayerList.GetNextSlotId: Assigning slot ID {nextSlotId}");
            return nextSlotId;
        }
    }
}