using Steamworks;
using System.Collections.Generic;
using UnityEngine;

namespace TheLastStand
{
    public static class SteamHelper
    {
        public static Texture2D GetAvatar(CSteamID steamId)
        {
            if (!SteamManager.Initialized)
            {
                Debug.LogWarning("SteamHelper: Steam not initialized, cannot get avatar");
                return null;
            }

            if (!steamId.IsValid())
            {
                Debug.LogWarning("SteamHelper: Invalid Steam ID provided");
                return null;
            }

            int avatarInt = SteamFriends.GetLargeFriendAvatar(steamId);
            
            if (avatarInt == -1)
            {
                Debug.LogWarning($"SteamHelper: No avatar found for Steam ID {steamId}");
                return null;
            }

            uint width, height;
            bool success = SteamUtils.GetImageSize(avatarInt, out width, out height);
            
            if (!success || width == 0 || height == 0)
            {
                Debug.LogWarning($"SteamHelper: Invalid avatar size for Steam ID {steamId}");
                return null;
            }

            byte[] avatarData = new byte[width * height * 4];
            success = SteamUtils.GetImageRGBA(avatarInt, avatarData, (int)(width * height * 4));
            
            if (!success)
            {
                Debug.LogWarning($"SteamHelper: Failed to get avatar RGBA data for Steam ID {steamId}");
                return null;
            }

            Texture2D avatarTexture = new Texture2D((int)width, (int)height, TextureFormat.RGBA32, false);
            avatarTexture.LoadRawTextureData(avatarData);
            avatarTexture.Apply();

            FlipTextureVertically(avatarTexture);

            return avatarTexture;
        }

        private static void FlipTextureVertically(Texture2D original)
        {
            Color32[] originalPixels = original.GetPixels32();
            Color32[] flippedPixels = new Color32[originalPixels.Length];

            int width = original.width;
            int height = original.height;

            for (int i = 0; i < height; i++)
            {
                for (int j = 0; j < width; j++)
                {
                    flippedPixels[i * width + j] = originalPixels[(height - i - 1) * width + j];
                }
            }

            original.SetPixels32(flippedPixels);
            original.Apply();
        }

        public static string GetPlayerName(CSteamID steamId)
        {
            if (!SteamManager.Initialized)
            {
                return "Unknown Player";
            }

            if (!steamId.IsValid())
            {
                return "Invalid Player";
            }

            return SteamFriends.GetFriendPersonaName(steamId);
        }

        public static bool IsPlayerOnline(CSteamID steamId)
        {
            if (!SteamManager.Initialized || !steamId.IsValid())
            {
                return false;
            }

            EPersonaState state = SteamFriends.GetFriendPersonaState(steamId);
            return state != EPersonaState.k_EPersonaStateOffline;
        }

        public static Sprite ConvertTextureToSprite(Texture2D texture)
        {
            return Sprite.Create(texture, new Rect(0, 0, texture.width, texture.height), Vector2.zero);
        }

        public static List<CSteamID> GetMembersInLobby()
        {
            List<CSteamID> members = new List<CSteamID>();
            int memberAmount = SteamMatchmaking.GetNumLobbyMembers(SteamLobby.LobbyID);

            for (int i = 0; i < memberAmount; i++)
            {
                CSteamID cSteamID = SteamMatchmaking.GetLobbyMemberByIndex(SteamLobby.LobbyID, i);
                members.Add(cSteamID);
            }

            return members;
        }
    }
}
