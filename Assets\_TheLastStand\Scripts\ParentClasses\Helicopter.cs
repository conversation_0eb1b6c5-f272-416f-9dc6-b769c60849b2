using UnityEngine;
using System.Collections;
using System.Collections.Generic;

public class Helicopter : MonoBehaviour
{
    [SerializeField] private GameObject helicopterModel;

    [Header("Navigation Settings")]
    [SerializeField] protected List<Transform> waypoints;
    [SerializeField] protected float moveSpeed = 10f;
    [SerializeField] protected float rotationSpeed = 2f;
    [SerializeField] protected float waitTimeAtWaypoint = 1f;
    [SerializeField] protected float landingSpeedFactor = 0.5f;

    protected enum HelicopterState
    {
        Idle,
        InitializingPath,
        Rotating,
        MovingToWaypoint,
        Landing,
        WaitingAtWaypoint,
        PathComplete
    }

    protected HelicopterState currentState = HelicopterState.Idle;
    protected List<Transform> currentPathWaypoints;
    protected int currentWaypointIndex;
    protected Transform activeTargetWaypoint;
    protected float pathSpecificLandingTime;

    private Coroutine _stateMachineExecutionCoroutine;

    public virtual void Start()
    {
        TransitionToState(HelicopterState.Idle);
    }

    public virtual void BeginNavigation(List<Transform> waypointsToFollow, float landingTimeForThisPath)
    {
        Debug.Log($"Helicopter: BeginNavigation called with {waypointsToFollow?.Count ?? 0} waypoints", this);
        
        if (waypointsToFollow == null || waypointsToFollow.Count == 0)
        {
            Debug.LogError("Helicopter: Cannot begin navigation - no waypoints provided!", this);
            return;
        }
        
        if (_stateMachineExecutionCoroutine != null)
        {
            Debug.Log("Helicopter: Stopping previous navigation coroutine", this);
            StopCoroutine(_stateMachineExecutionCoroutine);
        }
        
        this.currentPathWaypoints = waypointsToFollow;
        this.pathSpecificLandingTime = landingTimeForThisPath;
        TransitionToState(HelicopterState.InitializingPath);
        _stateMachineExecutionCoroutine = StartCoroutine(StateMachineDriver());
        
        Debug.Log("Helicopter: Navigation state machine started", this);
    }

    protected void TransitionToState(HelicopterState newState)
    {
        currentState = newState;
    }

    private IEnumerator StateMachineDriver()
    {
        while (ShouldContinueStateMachine())
        {
            yield return StartCoroutine(ExecuteCurrentStateAction());
            yield return null; 
        }

        HandleStateMachineCompletion();
    }

    private bool ShouldContinueStateMachine()
    {
        return currentState != HelicopterState.Idle && currentState != HelicopterState.PathComplete;
    }

    private IEnumerator ExecuteCurrentStateAction()
    {
        switch (currentState)
        {
            case HelicopterState.InitializingPath:
                yield return StartCoroutine(Execute_InitializingPathState());
                break;
            case HelicopterState.Rotating:
                yield return StartCoroutine(Execute_RotatingState());
                break;
            case HelicopterState.MovingToWaypoint:
                yield return StartCoroutine(Execute_MovingToWaypointState());
                break;
            case HelicopterState.Landing:
                yield return StartCoroutine(Execute_LandingState());
                break;
            case HelicopterState.WaitingAtWaypoint:
                yield return StartCoroutine(Execute_WaitingAtWaypointState());
                break;
        }
    }

    private void HandleStateMachineCompletion()
    {
        if (currentState == HelicopterState.PathComplete)
        {
            Debug.Log("Helicopter path successfully completed.", this);
            TransitionToState(HelicopterState.Idle);
        }
        _stateMachineExecutionCoroutine = null;
    }

    private bool AreWaypointsValid()
    {
        if (currentPathWaypoints == null || currentPathWaypoints.Count == 0)
        {
            Debug.LogError("No waypoints provided for navigation.", this);
            return false;
        }

        for (int i = 0; i < currentPathWaypoints.Count; i++)
        {
            if (currentPathWaypoints[i] == null)
            {
                Debug.LogError($"Waypoint at index {i} is null. Aborting path initialization.", this);
                return false;
            }
        }
        return true;
    }

    protected virtual IEnumerator Execute_InitializingPathState()
    {
        if (!AreWaypointsValid())
        {
            TransitionToState(HelicopterState.Idle);
            yield break;
        }

        currentWaypointIndex = 0;
        
        Vector3 currentPosition = transform.position;
        Vector3 targetPosition = currentPathWaypoints[0].position;
        
        float distanceToFirstWaypoint = Vector3.Distance(currentPosition, targetPosition);
        if (distanceToFirstWaypoint > 1.0f)
        {
            Debug.Log($"Helicopter moving to first waypoint. Distance: {distanceToFirstWaypoint}", this);
            yield return StartCoroutine(PerformMovement(currentPathWaypoints[0]));
        }
        else
        {
            Debug.Log("Helicopter already near first waypoint, skipping repositioning.", this);
        }

        if (currentPathWaypoints.Count == 1)
        {
            Debug.Log("Path has only one waypoint. Helicopter positioned. Path considered complete.", this);
            TransitionToState(HelicopterState.PathComplete);
            yield break;
        }

        activeTargetWaypoint = currentPathWaypoints[currentWaypointIndex + 1];
        TransitionToState(HelicopterState.Rotating);
    }

    private IEnumerator PerformRotation(Transform target)
    {
        Vector3 directionToTarget = target.position - transform.position;
        directionToTarget.y = 0f;

        if (directionToTarget.sqrMagnitude > 0.001f)
        {
            Quaternion targetRotation = Quaternion.LookRotation(directionToTarget.normalized);
            
            while (Quaternion.Angle(transform.rotation, targetRotation) > 1.0f)
            {
                transform.rotation = Quaternion.Slerp(transform.rotation, targetRotation, rotationSpeed * Time.deltaTime);
                yield return null;
            }
            
            transform.rotation = targetRotation;
        }
    }

    protected virtual IEnumerator Execute_RotatingState()
    {
        if (activeTargetWaypoint == null)
        {
            Debug.LogError("Rotation state: Target waypoint is null.", this);
            TransitionToState(HelicopterState.Idle);
            yield break;
        }

        yield return StartCoroutine(PerformRotation(activeTargetWaypoint));

        if (IsApproachingFinalSegment())
        {
            TransitionToState(HelicopterState.Landing);
        }
        else
        {
            TransitionToState(HelicopterState.MovingToWaypoint);
        }
    }

    private bool IsApproachingFinalSegment()
    {
        return currentWaypointIndex + 1 >= currentPathWaypoints.Count - 1;
    }

    private IEnumerator PerformMovement(Transform target)
    {
        while (Vector3.Distance(transform.position, target.position) > 0.1f)
        {
            Vector3 directionToTarget = target.position - transform.position;
            directionToTarget.y = 0f;
            
            if (directionToTarget.sqrMagnitude > 0.001f)
            {
                Quaternion targetRotation = Quaternion.LookRotation(directionToTarget.normalized);
                transform.rotation = Quaternion.Slerp(transform.rotation, targetRotation, rotationSpeed * Time.deltaTime);
            }
            
            transform.position = Vector3.MoveTowards(transform.position, target.position, moveSpeed * Time.deltaTime);
            yield return null;
        }
        transform.position = target.position;
    }

    protected virtual IEnumerator Execute_MovingToWaypointState()
    {
        if (activeTargetWaypoint == null)
        {
            Debug.LogError("Moving state: Target waypoint is null.", this);
            TransitionToState(HelicopterState.Idle);
            yield break;
        }

        yield return StartCoroutine(PerformMovement(activeTargetWaypoint));
        TransitionToState(HelicopterState.WaitingAtWaypoint);
    }

    private IEnumerator PerformLanding(Transform target, float duration)
    {
        Vector3 initialPosition = transform.position;
        float elapsedTime = 0f;
        float effectiveDuration = Mathf.Max(0.1f, duration);

        while (elapsedTime < effectiveDuration)
        {
            float normalizedTime = elapsedTime / effectiveDuration;
            float smoothedTime = Mathf.SmoothStep(0f, 1f, normalizedTime);
            
            Vector3 directionToTarget = target.position - transform.position;
            directionToTarget.y = 0f;
            
            if (directionToTarget.sqrMagnitude > 0.001f)
            {
                Quaternion targetRotation = Quaternion.LookRotation(directionToTarget.normalized);
                transform.rotation = Quaternion.Slerp(transform.rotation, targetRotation, rotationSpeed * Time.deltaTime);
            }
            
            transform.position = Vector3.Lerp(initialPosition, target.position, smoothedTime);
            elapsedTime += Time.deltaTime;
            yield return null;
        }
        transform.position = target.position;
    }

    protected virtual IEnumerator Execute_LandingState()
    {
        if (activeTargetWaypoint == null)
        {
            Debug.LogError("Landing state: Target waypoint is null.", this);
            TransitionToState(HelicopterState.Idle);
            yield break;
        }
        
        yield return StartCoroutine(PerformLanding(activeTargetWaypoint, pathSpecificLandingTime));
        Debug.Log("Helicopter has landed.", this);
        TransitionToState(HelicopterState.PathComplete);
    }

    private bool HasMoreSegments()
    {
        return currentWaypointIndex + 1 < currentPathWaypoints.Count;
    }

    protected virtual IEnumerator Execute_WaitingAtWaypointState()
    {
        Debug.Log($"Waiting at waypoint: {currentPathWaypoints[currentWaypointIndex + 1].name}", this);
        yield return new WaitForSeconds(waitTimeAtWaypoint);

        currentWaypointIndex++;

        if (HasMoreSegments())
        {
            activeTargetWaypoint = currentPathWaypoints[currentWaypointIndex + 1];
            TransitionToState(HelicopterState.Rotating);
        }
        else
        {
            Debug.LogWarning("Waiting state finished, but no more segments. Path might be misconfigured or landing logic was skipped.", this);
            TransitionToState(HelicopterState.PathComplete);
        }
    }
}
