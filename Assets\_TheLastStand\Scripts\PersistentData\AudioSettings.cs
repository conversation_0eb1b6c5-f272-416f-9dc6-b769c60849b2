using UnityEngine;
using System.Collections;

public class AudioSettings : MonoBehaviour
{
    public static AudioSettings Instance { get; private set; }

    public float masterVolume = 100f;
    public float musicVolume = 40f;
    public float soundFxVolume = 100f;
    public float dialogueVolume = 100f;
    public float enemyVolume = 100f;
    public float ambienceVolume = 75f;

    private const string MasterVolumeKey = "MasterVolume";
    private const string MusicVolumeKey = "MusicVolume";
    private const string SoundFxVolumeKey = "SoundFxVolume";
    private const string DialogueVolumeKey = "DialogueVolume";
    private const string EnemyVolumeKey = "EnemyVolume";
    private const string AmbienceVolumeKey = "AmbienceVolume";

    private void Awake()
    {
        bool IsFirstInstance = Instance == null;
        if (IsFirstInstance)
        {
            Instance = this;
            
            bool HasParent = transform.parent != null;
            if (HasParent)
            {
                transform.SetParent(null);
            }
            
            DontDestroyOnLoad(gameObject);
            LoadSettings();
        }
        else
        {
            Destroy(gameObject);
        }
    }

    public void SetMasterVolume(float volume)
    {
        masterVolume = Mathf.Clamp(volume, 0f, 100f);
        SaveSettings();
    }

    public void SetMusicVolume(float volume)
    {
        musicVolume = Mathf.Clamp(volume, 0f, 100f);
        SaveSettings();
    }

    public void SetSoundFxVolume(float volume)
    {
        soundFxVolume = Mathf.Clamp(volume, 0f, 100f);
        SaveSettings();
    }

    public void SetDialogueVolume(float volume)
    {
        dialogueVolume = Mathf.Clamp(volume, 0f, 100f);
        SaveSettings();
    }

    public void SetEnemyVolume(float volume)
    {
        enemyVolume = Mathf.Clamp(volume, 0f, 100f);
        SaveSettings();
    }

    public void SetAmbienceVolume(float volume) // Added SetAmbienceVolume
    {
        Debug.Log($"AudioSettings: SetAmbienceVolume received slider value: {volume}. Current ambienceVolume before clamp: {this.ambienceVolume}");
        ambienceVolume = Mathf.Clamp(volume, 0f, 100f);
        Debug.Log($"AudioSettings: ambienceVolume after clamp: {this.ambienceVolume}");
        SaveSettings();
    }

    public void SaveSettings()
    {
        PlayerPrefs.SetFloat(MasterVolumeKey, masterVolume);
        PlayerPrefs.SetFloat(MusicVolumeKey, musicVolume);
        PlayerPrefs.SetFloat(SoundFxVolumeKey, soundFxVolume);
        PlayerPrefs.SetFloat(DialogueVolumeKey, dialogueVolume);
        PlayerPrefs.SetFloat(EnemyVolumeKey, enemyVolume);
        PlayerPrefs.SetFloat(AmbienceVolumeKey, ambienceVolume); // Save Ambience Volume
        PlayerPrefs.Save();
        Debug.Log("AudioSettings: Settings Saved.");
    }

    public void LoadSettings()
    {
        masterVolume = PlayerPrefs.GetFloat(MasterVolumeKey, 100f);
        musicVolume = PlayerPrefs.GetFloat(MusicVolumeKey, 100f);
        soundFxVolume = PlayerPrefs.GetFloat(SoundFxVolumeKey, 100f);
        dialogueVolume = PlayerPrefs.GetFloat(DialogueVolumeKey, 100f);
        enemyVolume = PlayerPrefs.GetFloat(EnemyVolumeKey, 100f);
        ambienceVolume = PlayerPrefs.GetFloat(AmbienceVolumeKey, 75f); // Load Ambience Volume
        
        Debug.Log($"AudioSettings: Loaded volumes - Master: {masterVolume}, Music: {musicVolume}, SFX: {soundFxVolume}, Dialogue: {dialogueVolume}, Enemy: {enemyVolume}, Ambience: {ambienceVolume}");
        
        // Fix for master volume being 0 or negative (causes no audio)
        if (masterVolume <= 0f)
        {
            Debug.LogWarning($"AudioSettings: Master volume was {masterVolume}, resetting to 100f");
            masterVolume = 100f;
            SaveSettings(); // Save the corrected value
        }
    }

    // Optional: Call SaveSettings() when the application quits to ensure data is saved.
    private void OnApplicationQuit()
    {
        SaveSettings();
    }

    public void FadeOutSound(AudioSource audioSource, float fadeTime)
    {
        StartCoroutine(FadeOutCoroutine(audioSource, fadeTime));
    }

    private IEnumerator FadeOutCoroutine(AudioSource audioSource, float fadeTime)
    {
        float startVolume = audioSource.volume;
        while (audioSource.volume > 0)
        {
            audioSource.volume -= startVolume * Time.deltaTime / fadeTime;
            yield return null;
        }
        audioSource.Stop();
    }
    
}
